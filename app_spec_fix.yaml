alerts:
- rule: DEPLOYMENT_FAILED
- rule: DOMAIN_FAILED
features:
- buildpack-stack=ubuntu-22
ingress:
  rules:
  - component:
      name: goali-backend2
    match:
      path:
        prefix: /api
  - component:
      name: goali-backend2
    match:
      path:
        prefix: /admin
  - component:
      name: goali-backend2
    match:
      path:
        prefix: /health
  - component:
      name: goali-backend2
    match:
      path:
        prefix: /static
  - component:
      name: goali-backend2
    match:
      path:
        prefix: /ws
  - component:
      name: goali-frontend
    match:
      path:
        prefix: /
name: monkfish-app
region: lon
services:
- build_command: python manage.py migrate --noinput
  environment_slug: python
  envs:
  - key: DJANGO_SETTINGS_MODULE
    scope: RUN_AND_BUILD_TIME
    value: config.settings.prod
  - key: DJANGO_SECRET_KEY
    scope: RUN_AND_BUILD_TIME
    value: tDTGVJKlzGke7RQydZd6OJvVzVibKzp5bLf3ZPqjsk7s6LyF2PxeYwDk084XPvDW
  - key: STATIC_ROOT
    scope: RUN_AND_BUILD_TIME
    value: /app/staticfiles
  - key: DISABLE_COLLECTSTATIC
    scope: RUN_AND_BUILD_TIME
    value: "1"
  - key: DJANGO_ALLOWED_HOSTS
    scope: RUN_AND_BUILD_TIME
    value: 127.0.0.1,localhost,monkfish-app-jvgae.ondigitalocean.app
  - key: DATABASE_URL
    scope: RUN_AND_BUILD_TIME
    value: postgresql://doadmin:<EMAIL>:25060/defaultdb?sslmode=require
  - key: MISTRAL_API_KEY
    scope: RUN_AND_BUILD_TIME
    value: j43C8qMj0UsVHRlVVmURHATwEvK7b7Zu
  - key: REDIS_URL
    scope: RUN_TIME
    value: redis://localhost:6379/0
  github:
    branch: gguine/prod-slow
    deploy_on_push: true
    repo: elgui/goali
  http_port: 8080
  instance_count: 2
  instance_size_slug: apps-s-1vcpu-1gb
  name: goali-backend2
  run_command: gunicorn --worker-tmp-dir /dev/shm --bind 0.0.0.0:8080 config.wsgi:application
  source_dir: backend
static_sites:
- build_command: npm ci && npm run build:prod
  github:
    branch: gguine/prod-slow
    deploy_on_push: true
    repo: elgui/goali
  name: goali-frontend
  output_dir: /dist
  source_dir: frontend
