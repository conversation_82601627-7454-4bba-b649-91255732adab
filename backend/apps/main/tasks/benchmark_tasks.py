"""
Benchmark Tasks

This module defines Celery tasks for running benchmarks, including both agent benchmarks
and workflow benchmarks.
"""

import logging
import uuid
from typing import Dict, Any, Optional

from celery import shared_task
from asgiref.sync import async_to_sync

from apps.main.models import BenchmarkScenario
from apps.main.services.benchmark_manager import AgentBenchmarker

# Import workflow benchmark managers
from apps.main.services.async_workflow_manager import WorkflowBenchmarker
from apps.main.services.wheel_workflow_benchmark_manager import WheelWorkflowBenchmarkManager

logger = logging.getLogger(__name__)

@shared_task(bind=True, name='apps.main.tasks.benchmark_tasks.run_all_benchmarks_task')
def run_all_benchmarks_task(self, params):
    """
    Celery task to run all active benchmark scenarios with given parameters.
    """
    logger.info(f"Starting run_all_benchmarks_task with params: {params}")
    manager = AgentBenchmarker()
    total_scenarios = 0
    completed_count = 0
    failed_count = 0
    failed_scenarios = []

    try:
        # Fetch active scenarios synchronously within the task
        # Use sync_to_async if BenchmarkScenario.objects.filter needs it,
        # but standard Django ORM calls are often sync-safe in Celery tasks.
        # Let's assume it's safe for now. If issues arise, wrap in sync_to_async.
        active_scenarios = BenchmarkScenario.objects.filter(is_active=True).order_by('name')
        total_scenarios = active_scenarios.count()
        logger.info(f"Found {total_scenarios} active scenarios to benchmark.")

        for i, scenario in enumerate(active_scenarios):
            scenario_id = scenario.id
            scenario_name = scenario.name
            logger.info(f"Running benchmark for scenario {i+1}/{total_scenarios}: '{scenario_name}' (ID: {scenario_id})")

            # Update task state for progress tracking (optional but good practice)
            self.update_state(state='PROGRESS', meta={
                'current': i + 1,
                'total': total_scenarios,
                'status': f"Running scenario: {scenario_name}"
            })

            try:
                # Run the benchmark for the individual scenario using the manager
                # The manager's run_benchmark method is async, so we need async_to_sync
                benchmark_run = async_to_sync(manager.run_benchmark)(scenario_id, params)
                logger.info(f"Completed benchmark for scenario '{scenario_name}'. Run ID: {benchmark_run.id}")
                completed_count += 1
            except Exception as e:
                logger.error(f"Error running benchmark for scenario '{scenario_name}' (ID: {scenario_id}): {e}", exc_info=True)
                failed_count += 1
                failed_scenarios.append({'id': scenario_id, 'name': scenario_name, 'error': str(e)})
                # Decide whether to continue or stop on error. Let's continue for now.

        # Task completed
        result_message = f"Finished running all benchmarks. Total: {total_scenarios}, Completed: {completed_count}, Failed: {failed_count}."
        logger.info(result_message)
        if failed_scenarios:
             logger.warning(f"Failed scenarios: {failed_scenarios}")

        return {
            'status': 'SUCCESS',
            'total_scenarios': total_scenarios,
            'completed_count': completed_count,
            'failed_count': failed_count,
            'failed_scenarios': failed_scenarios,
            'message': result_message
        }

    except Exception as e:
        logger.error(f"Critical error during run_all_benchmarks_task: {e}", exc_info=True)
        # Update state to FAILURE
        self.update_state(state='FAILURE', meta={
            'exc_type': type(e).__name__,
            'exc_message': str(e),
            'status': 'Task failed critically.'
        })
        # Reraise the exception so Celery knows it failed critically
        raise

@shared_task(bind=True, name='apps.main.tasks.benchmark_tasks.run_workflow_benchmark')
def run_workflow_benchmark(self, benchmark_id: str, params: Dict[str, Any] = None, user_profile_id: Optional[str] = None):
    """
    Run a workflow benchmark with enhanced error handling and reporting.

    Args:
        benchmark_id: ID of the benchmark scenario
        params: Optional parameters for the benchmark
        user_profile_id: Optional ID of the user initiating the benchmark

    Returns:
        dict: Benchmark results with detailed error information
    """
    logger.info(f"Starting workflow benchmark task for scenario ID: {benchmark_id}")

    # Initialize error collection
    task_errors = []
    benchmark_run = None

    def add_error(error_type, level, message, source, details=None):
        """Helper function to add errors to the collection."""
        from django.utils import timezone
        error_entry = {
            'type': error_type,
            'level': level,
            'message': message,
            'source': source,
            'timestamp': timezone.now().isoformat(),
            'details': details or {}
        }
        task_errors.append(error_entry)
        logger.warning(f"Task error captured: {error_type} - {message}")

    try:
        # Handle Mock objects in test environments
        from unittest.mock import MagicMock
        if isinstance(benchmark_id, MagicMock):
            add_error('critical', 'error', f'Invalid benchmark ID format: Mock object passed instead of real ID', 'run_workflow_benchmark', {'benchmark_id_type': str(type(benchmark_id))})
            raise ValueError(f"Invalid benchmark ID format: Mock object passed instead of real ID")

        # Convert string ID to appropriate format (integer or UUID)
        try:
            # Try to parse as integer first (for regular Django IDs)
            scenario_id = int(benchmark_id)
        except ValueError:
            # If not an integer, try UUID format for backward compatibility
            try:
                scenario_id = uuid.UUID(benchmark_id)
            except ValueError as e:
                add_error('critical', 'error', f'Invalid benchmark ID format: {benchmark_id}. Must be an integer or UUID.', 'run_workflow_benchmark', {'original_error': str(e)})
                raise ValueError(f"Invalid benchmark ID format: {benchmark_id}. Must be an integer or UUID.")

        # Get the scenario to determine workflow type
        try:
            scenario = BenchmarkScenario.objects.get(id=scenario_id)
        except BenchmarkScenario.DoesNotExist as e:
            add_error('critical', 'error', f'Benchmark scenario not found: {benchmark_id}', 'run_workflow_benchmark', {'scenario_id': str(scenario_id)})
            raise ValueError(f"Benchmark scenario not found: {benchmark_id}")

        workflow_type = scenario.metadata.get('workflow_type')
        if not workflow_type:
            add_error('critical', 'error', f'Scenario {scenario.name} is not configured for workflow benchmarking (missing workflow_type)', 'run_workflow_benchmark', {'scenario_name': scenario.name})
            raise ValueError(f"Scenario {scenario.name} is not configured for workflow benchmarking (missing workflow_type)")

        # Select the appropriate workflow benchmark manager
        if workflow_type == 'wheel_generation':
            benchmark_manager = WheelWorkflowBenchmarkManager()
        else:
            add_error('critical', 'error', f'Unsupported workflow type: {workflow_type}', 'run_workflow_benchmark', {'workflow_type': workflow_type})
            raise ValueError(f"Unsupported workflow type: {workflow_type}")

        # Enhanced progress callback that captures errors
        def enhanced_progress_callback(state, meta=None):
            """Enhanced progress callback that captures errors from workflow execution."""
            if meta and isinstance(meta, dict):
                # Check for errors in the meta information
                if 'error' in meta:
                    add_error('warning', 'warning', meta['error'], 'workflow_execution', meta)
                elif 'errors' in meta and isinstance(meta['errors'], list):
                    for error in meta['errors']:
                        if isinstance(error, dict):
                            add_error(
                                error.get('type', 'warning'),
                                error.get('level', 'warning'),
                                error.get('message', 'Unknown error'),
                                error.get('source', 'workflow_execution'),
                                error.get('details', {})
                            )

            # Call the original update_state with proper error handling
            try:
                self.update_state(state=state, meta=meta)
            except Exception as update_error:
                # If update_state fails, log it but don't fail the whole task
                logger.warning(f"Failed to update task state in progress callback: {update_error}")
                add_error('warning', 'warning', f'Failed to update task state: {str(update_error)}', 'progress_callback', {'state': state, 'meta': meta})

        # Extract user_profile_id from params if not provided as separate parameter
        if user_profile_id is None and params and 'user_profile_id' in params:
            user_profile_id = params['user_profile_id']
            logger.info(f"Extracted user_profile_id from params: {user_profile_id}")

        # Run the benchmark with enhanced progress tracking
        try:
            benchmark_run = async_to_sync(benchmark_manager.execute_benchmark_with_scenario)(
                scenario=scenario,
                params=params,
                progress_callback=enhanced_progress_callback,
                user_profile_id=user_profile_id
            )
        except Exception as e:
            import traceback
            add_error('critical', 'error', f'Workflow execution failed: {str(e)}', 'benchmark_manager', {
                'error_type': type(e).__name__,
                'traceback': traceback.format_exc(),
                'scenario_name': scenario.name,
                'workflow_type': workflow_type
            })

            # Broadcast error to UI immediately
            try:
                from apps.main.services.event_service import EventService

                # Create user-friendly error message
                user_message = f"Workflow benchmark failed for '{scenario.name}'"
                if "'list' object has no attribute 'get'" in str(e):
                    user_message = f"CRITICAL: Workflow benchmark failed due to parameter type mismatch - a dictionary parameter was passed as a list. This indicates a data flow issue in the benchmark execution."
                    logger.error(f"CRITICAL PARAMETER TYPE ERROR DETECTED: {str(e)}. Scenario: {scenario.name}, Workflow Type: {workflow_type}")
                elif 'tool_call_details' in str(e):
                    user_message = f"Workflow benchmark failed: Missing tool call details field"
                elif 'null value in column' in str(e):
                    user_message = f"Workflow benchmark failed: Database constraint violation"
                elif 'IntegrityError' in str(e):
                    user_message = f"Workflow benchmark failed: Database integrity error"

                # Use sync version of EventService for Celery task
                EventService.emit_event_sync(
                    event_type='debug_info',
                    data={
                        'type': 'debug_info',
                        'level': 'error',
                        'message': user_message,
                        'source': 'run_workflow_benchmark',
                        'details': {
                            'error_type': type(e).__name__,
                            'error_message': str(e),
                            'scenario_name': scenario.name,
                            'workflow_type': workflow_type,
                            'benchmark_id': benchmark_id,
                            'traceback': traceback.format_exc()
                        }
                    },
                    target_groups=['benchmark_dashboard']  # Target the benchmark dashboard WebSocket
                )
            except Exception as event_err:
                logger.warning(f"Failed to broadcast error via EventService: {event_err}")

            raise

        # If we have errors, integrate them into the benchmark run
        if task_errors and benchmark_run:
            try:
                # Get existing raw_results or create new structure
                raw_results = benchmark_run.raw_results or {}

                # Add task errors to existing errors
                existing_errors = raw_results.get('errors', [])
                all_errors = existing_errors + task_errors

                # Update error summary
                critical_count = sum(1 for e in all_errors if e.get('type') == 'critical' or e.get('level') == 'error')
                warning_count = sum(1 for e in all_errors if e.get('type') == 'warning' or e.get('level') == 'warning')
                info_count = len(all_errors) - critical_count - warning_count

                raw_results.update({
                    'errors': all_errors,
                    'error_summary': {
                        'total_errors': len(all_errors),
                        'critical_errors': critical_count,
                        'warnings': warning_count,
                        'info_messages': info_count,
                        'has_critical_errors': critical_count > 0
                    }
                })

                # Save the updated benchmark run
                benchmark_run.raw_results = raw_results
                benchmark_run.save(update_fields=['raw_results'])

                logger.info(f"Integrated {len(task_errors)} task errors into benchmark run {benchmark_run.id}")

            except Exception as e:
                logger.error(f"Failed to integrate errors into benchmark run: {e}", exc_info=True)
                # Don't fail the task for this, just log it

        # Get final error information from the benchmark run
        final_errors = []
        final_error_summary = {}

        if benchmark_run and benchmark_run.raw_results:
            raw_results = benchmark_run.raw_results
            final_errors = raw_results.get('errors', [])
            final_error_summary = raw_results.get('error_summary', {})

        # Fallback to task errors if no errors in benchmark run
        if not final_errors and task_errors:
            final_errors = task_errors
            critical_count = sum(1 for e in task_errors if e.get('type') == 'critical' or e.get('level') == 'error')
            warning_count = sum(1 for e in task_errors if e.get('type') == 'warning' or e.get('level') == 'warning')
            info_count = len(task_errors) - critical_count - warning_count

            final_error_summary = {
                'total_errors': len(task_errors),
                'critical_errors': critical_count,
                'warnings': warning_count,
                'info_messages': info_count
            }

        # Return the result with comprehensive error information
        result = {
            'benchmark_run_id': str(benchmark_run.id),
            'scenario_name': benchmark_run.scenario.name,
            'workflow_type': workflow_type,
            'mean_duration': benchmark_run.mean_duration,
            'success_rate': benchmark_run.success_rate,
            'status': 'completed',
            'has_errors': len(final_errors) > 0,
            'error_count': len(final_errors),
            'errors': final_errors,  # Include the actual errors
            'error_summary': final_error_summary  # Include error summary
        }

        # Add critical error flag
        if final_errors:
            result['has_critical_errors'] = final_error_summary.get('critical_errors', 0) > 0
            result['critical_error_count'] = final_error_summary.get('critical_errors', 0)

        return result

    except Exception as e:
        import traceback
        logger.error(f"Error running workflow benchmark: {str(e)}", exc_info=True)

        # Add the final critical error
        add_error('critical', 'error', f'Task execution failed: {str(e)}', 'run_workflow_benchmark', {
            'error_type': type(e).__name__,
            'traceback': traceback.format_exc()
        })

        # Broadcast final error to UI
        try:
            from apps.main.services.event_service import EventService

            # Create user-friendly error message
            user_message = f"Workflow benchmark task failed"
            if "'list' object has no attribute 'get'" in str(e):
                user_message = f"CRITICAL: Workflow benchmark task failed due to parameter type mismatch - a dictionary parameter was passed as a list. This indicates a data flow issue in the benchmark execution."
                logger.error(f"CRITICAL PARAMETER TYPE ERROR DETECTED IN FINAL HANDLER: {str(e)}. Scenario: {scenario_id}, User Profile ID: {user_profile_id}")
            elif 'user_profile_id' in str(e).lower():
                user_message = f"Workflow benchmark task failed: User profile ID issue - {str(e)}"
                logger.error(f"USER_PROFILE_ID ERROR DETECTED: {str(e)}. Scenario: {scenario_id}, User Profile ID: {user_profile_id}")
            elif 'tool_call_details' in str(e):
                user_message = f"Workflow benchmark task failed: Missing tool call details field"
            elif 'null value in column' in str(e):
                user_message = f"Workflow benchmark task failed: Database constraint violation"
            elif 'IntegrityError' in str(e):
                user_message = f"Workflow benchmark task failed: Database integrity error"

            # Use sync version of EventService for Celery task
            EventService.emit_event_sync(
                event_type='debug_info',
                data={
                    'type': 'debug_info',
                    'level': 'error',
                    'message': user_message,
                    'source': 'run_workflow_benchmark_final',
                    'details': {
                        'error_type': type(e).__name__,
                        'error_message': str(e),
                        'benchmark_id': benchmark_id,
                        'task_errors': task_errors,
                        'traceback': traceback.format_exc()
                    }
                },
                target_groups=['benchmark_dashboard']  # Target the benchmark dashboard WebSocket
            )
        except Exception as event_err:
            logger.warning(f"Failed to broadcast final error via EventService: {event_err}")

        # Update state to FAILURE with detailed error information
        try:
            self.update_state(state='FAILURE', meta={
                'exc_type': type(e).__name__,
                'exc_message': str(e),
                'status': 'Task failed.',
                'errors': task_errors,
                'error_count': len(task_errors)
            })
        except Exception as update_error:
            # If update_state fails (e.g., in testing), just log it
            logger.warning(f"Failed to update task state: {update_error}")

        return {
            'benchmark_id': benchmark_id,
            'status': 'failed',
            'error': str(e),
            'errors': task_errors,
            'error_count': len(task_errors)
        }

@shared_task(bind=True, name='apps.main.tasks.benchmark_tasks.run_all_workflow_benchmarks')
def run_all_workflow_benchmarks(self, params: Dict[str, Any] = None, workflow_type: Optional[str] = None):
    """
    Run all active workflow benchmark scenarios.

    Args:
        params: Optional parameters for the benchmarks
        workflow_type: Optional workflow type to filter scenarios

    Returns:
        dict: Summary of benchmark results
    """
    logger.info(f"Starting run_all_workflow_benchmarks task with params: {params}, workflow_type: {workflow_type}")

    total_scenarios = 0
    completed_count = 0
    failed_count = 0
    failed_scenarios = []

    try:
        # Build the filter for active scenarios
        filter_kwargs = {'is_active': True}

        # Add metadata__workflow_type filter if specified
        if workflow_type:
            filter_kwargs['metadata__workflow_type'] = workflow_type

        # Fetch active workflow benchmark scenarios
        active_scenarios = BenchmarkScenario.objects.filter(**filter_kwargs).order_by('name')
        total_scenarios = active_scenarios.count()
        logger.info(f"Found {total_scenarios} active workflow benchmark scenarios.")

        for i, scenario in enumerate(active_scenarios):
            scenario_id = scenario.id
            scenario_name = scenario.name
            scenario_workflow_type = scenario.metadata.get('workflow_type')

            logger.info(f"Running workflow benchmark {i+1}/{total_scenarios}: '{scenario_name}' (ID: {scenario_id}, Type: {scenario_workflow_type})")

            # Update task state for progress tracking
            self.update_state(state='PROGRESS', meta={
                'current': i + 1,
                'total': total_scenarios,
                'status': f"Running workflow benchmark: {scenario_name}"
            })

            try:
                # Run the workflow benchmark for this scenario
                # We use the run_workflow_benchmark task directly
                result = run_workflow_benchmark.apply_async(
                    args=[str(scenario_id)],
                    kwargs={'params': params}
                ).get(timeout=1800)  # 30-minute timeout

                if result.get('status') == 'completed':
                    logger.info(f"Completed workflow benchmark for scenario '{scenario_name}'. Run ID: {result.get('benchmark_run_id')}")
                    completed_count += 1
                else:
                    logger.error(f"Workflow benchmark failed for scenario '{scenario_name}': {result.get('error')}")
                    failed_count += 1
                    failed_scenarios.append({
                        'id': str(scenario_id),
                        'name': scenario_name,
                        'error': result.get('error')
                    })
            except Exception as e:
                logger.error(f"Error running workflow benchmark for scenario '{scenario_name}' (ID: {scenario_id}): {e}", exc_info=True)
                failed_count += 1
                failed_scenarios.append({
                    'id': str(scenario_id),
                    'name': scenario_name,
                    'error': str(e)
                })

        # Task completed
        result_message = f"Finished running all workflow benchmarks. Total: {total_scenarios}, Completed: {completed_count}, Failed: {failed_count}."
        logger.info(result_message)
        if failed_scenarios:
            logger.warning(f"Failed scenarios: {failed_scenarios}")

        return {
            'status': 'SUCCESS',
            'total_scenarios': total_scenarios,
            'completed_count': completed_count,
            'failed_count': failed_count,
            'failed_scenarios': failed_scenarios,
            'message': result_message
        }
    except Exception as e:
        logger.error(f"Critical error during run_all_workflow_benchmarks task: {e}", exc_info=True)
        # Update state to FAILURE
        self.update_state(state='FAILURE', meta={
            'exc_type': type(e).__name__,
            'exc_message': str(e),
            'status': 'Task failed critically.'
        })
        # Reraise the exception so Celery knows it failed critically
        raise


@shared_task(bind=True, name='apps.main.tasks.benchmark_tasks.run_template_test')
def run_template_test(self, scenario_id: str, template_data: Dict[str, Any], params: Dict[str, Any] = None):
    """
    Run a template test as a Celery task.

    Args:
        scenario_id: ID of the benchmark scenario
        template_data: Template data for evaluation
        params: Test parameters including selected combinations

    Returns:
        dict: Test results with runs data
    """
    logger.info(f"Starting template test task for scenario {scenario_id}")

    try:
        from django.shortcuts import get_object_or_404
        from apps.main.models import BenchmarkScenario
        from apps.main.services.benchmark_manager import AgentBenchmarker
        from asgiref.sync import async_to_sync
        import uuid

        # Get the scenario
        scenario = get_object_or_404(BenchmarkScenario, id=scenario_id)

        # Check if this is a workflow scenario and route to appropriate manager
        workflow_type = scenario.metadata.get('workflow_type')
        if workflow_type:
            # This is a workflow scenario, use workflow benchmark manager
            if workflow_type == 'wheel_generation':
                from apps.main.services.wheel_workflow_benchmark_manager import WheelWorkflowBenchmarkManager
                manager = WheelWorkflowBenchmarkManager()
            else:
                # For other workflow types, use the base workflow manager
                from apps.main.services.async_workflow_manager import WorkflowBenchmarker
                manager = WorkflowBenchmarker()

            # Use workflow benchmark execution path
            is_workflow_benchmark = True
        else:
            # This is an agent scenario, use agent benchmark manager
            from apps.main.services.benchmark_manager import AgentBenchmarker
            manager = AgentBenchmarker()
            is_workflow_benchmark = False

        # Extract parameters
        runs = min(params.get('runs', 1), 5)  # Limit to 5 runs for template testing
        semantic_evaluation = params.get('semantic_evaluation', True)
        context_variables = params.get('context_variables', {})
        multi_range_evaluation = params.get('multi_range_contextual_evaluation', False)
        selected_combinations = params.get('selected_combinations', [])
        selected_combination_indices = params.get('selected_combination_indices', [])

        # Extract execution mode parameters
        use_real_llm = params.get('use_real_llm', False)
        use_real_tools = params.get('use_real_tools', False)
        use_real_db = params.get('use_real_db', False)

        # CRITICAL FIX: Extract user_profile_id from params (same as run_workflow_benchmark)
        user_profile_id = params.get('user_profile_id') if params else None
        logger.info(f"Extracted user_profile_id from params: {user_profile_id}")

        test_runs = []

        # Update task state
        self.update_state(state='PROGRESS', meta={
            'current': 0,
            'total': len(selected_combinations) if multi_range_evaluation and selected_combinations else runs,
            'status': 'Starting template test...'
        })

        if multi_range_evaluation and selected_combinations:
            # Multi-range evaluation with selected combinations
            logger.info(f"Running multi-range evaluation with {len(selected_combinations)} selected combinations")

            for i, context_combo in enumerate(selected_combinations):
                # Update progress
                self.update_state(state='PROGRESS', meta={
                    'current': i + 1,
                    'total': len(selected_combinations),
                    'status': f'Testing combination {i + 1}/{len(selected_combinations)}...'
                })

                try:
                    # Create benchmark parameters with specific context combination
                    benchmark_params = {
                        'runs': 1,  # Single run per context combination
                        'warmup_runs': 0,  # No warmup for template testing
                        'semantic_evaluation': semantic_evaluation,
                        'evaluation_template_data': template_data,
                        'context_variables': context_combo,  # Use specific context combination
                        'multi_range_contextual_evaluation': True,
                        'selected_combinations': [context_combo],  # Pass only this combination
                        # Add execution mode parameters
                        'use_real_llm': use_real_llm,
                        'use_real_tools': use_real_tools,
                        'use_real_db': use_real_db
                    }

                    # Run the benchmark with the template
                    if is_workflow_benchmark:
                        # Use workflow benchmark execution - FIXED: Pass user_profile_id
                        run = async_to_sync(manager.execute_benchmark)(
                            scenario_id=scenario.id,
                            params=benchmark_params,
                            user_profile_id=user_profile_id
                        )
                    else:
                        # Use agent benchmark execution - FIXED: Pass user_profile_id
                        run = async_to_sync(manager.run_benchmark)(
                            scenario_id=scenario.id,
                            params=benchmark_params,
                            user_profile_id=user_profile_id
                        )

                    # Use the new property for robust token usage display
                    token_usage_str = run.token_usage_display

                    # Get range info for this context combination
                    range_info = context_combo.get('range_info', {})
                    range_key = next(iter(range_info.values())) if range_info else f'combination_{i+1}'

                    test_runs.append({
                        'id': str(run.id),
                        'success': run.success_rate >= 0.5,  # Consider successful if success rate >= 50%
                        'semantic_score': run.semantic_score or 0.0,
                        'execution_time': (run.mean_duration or 0.0) / 1000.0,  # Convert ms to seconds
                        'token_usage': token_usage_str,
                        'cost': float(run.estimated_cost or 0.0),
                        'error': None,  # BenchmarkRun doesn't store individual run errors
                        'context_combination': context_combo,  # Include context info for debugging
                        'range_key': range_key  # Include range identifier
                    })

                except Exception as e:
                    logger.error(f"Error running benchmark for context combination {i+1}: {e}", exc_info=True)

                    # Add detailed error information for debugging
                    error_details = {
                        'error_type': type(e).__name__,
                        'error_message': str(e),
                        'scenario_id': scenario_id,
                        'context_combination': context_combo if 'context_combo' in locals() else {},
                        'combination_index': i + 1,
                        'benchmark_params': benchmark_params if 'benchmark_params' in locals() else {}
                    }

                    # Log specific error types with helpful context
                    if 'null value in column' in str(e):
                        logger.error(f"Database constraint violation for context combination {i+1}. This usually indicates a missing required field. Error details: {error_details}")
                    elif 'IntegrityError' in str(e):
                        logger.error(f"Database integrity error for context combination {i+1}. Error details: {error_details}")
                    elif 'agent_communications' in str(e):
                        logger.error(f"Agent communications field error for context combination {i+1}. This field is required for all benchmark runs. Error details: {error_details}")
                    else:
                        logger.error(f"Unexpected error for context combination {i+1}. Error details: {error_details}")

                    # Broadcast error to WebSocket for immediate UI feedback
                    try:
                        from apps.main.services.event_service import EventService

                        # Create user-friendly error message
                        user_message = f"Context combination {i+1} failed"
                        if 'agent_communications' in str(e):
                            user_message = f"Context combination {i+1} failed: Missing required field 'agent_communications'"
                        elif 'null value in column' in str(e):
                            user_message = f"Context combination {i+1} failed: Database constraint violation"
                        elif 'IntegrityError' in str(e):
                            user_message = f"Context combination {i+1} failed: Database integrity error"

                        # Use sync version of EventService for Celery task
                        EventService.emit_event_sync(
                            event_type='debug_info',
                            data={
                                'type': 'debug_info',
                                'level': 'error',
                                'message': user_message,
                                'source': 'run_template_test',
                                'details': error_details
                            },
                            target_groups=['benchmark_dashboard']  # Target the benchmark dashboard WebSocket
                        )
                    except Exception as event_err:
                        logger.warning(f"Failed to broadcast error via EventService: {event_err}")

                    test_runs.append({
                        'id': str(uuid.uuid4()),
                        'success': False,
                        'semantic_score': 0.0,
                        'execution_time': 0.0,
                        'token_usage': "0",
                        'cost': 0.0,
                        'error': str(e),
                        'error_type': type(e).__name__,
                        'context_combination': context_combo if 'context_combo' in locals() else {},
                        'range_key': f'combination_{i+1}_error'
                    })
        else:
            # Standard evaluation: run the benchmark multiple times with the same context
            logger.info(f"Running standard evaluation with {runs} runs")

            for i in range(runs):
                # Update progress
                self.update_state(state='PROGRESS', meta={
                    'current': i + 1,
                    'total': runs,
                    'status': f'Running test {i + 1}/{runs}...'
                })

                try:
                    # Create benchmark parameters with template data
                    benchmark_params = {
                        'runs': 1,  # Single run per iteration
                        'warmup_runs': 0,  # No warmup for template testing
                        'semantic_evaluation': semantic_evaluation,
                        'evaluation_template_data': template_data,
                        'context_variables': context_variables,
                        'multi_range_contextual_evaluation': False,  # Explicitly set to False for standard evaluation
                        # Add execution mode parameters
                        'use_real_llm': use_real_llm,
                        'use_real_tools': use_real_tools,
                        'use_real_db': use_real_db
                    }

                    # Run the benchmark with the template
                    if is_workflow_benchmark:
                        # Use workflow benchmark execution - FIXED: Pass user_profile_id
                        run = async_to_sync(manager.execute_benchmark)(
                            scenario_id=scenario.id,
                            params=benchmark_params,
                            user_profile_id=user_profile_id
                        )
                    else:
                        # Use agent benchmark execution - FIXED: Pass user_profile_id
                        run = async_to_sync(manager.run_benchmark)(
                            scenario_id=scenario.id,
                            params=benchmark_params,
                            user_profile_id=user_profile_id
                        )

                    # Use the new property for robust token usage display
                    token_usage_str = run.token_usage_display

                    test_runs.append({
                        'id': str(run.id),
                        'success': run.success_rate >= 0.5,  # Consider successful if success rate >= 50%
                        'semantic_score': run.semantic_score or 0.0,
                        'execution_time': (run.mean_duration or 0.0) / 1000.0,  # Convert ms to seconds
                        'token_usage': token_usage_str,
                        'cost': float(run.estimated_cost or 0.0),
                        'error': None,  # BenchmarkRun doesn't store individual run errors
                        'run_number': i + 1  # Include run number for standard evaluation
                    })

                except Exception as e:
                    logger.error(f"Error running benchmark run {i+1}: {e}", exc_info=True)

                    # Add detailed error information for debugging
                    error_details = {
                        'error_type': type(e).__name__,
                        'error_message': str(e),
                        'scenario_id': scenario_id,
                        'run_number': i + 1,
                        'benchmark_params': benchmark_params
                    }

                    # Log specific error types with helpful context
                    if 'null value in column' in str(e):
                        logger.error(f"Database constraint violation in benchmark run {i+1}. This usually indicates a missing required field. Error details: {error_details}")
                    elif 'IntegrityError' in str(e):
                        logger.error(f"Database integrity error in benchmark run {i+1}. Error details: {error_details}")
                    elif 'agent_communications' in str(e):
                        logger.error(f"Agent communications field error in benchmark run {i+1}. This field is required for all benchmark runs. Error details: {error_details}")
                    else:
                        logger.error(f"Unexpected error in benchmark run {i+1}. Error details: {error_details}")

                    # Broadcast error to WebSocket for immediate UI feedback
                    try:
                        from apps.main.services.event_service import EventService

                        # Create user-friendly error message
                        user_message = f"Benchmark run {i+1} failed"
                        if 'agent_communications' in str(e):
                            user_message = f"Benchmark run {i+1} failed: Missing required field 'agent_communications'"
                        elif 'null value in column' in str(e):
                            user_message = f"Benchmark run {i+1} failed: Database constraint violation"
                        elif 'IntegrityError' in str(e):
                            user_message = f"Benchmark run {i+1} failed: Database integrity error"

                        # Use sync version of EventService for Celery task
                        EventService.emit_event_sync(
                            event_type='debug_info',
                            data={
                                'type': 'debug_info',
                                'level': 'error',
                                'message': user_message,
                                'source': 'run_template_test',
                                'details': error_details
                            },
                            target_groups=['benchmark_dashboard']  # Target the benchmark dashboard WebSocket
                        )
                    except Exception as event_err:
                        logger.warning(f"Failed to broadcast error via EventService: {event_err}")

                    test_runs.append({
                        'id': str(uuid.uuid4()),
                        'success': False,
                        'semantic_score': 0.0,
                        'execution_time': 0.0,
                        'token_usage': "0",
                        'cost': 0.0,
                        'error': str(e),
                        'error_type': type(e).__name__,
                        'run_number': i + 1
                    })

        # Task completed successfully
        self.update_state(state='SUCCESS', meta={
            'current': len(test_runs),
            'total': len(test_runs),
            'status': 'Template test completed successfully'
        })

        return {
            'success': True,
            'runs': test_runs,
            'template_name': template_data.get('name', 'Unnamed Template'),
            'scenario_name': scenario.name,
            'message': f"Template test completed with {len(test_runs)} runs."
        }

    except Exception as e:
        logger.error(f"Error in template test task: {e}", exc_info=True)

        # Update task state to FAILURE
        self.update_state(state='FAILURE', meta={
            'exc_type': type(e).__name__,
            'exc_message': str(e),
            'status': 'Template test failed'
        })

        # Return error result
        return {
            'success': False,
            'error': str(e),
            'message': f"Template test failed: {str(e)}"
        }