from django.urls import path
from . import views
from . import api_views
from .views import activity_colors

urlpatterns = [
    # Game interface
    path('game_interface/', views.game_interface, name='game_interface'),

    # Health check endpoint
    path('api/health/', api_views.HealthCheckView.as_view(), name='health_check_api'),

    # Debug API endpoints (only available in debug mode)
    path('api/debug/users/', api_views.DebugUsersView.as_view(), name='debug_users_api'),
    path('api/debug/users/<str:user_id>/', api_views.DebugUserDetailView.as_view(), name='debug_user_detail_api'),
    path('api/debug/llm-configs/', api_views.DebugLLMConfigsView.as_view(), name='debug_llm_configs_api'),

    # Authentication API endpoints
    path('api/auth/login/', api_views.AuthLoginView.as_view(), name='auth_login_api'),
    path('api/auth/verify/', api_views.AuthVerifyView.as_view(), name='auth_verify_api'),
    path('api/auth/logout/', api_views.AuthLogoutView.as_view(), name='auth_logout_api'),

    # Beta signup endpoint
    path('api/auth/beta-signup/', api_views.BetaSignupView.as_view(), name='beta_signup_api'),

    # Activity catalog endpoint
    path('api/activities/catalog/', api_views.ActivityCatalogView.as_view(), name='activity_catalog_api'),

    # Activity creation and tailoring endpoints
    path('api/activities/create/', api_views.CreateActivityView.as_view(), name='create_activity_api'),
    path('api/activities/tailor/', api_views.TailorActivityView.as_view(), name='tailor_activity_api'),

    # User feedback endpoint
    path('api/feedback/', api_views.UserFeedbackView.as_view(), name='user_feedback_api'),

    # Wheel item management endpoints
    path('api/wheel-items/', api_views.WheelItemManagementView.as_view(), name='wheel_item_add_api'),
    path('api/wheel-items/<str:wheel_item_id>/', api_views.WheelItemManagementView.as_view(), name='wheel_item_remove_api'),

    # User profile endpoint
    path('api/user/profile/', api_views.UserProfileDetailView.as_view(), name='user_profile_api'),
    path('api/user/profile/<int:user_id>/', api_views.UserProfileDetailView.as_view(), name='user_profile_detail_api'),

    # Logging endpoint
    path('logs/', api_views.LoggingView.as_view(), name='logging_api'),

    # Activity color configuration endpoints
    path('api/activity-colors/config/', activity_colors.get_activity_color_config, name='activity_color_config'),
    path('api/activity-colors/domain/<str:domain>/', activity_colors.get_activity_color_by_domain, name='activity_color_by_domain'),
]