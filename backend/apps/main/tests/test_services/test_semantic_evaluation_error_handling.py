"""
Tests for error handling in semantic evaluation.

This module contains tests for error handling in the SemanticEvaluator class.
"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch

from apps.main.services.semantic_evaluator import SemanticEvaluator
from apps.main.llm.response import LL<PERSON><PERSON>ponse, ResponseType
from apps.main.tests.workflow_utils import create_test_workflow_scenario_async


@pytest.fixture
def semantic_evaluator():
    """Fixture to provide a SemanticEvaluator instance."""
    return SemanticEvaluator()


@pytest.fixture
async def workflow_scenario(db):
    """Fixture to provide a workflow benchmark scenario."""
    return await create_test_workflow_scenario_async(
        workflow_type="test_workflow",
        input_data={
            "user_profile_id": "test-user-123",
            "context_packet": {
                "task_type": "test_workflow",
                "user_message": "Test message"
            }
        },
        metadata={
            "workflow_type": "test_workflow",
            "expected_quality_criteria": {
                "Clarity": ["Is the response clear?"],
                "Helpfulness": ["Is the response helpful?"]
            },
            "evaluator_models": ["test-model"]
        }
    )


@pytest.mark.asyncio
async def test_evaluate_response_with_missing_criteria(semantic_evaluator, workflow_scenario):
    """Test that evaluate_response handles missing criteria gracefully by falling back to tone analysis."""
    # Create a scenario context and agent response
    scenario_context = "This is a test scenario context."
    agent_response = "This is a test agent response."

    # Call evaluate_response with empty criteria
    result = await semantic_evaluator.evaluate_response(
        scenario_context=scenario_context,
        agent_response=agent_response,
        criteria={},
        evaluator_models=["test-model"]
    )

    # Verify that the result handles the edge case gracefully
    # When criteria is empty, the LLM might return invalid JSON, which is handled gracefully
    assert result is not None
    assert "_meta" in result
    assert "test-model" in result

    # The system should handle this gracefully - either succeed or fail gracefully
    if result["test-model"].get("error"):
        # If there's an error, it should be a parsing error (which is expected with empty criteria)
        assert "error_message" in result["test-model"]
        assert "overall_score" in result["test-model"]
        assert result["test-model"]["overall_score"] == 0.0  # Default score for errors
    else:
        # If successful, should have valid score
        assert "overall_score" in result["test-model"]
        assert isinstance(result["test-model"]["overall_score"], (int, float))


@pytest.mark.asyncio
async def test_evaluate_response_with_invalid_criteria(semantic_evaluator, workflow_scenario):
    """Test that evaluate_response handles invalid criteria gracefully by falling back to tone analysis."""
    # Create a scenario context and agent response
    scenario_context = "This is a test scenario context."
    agent_response = "This is a test agent response."

    # Call evaluate_response with invalid criteria (not a dict of lists)
    result = await semantic_evaluator.evaluate_response(
        scenario_context=scenario_context,
        agent_response=agent_response,
        criteria={"Clarity": "This is not a list"},
        evaluator_models=["test-model"]
    )

    # Verify that the result handles invalid criteria gracefully
    # The system should detect invalid criteria structure and fall back gracefully
    assert result is not None
    assert "_meta" in result
    assert "test-model" in result

    # The system should handle this gracefully - either succeed or fail gracefully
    if result["test-model"].get("error"):
        # If there's an error, it should be handled gracefully
        assert "error_message" in result["test-model"]
        assert "overall_score" in result["test-model"]
        assert result["test-model"]["overall_score"] == 0.0  # Default score for errors
    else:
        # If successful, should have valid score
        assert "overall_score" in result["test-model"]
        assert isinstance(result["test-model"]["overall_score"], (int, float))


@pytest.mark.asyncio
async def test_evaluate_response_with_missing_agent_response(semantic_evaluator, workflow_scenario):
    """Test that evaluate_response handles missing agent response gracefully."""
    # Create a scenario context
    scenario_context = "This is a test scenario context."

    # Call evaluate_response with empty agent response
    result = await semantic_evaluator.evaluate_response(
        scenario_context=scenario_context,
        agent_response="",
        criteria={"Clarity": ["Is the response clear?"]},
        evaluator_models=["test-model"]
    )

    # Verify that the result handles empty agent response gracefully
    # The LLM can still provide meaningful evaluation of an empty response
    assert result is not None
    assert "_meta" in result
    assert "test-model" in result

    # The system should handle this gracefully - either succeed or fail gracefully
    if result["test-model"].get("error"):
        # If there's an error, it should be handled gracefully
        assert "error_message" in result["test-model"]
        assert "overall_score" in result["test-model"]
        assert result["test-model"]["overall_score"] == 0.0  # Default score for errors
    else:
        # If successful, should have valid score
        assert "overall_score" in result["test-model"]
        assert isinstance(result["test-model"]["overall_score"], (int, float))


@pytest.mark.asyncio
async def test_evaluate_response_with_llm_client_error(semantic_evaluator, workflow_scenario):
    """Test that evaluate_response handles LLM client errors gracefully."""
    # Create a scenario context and agent response
    scenario_context = "This is a test scenario context."
    agent_response = "This is a test agent response."
    
    # Mock the _get_llm_client method to raise an exception
    with patch.object(semantic_evaluator, '_get_llm_client', side_effect=Exception("LLM client error")):
        # Call evaluate_response
        result = await semantic_evaluator.evaluate_response(
            scenario_context=scenario_context,
            agent_response=agent_response,
            criteria={"Clarity": ["Is the response clear?"]},
            evaluator_models=["test-model"]
        )
        
        # Verify that the result contains an error
        assert result is not None
        assert "_meta" in result
        assert "errors" in result["_meta"]
        assert len(result["_meta"]["errors"]) > 0
        assert "test-model" in result
        assert "error" in result["test-model"]
        assert result["test-model"]["error"] is True
        assert "LLM client error" in result["test-model"]["error_message"]


@pytest.mark.asyncio
async def test_evaluate_response_with_chat_completion_error(semantic_evaluator, workflow_scenario):
    """Test that evaluate_response handles chat completion errors gracefully."""
    # Create a scenario context and agent response
    scenario_context = "This is a test scenario context."
    agent_response = "This is a test agent response."
    
    # Create a mock LLM client
    mock_llm_client = AsyncMock()
    mock_llm_client.chat_completion.side_effect = Exception("Chat completion error")
    
    # Mock the _get_llm_client method to return the mock client
    with patch.object(semantic_evaluator, '_get_llm_client', return_value=mock_llm_client):
        # Call evaluate_response
        result = await semantic_evaluator.evaluate_response(
            scenario_context=scenario_context,
            agent_response=agent_response,
            criteria={"Clarity": ["Is the response clear?"]},
            evaluator_models=["test-model"]
        )
        
        # Verify that the result contains an error
        assert result is not None
        assert "_meta" in result
        assert "errors" in result["_meta"]
        assert len(result["_meta"]["errors"]) > 0
        assert "test-model" in result
        assert "error" in result["test-model"]
        assert result["test-model"]["error"] is True
        assert "Chat completion error" in result["test-model"]["error_message"]


@pytest.mark.asyncio
async def test_evaluate_response_with_invalid_response_format(semantic_evaluator, workflow_scenario):
    """Test that evaluate_response handles invalid response format gracefully."""
    # Create a scenario context and agent response
    scenario_context = "This is a test scenario context."
    agent_response = "This is a test agent response."
    
    # Create a mock LLM client that returns an invalid response format
    mock_llm_client = AsyncMock()
    mock_llm_client.chat_completion.return_value = LLMResponse(
        content="This is not valid JSON",
        response_type=ResponseType.TEXT,
        input_tokens=100,
        output_tokens=50
    )
    
    # Mock the _get_llm_client method to return the mock client
    with patch.object(semantic_evaluator, '_get_llm_client', return_value=mock_llm_client):
        # Mock the _parse_evaluation_response method to raise an exception
        with patch.object(semantic_evaluator, '_parse_evaluation_response', side_effect=Exception("Invalid response format")):
            # Call evaluate_response
            result = await semantic_evaluator.evaluate_response(
                scenario_context=scenario_context,
                agent_response=agent_response,
                criteria={"Clarity": ["Is the response clear?"]},
                evaluator_models=["test-model"]
            )
            
            # Verify that the result contains an error
            assert result is not None
            assert "_meta" in result
            assert "errors" in result["_meta"]
            assert len(result["_meta"]["errors"]) > 0
            assert "test-model" in result
            assert "error" in result["test-model"]
            assert result["test-model"]["error"] is True
            assert "Invalid response format" in result["test-model"]["error_message"]


@pytest.mark.asyncio
async def test_evaluate_response_with_multiple_models_partial_failure(semantic_evaluator, workflow_scenario):
    """Test that evaluate_response handles partial failure with multiple models gracefully."""
    # Create a scenario context and agent response
    scenario_context = "This is a test scenario context."
    agent_response = "This is a test agent response."
    
    # Create a mock successful LLM client
    mock_success_client = AsyncMock()
    mock_success_client.chat_completion.return_value = LLMResponse(
        content=json.dumps({
            "overall_score": 0.8,
            "overall_reasoning": "Good response",
            "dimensions": {
                "Clarity": {"score": 0.85, "reasoning": "Clear"}
            }
        }),
        response_type=ResponseType.TEXT,
        input_tokens=100,
        output_tokens=50
    )
    
    # Create a mock failing LLM client
    mock_failure_client = AsyncMock()
    mock_failure_client.chat_completion.side_effect = Exception("Model error")
    
    # Mock the _get_llm_client method to return different clients based on model name
    async def mock_get_llm_client(model_name):
        if model_name == "success-model":
            return mock_success_client
        else:
            return mock_failure_client
    
    # Patch the _get_llm_client method
    with patch.object(semantic_evaluator, '_get_llm_client', side_effect=mock_get_llm_client):
        # Call evaluate_response with multiple models
        result = await semantic_evaluator.evaluate_response(
            scenario_context=scenario_context,
            agent_response=agent_response,
            criteria={"Clarity": ["Is the response clear?"]},
            evaluator_models=["success-model", "failure-model"]
        )
        
        # Verify that the result contains both success and error
        assert result is not None
        assert "_meta" in result
        assert "errors" in result["_meta"]
        assert len(result["_meta"]["errors"]) > 0
        assert "success-model" in result
        assert "error" not in result["success-model"]
        assert result["success-model"]["overall_score"] == 0.8
        assert "failure-model" in result
        assert "error" in result["failure-model"]
        assert result["failure-model"]["error"] is True
        assert "Model error" in result["failure-model"]["error_message"]
