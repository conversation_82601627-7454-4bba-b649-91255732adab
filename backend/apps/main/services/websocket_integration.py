"""
WebSocket Integration Layer for Goali

This module provides integration between the WebSocket Manager and Django Channels consumers.
It handles the lifecycle of WebSocket connections and provides a clean interface for
message routing and connection management.
"""

import logging
import uuid
from typing import Optional, Dict, Any
from django.utils import timezone

from .websocket_manager import websocket_manager, ConnectionState

logger = logging.getLogger(__name__)


class WebSocketIntegration:
    """
    Integration layer between Django Channels consumers and WebSocket Manager
    """
    
    @staticmethod
    def on_connect(consumer, user_id: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Handle WebSocket connection
        
        Args:
            consumer: Django Channels consumer instance
            user_id: Optional user ID if authenticated
            metadata: Optional connection metadata
            
        Returns:
            str: Session ID for the connection
        """
        try:
            # Generate session ID
            session_id = str(uuid.uuid4())
            
            # Extract metadata from consumer
            if metadata is None:
                metadata = {}
            
            # Add scope information
            scope = getattr(consumer, 'scope', {})
            metadata.update({
                'user_agent': dict(scope.get('headers', {})).get(b'user-agent', b'').decode('utf-8'),
                'ip_address': scope.get('client', ['unknown', None])[0],
                'path': scope.get('path', ''),
                'query_string': scope.get('query_string', b'').decode('utf-8')
            })
            
            # Register connection
            success = websocket_manager.register_connection(
                session_id=session_id,
                channel_name=consumer.channel_name,
                consumer_instance=consumer,
                user_id=user_id,
                metadata=metadata
            )
            
            if success:
                # Store session ID in consumer for easy access
                consumer.session_id = session_id
                
                # Start monitoring if not already started
                import asyncio
                try:
                    asyncio.create_task(websocket_manager.start_monitoring())
                except Exception as e:
                    logger.warning(f"Monitoring already started or failed to start: {e}")
                
                logger.info(f"WebSocket connected: session_id={session_id}, user_id={user_id}")
                return session_id
            else:
                logger.error(f"Failed to register WebSocket connection for user {user_id}")
                raise Exception("Connection registration failed")
                
        except Exception as e:
            logger.error(f"Error in WebSocket connection: {e}")
            raise
    
    @staticmethod
    def on_authenticate(consumer, user_id: str) -> bool:
        """
        Handle WebSocket authentication
        
        Args:
            consumer: Django Channels consumer instance
            user_id: User ID for authentication
            
        Returns:
            bool: True if authentication successful
        """
        try:
            session_id = getattr(consumer, 'session_id', None)
            if not session_id:
                logger.error("No session ID found for authentication")
                return False
            
            success = websocket_manager.authenticate_connection(session_id, user_id)
            
            if success:
                logger.info(f"WebSocket authenticated: session_id={session_id}, user_id={user_id}")
            else:
                logger.error(f"Failed to authenticate WebSocket: session_id={session_id}, user_id={user_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error in WebSocket authentication: {e}")
            return False
    
    @staticmethod
    def on_disconnect(consumer) -> bool:
        """
        Handle WebSocket disconnection
        
        Args:
            consumer: Django Channels consumer instance
            
        Returns:
            bool: True if disconnection handled successfully
        """
        try:
            session_id = getattr(consumer, 'session_id', None)
            if not session_id:
                logger.debug("No session ID found for disconnection")
                return True
            
            success = websocket_manager.unregister_connection(session_id)
            
            if success:
                logger.info(f"WebSocket disconnected: session_id={session_id}")
            else:
                logger.warning(f"Failed to unregister WebSocket: session_id={session_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error in WebSocket disconnection: {e}")
            return False
    
    @staticmethod
    def on_message_received(consumer, message_data: Dict[str, Any]) -> bool:
        """
        Handle incoming WebSocket message
        
        Args:
            consumer: Django Channels consumer instance
            message_data: Received message data
            
        Returns:
            bool: True if message handled successfully
        """
        try:
            session_id = getattr(consumer, 'session_id', None)
            if not session_id:
                logger.error("No session ID found for message handling")
                return False
            
            # Update activity
            workflow_id = message_data.get('workflow_id')
            websocket_manager.update_activity(session_id, workflow_id)
            
            logger.debug(f"Message received: session_id={session_id}, type={message_data.get('type')}")
            return True
            
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
            return False
    
    @staticmethod
    def send_message_to_session(session_id: str, message: Dict[str, Any]) -> bool:
        """
        Send message to specific session
        
        Args:
            session_id: Target session ID
            message: Message to send
            
        Returns:
            bool: True if message sent successfully
        """
        return websocket_manager.send_message_to_session(session_id, message)
    
    @staticmethod
    def send_message_to_user(user_id: str, message: Dict[str, Any]) -> int:
        """
        Send message to all sessions of a user
        
        Args:
            user_id: Target user ID
            message: Message to send
            
        Returns:
            int: Number of sessions message was sent to
        """
        return websocket_manager.send_message_to_user(user_id, message)
    
    @staticmethod
    def broadcast_message(message: Dict[str, Any]) -> int:
        """
        Broadcast message to all connections
        
        Args:
            message: Message to broadcast
            
        Returns:
            int: Number of connections message was sent to
        """
        return websocket_manager.broadcast_message(message)
    
    @staticmethod
    def get_user_sessions(user_id: str) -> list:
        """
        Get all session IDs for a user
        
        Args:
            user_id: User ID
            
        Returns:
            list: List of session IDs
        """
        return websocket_manager.get_user_sessions(user_id)
    
    @staticmethod
    def get_connection_info(session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get connection information
        
        Args:
            session_id: Session ID
            
        Returns:
            dict: Connection information or None
        """
        return websocket_manager.get_connection_info(session_id)
    
    @staticmethod
    def get_metrics() -> Dict[str, Any]:
        """
        Get WebSocket metrics
        
        Returns:
            dict: Current metrics
        """
        return websocket_manager.get_metrics()
    
    @staticmethod
    def get_all_connections() -> Dict[str, Dict[str, Any]]:
        """
        Get all connection information
        
        Returns:
            dict: All connections information
        """
        return websocket_manager.get_all_connections()


# Global integration instance
ws_integration = WebSocketIntegration()
