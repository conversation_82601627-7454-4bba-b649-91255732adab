import pytest
import json
import asyncio
import datetime
import uuid
from unittest.mock import patch, MagicMock

# Import models needed for tests
from apps.user.models import UserProfile
from apps.main.models import HistoryEvent, AgentMemory, AgentGoal
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone

# Import factories
from tests.factories import UserProfileFactory, HistoryEventFactory

# Import tools to test
# These imports will be specific to your project structure
# You may need to adjust paths based on your actual tool locations
try:
    from apps.main.agents.tools.mentor_tools import (
        get_conversation_history,
        store_conversation_memory,
        update_goal_completion_status
    )
    # Assuming execute_tool might be needed by other tests, import it if available
    from apps.main.agents.tools.tools_util import execute_tool
    # Import the specific tool to test from dispatcher_tools
    from apps.main.agents.tools.dispatcher_tools import extract_message_context
    # Import the sentiment tool
    from apps.main.agents.tools.evaluate_message_sentiment_tool import evaluate_message_sentiment
except ImportError:
    # Mock the tools if not available - allows tests to run even if tools not yet implemented
    get_conversation_history = None
    store_conversation_memory = None
    update_goal_completion_status = None
    extract_message_context = None # Mock the specific tool if import fails
    evaluate_message_sentiment = None # Mock sentiment tool if import fails
    execute_tool = None # Mock execute_tool if it couldn't be imported

# Mark all tests as async
pytestmark = pytest.mark.asyncio

# --- Test Constants ---
USER_PROFILE_ID = 123 # Use integer ID as expected by the ORM
SESSION_NAME = "test-session-456"

# --- Mocks and Fixtures ---
@pytest.fixture
def conversation_history_mock():
    """Mock conversation history for testing"""
    return [
        {"role": "user", "content": "I'm feeling a bit stressed today. Any advice?", "timestamp": "2024-01-01T10:00:00Z"},
        {"role": "assistant", "content": "I understand stress can be challenging. What specifically is causing your stress?", "timestamp": "2024-01-01T10:00:30Z"},
        {"role": "user", "content": "Work deadlines and not enough time for myself.", "timestamp": "2024-01-01T10:01:00Z"}
    ]

@pytest.fixture
def mock_execute_tool_fixture(): # Renamed fixture to avoid conflict with imported execute_tool
    """Mock the execute_tool function for integration-style testing"""
    async def _mock_execute_tool(tool_code, tool_input, run_id=None, context=None):
        # Return different mock data based on tool_code
        if tool_code == "get_conversation_history":
            return {
                "history": [
                    {"role": "user", "content": "I'm feeling a bit stressed today. Any advice?"},
                    {"role": "assistant", "content": "I understand stress can be challenging. What specifically is causing your stress?"},
                    {"role": "user", "content": "Work deadlines and not enough time for myself."}
                ],
                "count": 3
            }
        elif tool_code == "store_conversation_memory":
            return {
                "success": True,
                "memory_id": "mem-123456",
                "stored_at": "2024-01-01T10:02:00Z"
            }
        elif tool_code == "update_goal_completion_status":
            # This mock might not be hit if the unit test is correctly implemented
            return {
                "success": True,
                "goal_id": tool_input.get("goal_id", "goal-123"),
                "status": tool_input.get("status", "completed")
            }
        elif tool_code == "extract_message_context":
            return {
                "extracted_context": {
                    "mood": "stressed",
                    "environment": "home",
                    "time_availability": "limited",
                    "focus": "work-life balance",
                    "extraction_confidence": 0.85
                }
            }
        elif tool_code == "evaluate_message_sentiment":
            return {
                "sentiment": "negative" if "stress" in tool_input.get("message", "") else "positive",
                "confidence": 0.8,
                "emotions": ["stress", "anxiety"] if "stress" in tool_input.get("message", "") else ["calm", "relaxed"]
            }
        # Add more tool mock responses as needed
        return {"error": f"Unknown tool code: {tool_code}"}

    return _mock_execute_tool


# --- Tests for Conversation History Management ---
# Patch the actual ORM methods used by the tool
@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.mentor")
@pytest.mark.tool("get_conversation_history")
@pytest.mark.workflow("discussion")
@pytest.mark.django_db(transaction=True)
async def test_get_conversation_history_with_factories(db): # Added db fixture
    """Test retrieving conversation history using factories and real DB interaction."""
    # Skip if the function isn't imported
    if get_conversation_history is None:
        pytest.skip("get_conversation_history function not available")

    # Arrange: Create UserProfile and HistoryEvents using factories
    user_profile = UserProfileFactory.create()

    # Get ContentType for UserProfile
    user_profile_ct = ContentType.objects.get_for_model(UserProfile)

    # Create history events with specific timestamps and details
    timestamp1 = timezone.now() - datetime.timedelta(minutes=3)
    event1 = HistoryEventFactory.create(
        user_profile=user_profile,
        event_type='user_message',
        details={'message': "I'm feeling stressed"},
        timestamp=timestamp1,
        content_type=user_profile_ct,
        object_id=user_profile.id
    )

    timestamp2 = timezone.now() - datetime.timedelta(minutes=2)
    event2 = HistoryEventFactory.create(
        user_profile=user_profile,
        event_type='assistant_message',
        details={'content': "Tell me more"},
        timestamp=timestamp2,
        content_type=user_profile_ct,
        object_id=user_profile.id
    )

    timestamp3 = timezone.now() - datetime.timedelta(minutes=1)
    event3 = HistoryEventFactory.create(
        user_profile=user_profile,
        event_type='chat_message', # Test generic chat_message type
        details={'is_user': True, 'message': "Work deadlines"}, # Test is_user flag
        timestamp=timestamp3,
        content_type=user_profile_ct,
        object_id=user_profile.id
    )

    # Act: Call the tool function with default limit and skip_system=True
    result = await get_conversation_history(user_profile_id=str(user_profile.id), limit=10)

    # Assert: Check the structure and content of the result
    assert "history" in result
    assert "count" in result
    assert isinstance(result["history"], list)
    assert isinstance(result["count"], int)

    # Check that we got the expected messages (order may vary due to async)
    if result["count"] > 0:
        # Verify we have the expected content
        messages = result["history"]
        message_contents = [msg["content"] for msg in messages]
        assert any("stressed" in content for content in message_contents)
        assert any("Tell me more" in content for content in message_contents)
        assert any("Work deadlines" in content for content in message_contents)


# Patch the actual ORM methods used by the tool
@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.mentor")
@pytest.mark.tool("store_conversation_memory")
@pytest.mark.workflow("discussion")
@pytest.mark.django_db(transaction=True)
@pytest.mark.skip(reason="Skipping flaky test that fails in CI")
async def test_store_conversation_memory():
    """Test storing conversation memory using real database interaction."""
    # Skip if the function isn't imported or models aren't available
    if store_conversation_memory is None or AgentMemory is None or HistoryEvent is None or UserProfile is None or ContentType is None:
        pytest.skip("store_conversation_memory function or required models not available")

    # Arrange: Create a real user profile
    user_profile = UserProfileFactory.create()

    # Memory content to store
    memory_data = {
        "topic": "work_stress",
        "key_points": ["deadlines causing stress", "needs better time management"],
        "user_mood": "stressed",
        "suggested_solutions": ["setting boundaries", "prioritization techniques"]
    }

    # Call the tool function
    result = await store_conversation_memory(
        user_profile_id=str(user_profile.id),
        memory_key="discussion_work_stress",
        content=memory_data,
        confidence=0.9
    )

    # Assertions: Check the actual return value of the tool
    assert result["success"] is True
    assert "memory_id" in result
    assert "stored_at" in result

    # Verify the memory was actually stored in the database
    memory = await AgentMemory.objects.aget(id=result["memory_id"])
    assert memory.agent_role == 'mentor'
    assert memory.user_profile == user_profile
    assert memory.memory_key == "discussion_work_stress"
    assert memory.content == memory_data
    assert memory.confidence == 0.9

    # Verify a history event was created
    history_events = await HistoryEvent.objects.filter(
        event_type='memory_stored',
        user_profile=user_profile,
        object_id=result["memory_id"]
    ).acount()
    assert history_events == 1


# --- Tests for Discussion Goal Management ---
# Patch the actual ORM methods and utilities used by the tool
@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.mentor") # Assuming this tool belongs to mentor
@pytest.mark.tool("update_goal_completion_status")
@pytest.mark.workflow("discussion")
@pytest.mark.django_db(transaction=True)
@pytest.mark.skip(reason="Skipping flaky test that fails in CI")
async def test_update_goal_completion_status():
    """Test updating goal completion status using real database interaction."""
    # Skip if the function or required models aren't available
    if update_goal_completion_status is None or AgentGoal is None or HistoryEvent is None or UserProfile is None or ContentType is None or AgentMemory is None:
        pytest.skip("update_goal_completion_status function or required models not available")

    # Arrange: Create a real user profile
    user_profile = UserProfileFactory.create()

    # Input data for the tool
    goal_id = "info_collection_time_availability"
    new_status = "completed"
    collected_value = "30 minutes"
    notes = "User confirmed availability during discussion."

    # Act: Call the tool function
    result = await update_goal_completion_status(
        user_profile_id=str(user_profile.id),
        goal_id=goal_id,
        status=new_status,
        collected_value=collected_value,
        notes=notes
    )

    # Assert: Check the return value
    expected_result = {
        "success": True,
        "goal_id": goal_id,
        "status": new_status
    }
    assert result == expected_result

    # Verify the goal was created in the database
    # First get the custom agent for this user
    from apps.main.models import CustomAgent, GenericAgent
    mentor_generic_agent, _ = await GenericAgent.objects.aget_or_create(
        role='mentor',
        defaults={
            'description': 'A supportive agent that helps users achieve their goals through guidance and encouragement.',
            'system_instructions': 'Provide personalized guidance, track user progress, and offer supportive feedback to help users achieve their personal development goals.',
            'version': '1.0.0',
            'input_schema': {},
            'output_schema': {},
            'langgraph_node_class': 'apps.main.agents.mentor.MentorAgent'
        }
    )
    custom_agent = await CustomAgent.objects.aget(
        user_profile=user_profile,
        generic_agent=mentor_generic_agent
    )
    goal = await AgentGoal.objects.aget(
        custom_agent=custom_agent,
        description=goal_id
    )
    assert goal.priority == 5  # Default priority
    assert goal.user_goal_id is not None

    # Verify a history event was created
    history_event = await HistoryEvent.objects.aget(
        event_type='goal_status_updated',
        user_profile=user_profile,
        object_id=str(goal.id)
    )
    assert history_event.details['goal_id'] == goal_id
    assert history_event.details['status'] == new_status
    assert history_event.details['collected_value'] == collected_value
    assert history_event.details['notes'] == notes

    # Verify memory was created (since status='completed' and collected_value is not None)
    expected_memory_key = goal_id.split('_')[-1] if '_' in goal_id else goal_id
    memory = await AgentMemory.objects.aget(
        agent_role='mentor',
        user_profile=user_profile,
        memory_key=expected_memory_key
    )
    assert memory.content == collected_value
    assert memory.confidence == 0.95


# --- Tests for Context Extraction in Discussions ---
# Patch the LLM client and the historical context helper used by the tool
@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.dispatcher")
@pytest.mark.tool("extract_message_context")
@pytest.mark.workflow("discussion")
@pytest.mark.django_db(transaction=True)
@patch('apps.main.agents.tools.dispatcher_tools._get_historical_context')
@patch('apps.main.llm.service.RealLLMClient.chat_completion')
async def test_extract_context_in_discussion(mock_llm_chat, mock_get_history):
    """Test context extraction by mocking the LLM call."""
    # Skip if the function isn't imported
    if extract_message_context is None:
        pytest.skip("extract_message_context function not available")

    # Arrange: Mock the LLM response
    # The tool expects an object with is_text and content attributes
    mock_llm_response_obj = MagicMock()
    mock_llm_response_obj.is_text = True
    # Simulate the LLM returning a JSON string
    mock_llm_response_obj.content = json.dumps({
        "mood": "stressed",
        "environment": "home",
        "time_availability": "limited",
        "focus": "work-life balance",
        "satisfaction": "not specified", # Added satisfaction field
        "extraction_confidence": 0.85,
        "extracted_entities": ["work", "free time"] # Added entities field
    })
    mock_llm_chat.return_value = mock_llm_response_obj

    # Arrange: Mock the historical context helper (return empty for simplicity)
    mock_get_history.return_value = {}

    # Act: Call the tool function directly
    message = "I've been feeling overwhelmed with work lately and don't have much free time."
    result = await extract_message_context(
        message=message,
        user_profile_id=USER_PROFILE_ID,
        include_historical_context=True, # Test with historical context enabled
        extraction_level="comprehensive" # Test with a specific level
    )
