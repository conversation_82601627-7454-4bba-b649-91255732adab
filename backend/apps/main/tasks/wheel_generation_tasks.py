# backend/apps/main/tasks/wheel_generation_tasks.py

from celery import shared_task
import logging
import asyncio
import json
import time
from typing import Dict, Any, Optional

from apps.main.graphs.wheel_generation_graph import run_wheel_generation_workflow
from apps.user.models import UserProfile
from apps.main.models import HistoryEvent
from django.contrib.contenttypes.models import ContentType
from apps.main.services.progress_tracking_service import ProgressTrackingService, ProgressPriority
from apps.main.services.observability_service import observability, CeleryObserver, EventType, Severity

logger = logging.getLogger(__name__)

@shared_task(bind=True, name="execute_wheel_generation_workflow")
@CeleryObserver.instrument_task("wheel_generation")
def execute_wheel_generation_workflow(self,
                                     user_profile_id: str,
                                     context_packet: Dict[str, Any],
                                     workflow_id: Optional[str] = None):
    """
    Execute the complete wheel generation workflow.
    
    This task orchestrates the multi-agent wheel generation process, which:
    1. Starts with mentor agent gathering context
    2. Routes through multiple specialized agents
    3. Creates a personalized activity wheel
    4. Provides that wheel back to the user
    
    Args:
        user_profile_id: The ID of the user profile
        context_packet: Initial context information from user/system
        workflow_id: Optional workflow ID (will generate new one if not provided)
        
    Returns:
        dict: Wheel generation results including the wheel and agent outputs
    """
    try:
        logger.info(f"Starting wheel generation workflow for user {user_profile_id}")

        # Emit workflow start event for observability
        observability.emit_event(
            EventType.WORKFLOW_START,
            'celery',
            'wheel_generation_workflow',
            metadata={
                'user_profile_id': user_profile_id,
                'task_id': self.request.id,
                'workflow_id': workflow_id
            },
            tags={'workflow_type': 'wheel_generation', 'priority': 'high'}
        )

        # Ensure context packet has workflow metadata
        if not context_packet.get("task_type"):
            context_packet["task_type"] = "wheel_generation"

        # Store the Celery task ID for tracking
        context_packet["celery_task_id"] = self.request.id
        
        # Initialize progress tracking
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        progress_tracker = None

        try:
            # Get progress tracking service
            progress_service = loop.run_until_complete(ProgressTrackingService.get_instance())

            # Create progress tracker
            progress_tracker = progress_service.create_tracker(
                name="Wheel Generation",
                user_id=str(user_profile_id),
                workflow_type="wheel_generation",
                tracker_id=self.request.id
            )

            # Stage 1: Initialize workflow
            stage_id = progress_tracker.start_stage(
                "initialization",
                "Initializing Workflow",
                "Setting up wheel generation process...",
                ProgressPriority.HIGH
            )

            context_packet["progress_tracker_id"] = progress_tracker.tracker_id
            progress_tracker.update_stage(stage_id, 10, "Workflow metadata prepared")
            progress_tracker.complete_stage(stage_id, "Initialization complete")

            # Stage 2: Workflow execution
            stage_id = progress_tracker.start_stage(
                "workflow_execution",
                "Executing Workflow",
                "Running multi-agent wheel generation...",
                ProgressPriority.HIGH
            )

            progress_tracker.update_stage(stage_id, 30, "Starting agent coordination")

            # Run the workflow with progress tracking
            start_time = time.time()
            result = loop.run_until_complete(
                run_wheel_generation_workflow(
                    user_profile_id=user_profile_id,
                    context_packet=context_packet,
                    workflow_id=workflow_id
                )
            )
            execution_time = (time.time() - start_time) * 1000

            progress_tracker.update_stage(stage_id, 85, "Workflow execution complete")
            progress_tracker.complete_stage(
                stage_id,
                f"Workflow completed in {execution_time:.0f}ms",
                {"execution_time_ms": execution_time}
            )

        finally:
            loop.close()
        
        # Stage 3: Result processing and completion
        if progress_tracker:
            stage_id = progress_tracker.start_stage(
                "result_processing",
                "Processing Results",
                "Finalizing wheel data and metrics...",
                ProgressPriority.NORMAL
            )

            progress_tracker.update_stage(stage_id, 95, "Results processed")

            # Add performance metrics to result
            if result and isinstance(result, dict):
                result["performance_metrics"] = {
                    "total_execution_time_ms": execution_time,
                    "celery_task_id": self.request.id,
                    "progress_tracker_id": progress_tracker.tracker_id,
                    "workflow_efficiency": 1.0 if execution_time < 30000 else 0.8 if execution_time < 60000 else 0.6
                }

            progress_tracker.complete_stage(stage_id, "Result processing complete")
            progress_tracker.complete_tracker(f"Wheel generation completed successfully in {execution_time:.0f}ms")

        # Emit workflow completion event for observability
        observability.emit_event(
            EventType.WORKFLOW_END,
            'celery',
            'wheel_generation_workflow',
            duration_ms=execution_time,
            metadata={
                'user_profile_id': user_profile_id,
                'task_id': self.request.id,
                'workflow_id': workflow_id,
                'success': True,
                'wheel_items_count': len(result.get('wheel', {}).get('items', [])) if result else 0
            },
            metrics={
                'execution_time_ms': execution_time,
                'wheel_items_generated': float(len(result.get('wheel', {}).get('items', [])) if result else 0)
            }
        )

        # Record workflow completion in history
        try:
            # Get user profile content type
            user_profile = UserProfile.objects.get(id=user_profile_id)
            content_type = ContentType.objects.get_for_model(UserProfile)

            # Extract the wheel from the result
            wheel_data = None
            if hasattr(result, 'wheel') and result.wheel:
                wheel_data = result.wheel
            elif isinstance(result, dict) and 'wheel' in result:
                wheel_data = result['wheel']

            # Create history event with performance metrics
            HistoryEvent.objects.create(
                event_type='wheel_generated',
                content_type=content_type,
                object_id=user_profile_id,
                user_profile=user_profile,
                details={
                    'workflow_id': workflow_id or result.workflow_id if hasattr(result, 'workflow_id') else str(self.request.id),
                    'status': 'completed' if not result.get('error', None) else 'failed',
                    'wheel_summary': {
                        'name': wheel_data.get('name', 'Activity Wheel') if wheel_data else None,
                        'item_count': len(wheel_data.get('items', [])) if wheel_data else 0
                    } if wheel_data else None,
                    'agent_flow': 'wheel_generation_graph',
                    'performance_metrics': {
                        'execution_time_ms': execution_time if 'execution_time' in locals() else None,
                        'progress_tracker_id': progress_tracker.tracker_id if progress_tracker else None
                    }
                }
            )
        except Exception as e:
            logger.error(f"Error recording wheel generation history: {str(e)}")
            # Non-critical error, don't re-raise
            
        # Return result, converting to dict if needed
        if hasattr(result, 'dict'):
            return result.dict()
        elif isinstance(result, dict):
            return result
        else:
            return {"error": "Unknown result format", "completed": False}
            
    except Exception as e:
        logger.error(f"Error executing wheel generation workflow: {str(e)}", exc_info=True)

        # Mark progress tracker as errored if available
        if progress_tracker:
            progress_tracker.error_stage(
                "workflow_execution",
                f"Workflow execution failed: {str(e)}",
                {"error_type": type(e).__name__, "error_details": str(e)}
            )

        # Emit workflow error event for observability
        observability.emit_event(
            EventType.ERROR,
            'celery',
            'wheel_generation_workflow',
            severity=Severity.ERROR,
            metadata={
                'user_profile_id': user_profile_id,
                'task_id': self.request.id,
                'workflow_id': workflow_id,
                'error_type': type(e).__name__,
                'error_message': str(e),
                'success': False
            }
        )

        return {
            "error": str(e),
            "workflow_id": workflow_id,
            "completed": False,
            "user_profile_id": user_profile_id,
            "task_type": "wheel_generation",
            "performance_metrics": {
                "celery_task_id": self.request.id,
                "progress_tracker_id": progress_tracker.tracker_id if progress_tracker else None,
                "workflow_efficiency": 0.0
            }
        }