# backend/apps/main/tests/test_benchmark_error_handling.py

import pytest
import uuid
from unittest.mock import Mock, patch
from django.test import TestCase
from django.utils import timezone

from apps.main.models import BenchmarkRun, BenchmarkScenario, GenericAgent, LLMConfig, AgentRole
from apps.main.services.benchmark_manager import AgentBenchmarker
from apps.main.services.wheel_workflow_benchmark_manager import WheelWorkflowBenchmarkManager


class TestBenchmarkErrorHandling(TestCase):
    """Test error handling and reporting in benchmark execution."""

    def setUp(self):
        """Set up test fixtures."""
        # Create test LLM config
        self.llm_config = LLMConfig.objects.create(
            name="test-llm",
            model_name="test-model",
            temperature=0.7,
            input_token_price=0.001,
            output_token_price=0.002
        )

        # Create test agent using get_or_create to avoid unique constraint violations
        self.agent, created = GenericAgent.objects.get_or_create(
            role=AgentRole.MENTOR,
            defaults={
                'version': "1.0",
                'description': "Test agent for error handling",
                'system_instructions': "Test instructions",
                'input_schema': {"type": "object"},
                'output_schema': {"type": "object"},
                'llm_config': self.llm_config,
                'langgraph_node_class': "test.TestNode"
            }
        )

        # Create test scenario with the agent role
        self.scenario = BenchmarkScenario.objects.create(
            name=f"Test Error Scenario {uuid.uuid4()}",
            description="Test scenario for error handling",
            agent_role=AgentRole.MENTOR,
            input_data={"test": "data"},
            metadata={"workflow_type": "wheel_generation"}
        )

    def test_benchmark_run_error_storage_structure(self):
        """Test that BenchmarkRun can store structured error information."""
        # Create a benchmark run with error information
        error_info = {
            'errors': [
                {
                    'type': 'critical',
                    'level': 'error',
                    'message': 'LLM connection failed',
                    'source': 'wheel_workflow_benchmark_manager',
                    'timestamp': timezone.now().isoformat(),
                    'details': {
                        'error_type': 'ConnectionError',
                        'traceback': 'Traceback (most recent call last)...'
                    }
                },
                {
                    'type': 'warning',
                    'level': 'warning',
                    'message': 'Agent response truncated',
                    'source': 'orchestrator_agent',
                    'timestamp': timezone.now().isoformat(),
                    'details': {
                        'original_length': 5000,
                        'truncated_length': 4000
                    }
                }
            ],
            'error_summary': {
                'total_errors': 2,
                'critical_errors': 1,
                'warnings': 1,
                'has_critical_errors': True
            }
        }

        benchmark_run = BenchmarkRun.objects.create(
            scenario=self.scenario,
            agent_definition=self.agent,
            llm_config=self.llm_config,
            agent_version="1.0",
            parameters={'test': 'params'},
            runs_count=1,
            mean_duration=1000.0,
            median_duration=1000.0,
            min_duration=1000.0,
            max_duration=1000.0,
            std_dev=0.0,
            success_rate=0.5,  # Partial success due to errors
            raw_results=error_info
        )

        # Verify error information is stored correctly
        self.assertEqual(len(benchmark_run.raw_results['errors']), 2)
        self.assertTrue(benchmark_run.raw_results['error_summary']['has_critical_errors'])
        self.assertEqual(benchmark_run.raw_results['error_summary']['total_errors'], 2)

    def test_benchmark_run_error_classification_methods(self):
        """Test methods for classifying and accessing error information."""
        # This test will verify methods we'll add to BenchmarkRun model
        error_info = {
            'errors': [
                {
                    'type': 'critical',
                    'level': 'error',
                    'message': 'Workflow execution failed',
                    'source': 'wheel_generation_graph'
                },
                {
                    'type': 'warning',
                    'level': 'warning',
                    'message': 'Slow response time detected',
                    'source': 'mentor_agent'
                }
            ]
        }

        benchmark_run = BenchmarkRun.objects.create(
            scenario=self.scenario,
            agent_definition=self.agent,
            llm_config=self.llm_config,
            agent_version="1.0",
            parameters={'test': 'params'},
            runs_count=1,
            mean_duration=1000.0,
            median_duration=1000.0,
            min_duration=1000.0,
            max_duration=1000.0,
            std_dev=0.0,
            success_rate=0.5,
            raw_results=error_info
        )

        # Test methods we've implemented
        self.assertTrue(benchmark_run.has_errors())
        self.assertTrue(benchmark_run.has_critical_errors())

        error_summary = benchmark_run.get_error_summary()
        self.assertEqual(error_summary['total_errors'], 2)
        self.assertEqual(error_summary['critical_errors'], 1)
        self.assertEqual(error_summary['warnings'], 1)
        self.assertTrue(error_summary['has_critical_errors'])

        critical_errors = benchmark_run.get_errors_by_type('critical')
        self.assertEqual(len(critical_errors), 1)
        self.assertEqual(critical_errors[0]['message'], 'Workflow execution failed')

        warnings = benchmark_run.get_errors_by_type('warning')
        self.assertEqual(len(warnings), 1)
        self.assertEqual(warnings[0]['message'], 'Slow response time detected')

    def test_error_aggregation_in_workflow_benchmark(self):
        """Test that workflow benchmarks properly aggregate errors from multiple agents."""
        # This test will verify error collection during workflow execution
        pass  # Will implement after adding error collection to workflow manager

    def test_celery_task_error_integration(self):
        """Test that Celery task errors are properly integrated into BenchmarkRun."""
        # Test the error handling logic without the full Celery infrastructure
        from apps.main.tasks.benchmark_tasks import run_workflow_benchmark
        from unittest.mock import patch, MagicMock

        # Create a mock task that doesn't interfere with Celery backend
        mock_task = MagicMock()
        mock_task.update_state = Mock()

        # Test with an invalid scenario ID to trigger error handling
        result = run_workflow_benchmark(mock_task, "invalid-uuid-format")

        # Verify error handling
        self.assertEqual(result['status'], 'failed')
        self.assertIn('Invalid benchmark ID format', result['error'])
        self.assertIn('errors', result)
        self.assertGreater(result['error_count'], 0)

        # Verify that errors were captured
        errors = result['errors']
        self.assertTrue(any(error['type'] == 'critical' for error in errors))
        self.assertTrue(any('Invalid benchmark ID format' in error['message'] for error in errors))

    def test_task_status_api_with_benchmark_run_errors(self):
        """Test that task status API correctly extracts errors from BenchmarkRun objects."""
        from apps.admin_tools.views import BenchmarkTaskStatusView
        from django.http import HttpRequest
        import json
        import asyncio

        # Create a mock benchmark run with errors
        mock_benchmark_run = Mock()
        mock_benchmark_run.id = uuid.uuid4()
        mock_benchmark_run.has_errors.return_value = True
        mock_benchmark_run.has_critical_errors.return_value = True
        mock_benchmark_run.raw_results = {
            'errors': [
                {
                    'type': 'warning',
                    'level': 'warning',
                    'message': 'Agent response was truncated',
                    'source': 'orchestrator_agent',
                    'timestamp': '2025-01-31T12:00:00Z'
                },
                {
                    'type': 'critical',
                    'level': 'error',
                    'message': 'LLM service timeout',
                    'source': 'llm_service',
                    'timestamp': '2025-01-31T12:01:00Z'
                }
            ],
            'error_summary': {
                'total_errors': 2,
                'critical_errors': 1,
                'warnings': 1,
                'info_messages': 0
            }
        }

        # Create a task result that includes a benchmark_run_id
        task_result = {
            'benchmark_run_id': str(mock_benchmark_run.id),
            'status': 'completed',
            'has_errors': False,  # Task result doesn't show errors
            'errors': []  # Task result doesn't include errors
        }

        # Test the task status API
        view = BenchmarkTaskStatusView()
        request = HttpRequest()

        # Mock the AsyncResult to return our task result
        mock_async_result = Mock()
        mock_async_result.ready.return_value = True
        mock_async_result.failed.return_value = False
        mock_async_result.result = task_result

        with patch('celery.result.AsyncResult', return_value=mock_async_result):
            with patch('apps.admin_tools.views.BenchmarkRun.objects.get', return_value=mock_benchmark_run):
                response = asyncio.run(view.get(request, 'test-task-id'))
                response_data = json.loads(response.content)

                # Verify the API response includes error information from BenchmarkRun
                self.assertEqual(response_data['status'], 'completed')
                self.assertTrue(response_data['has_errors'])
                self.assertTrue(response_data['has_critical_errors'])
                self.assertIn('error_details', response_data)

                error_details = response_data['error_details']
                self.assertEqual(len(error_details['errors']), 2)
                self.assertEqual(error_details['error_summary']['critical_errors'], 1)
                self.assertEqual(error_details['error_summary']['warnings'], 1)
                self.assertEqual(error_details['source'], 'benchmark_run')

    def test_task_status_api_error_reporting(self):
        """Test that task status API returns detailed error information."""
        from apps.admin_tools.views import BenchmarkTaskStatusView
        from unittest.mock import Mock, AsyncMock
        from django.http import HttpRequest
        import json

        # Create a mock request
        request = HttpRequest()

        # Create the view instance
        view = BenchmarkTaskStatusView()

        # Test 1: Failed task with detailed error information
        mock_result = Mock()
        mock_result.ready.return_value = True
        mock_result.failed.return_value = True
        mock_result.result = Exception("Test task failure")
        mock_result.info = {
            'exc_type': 'ValueError',
            'exc_message': 'Test task failure',
            'status': 'Task failed',
            'errors': [
                {
                    'type': 'critical',
                    'level': 'error',
                    'message': 'Workflow execution failed',
                    'source': 'benchmark_manager',
                    'timestamp': '2025-01-31T10:00:00Z'
                },
                {
                    'type': 'warning',
                    'level': 'warning',
                    'message': 'Slow response detected',
                    'source': 'llm_service',
                    'timestamp': '2025-01-31T10:01:00Z'
                }
            ]
        }

        with patch('celery.result.AsyncResult', return_value=mock_result):
            # Use asyncio to run the async method
            import asyncio
            response = asyncio.run(view.get(request, 'test-task-id'))

            # Parse the response
            response_data = json.loads(response.content)

            # Verify error reporting
            self.assertEqual(response_data['status'], 'failed')
            self.assertTrue(response_data['has_errors'])
            self.assertTrue(response_data['has_critical_errors'])
            self.assertIn('error_details', response_data)

            error_details = response_data['error_details']
            self.assertEqual(len(error_details['errors']), 3)  # 2 from meta + 1 primary
            self.assertEqual(error_details['error_summary']['critical_errors'], 2)
            self.assertEqual(error_details['error_summary']['warnings'], 1)

        # Test 2: Successful task with non-critical errors
        mock_result_success = Mock()
        mock_result_success.ready.return_value = True
        mock_result_success.failed.return_value = False
        mock_result_success.result = {
            'status': 'completed',
            'benchmark_run_id': 'test-run-id',
            'errors': [
                {
                    'type': 'warning',
                    'level': 'warning',
                    'message': 'Agent response truncated',
                    'source': 'orchestrator_agent'
                }
            ]
        }

        with patch('celery.result.AsyncResult', return_value=mock_result_success):
            response = asyncio.run(view.get(request, 'test-task-id-success'))
            response_data = json.loads(response.content)

            # Verify successful task with warnings
            self.assertEqual(response_data['status'], 'completed')
            self.assertTrue(response_data['has_errors'])
            self.assertFalse(response_data['has_critical_errors'])
            self.assertIn('error_summary', response_data)

            error_summary = response_data['error_summary']
            self.assertEqual(error_summary['warnings'], 1)
            self.assertEqual(error_summary['critical_errors'], 0)

        # Test 3: Running task with progress errors
        mock_result_running = Mock()
        mock_result_running.ready.return_value = False
        mock_result_running.status = 'PROGRESS'
        mock_result_running.info = {
            'current': 2,
            'total': 5,
            'status': 'Processing step 2 of 5',
            'errors': [
                {
                    'type': 'warning',
                    'level': 'warning',
                    'message': 'Retry attempt for agent call',
                    'source': 'workflow_execution'
                }
            ]
        }

        with patch('celery.result.AsyncResult', return_value=mock_result_running):
            response = asyncio.run(view.get(request, 'test-task-id-running'))
            response_data = json.loads(response.content)

            # Verify running task with progress errors
            self.assertEqual(response_data['status'], 'progress')
            self.assertTrue(response_data['has_errors'])
            self.assertIn('error_details', response_data)

            error_details = response_data['error_details']
            self.assertEqual(len(error_details['errors']), 1)
            self.assertFalse(error_details['has_critical_errors'])

    def test_frontend_error_display_data(self):
        """Test that error data is properly formatted for frontend display."""
        # This test will verify error data formatting for UI consumption
        pass  # Will implement after adding error formatting methods
