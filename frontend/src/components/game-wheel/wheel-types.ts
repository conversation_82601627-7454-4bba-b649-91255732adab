/**
 * TypeScript interfaces and types for the spinning wheel component
 * Defines the data structures used by the wheel physics, rendering, and component logic
 */

import type { Body, Engine, World } from 'matter-js';
import type { Application, Container, Graphics } from 'pixi.js';

/**
 * Represents a single segment of the spinning wheel
 */
export interface WheelSegment {
  /** Unique identifier for the segment */
  id: string;
  /** Display text for the activity */
  text: string;
  /** Alternative name field for compatibility */
  name?: string;
  /** Description of the activity */
  description?: string;
  /** Percentage of the wheel this segment occupies (0-100) */
  percentage: number;
  /** Color for the segment background */
  color: string;
  /** Text color for the segment label */
  textColor?: string;
  /** Start angle in radians */
  startAngle: number;
  /** End angle in radians */
  endAngle: number;
  /** Center angle in radians */
  centerAngle: number;
  /** Activity ID this segment belongs to (for subdivided segments) */
  activityId?: string;
  /** Activity index in original data (for subdivided segments) */
  activityIndex?: number;
  /** Domain/category of the activity */
  domain?: string;
  /** Base challenge rating (0-100) */
  base_challenge_rating?: number;
  /** Segment index within the activity (for subdivided segments) */
  segmentIndex?: number;
}

/**
 * Configuration for wheel appearance and behavior
 */
export interface WheelConfig {
  /** Wheel radius in pixels */
  radius: number;
  /** Center X position */
  centerX: number;
  /** Center Y position */
  centerY: number;
  /** Number of nails around the wheel perimeter */
  nailCount: number;
  /** Radius of individual nails */
  nailRadius: number;
  /** Ball radius */
  ballRadius: number;
  /** Initial ball position offset from center */
  ballStartOffset: number;
  /** Spin force multiplier */
  spinForce: number;
  /** Maximum spin duration in milliseconds */
  maxSpinDuration: number;
  /** Friction coefficient for wheel rotation */
  wheelFriction: number;
  /** Air resistance coefficient */
  airResistance: number;
  /** Wheel rotation friction */
  wheelRotationFriction: number;
  /** Initial wheel rotation velocity */
  initialWheelVelocity: number;
}

/**
 * Physics world state and objects
 */
export interface WheelPhysics {
  /** Matter.js engine instance */
  engine: Engine;
  /** Matter.js world instance */
  world: World;
  /** Wheel body (static) - now optional since we removed it */
  wheelBody: Body | null;
  /** Ball body (dynamic) */
  ballBody: Body;
  /** Nail bodies around the wheel */
  nailBodies: Body[];
  /** Whether physics simulation is running */
  isRunning: boolean;
  /** Current angular velocity of the ball */
  ballAngularVelocity: number;
}

/**
 * PixiJS rendering objects and state
 */
export interface WheelRenderer {
  /** PixiJS application instance */
  app: Application;
  /** Main container for all wheel graphics */
  container: Container;
  /** Wheel background graphics */
  wheelGraphics: Graphics;
  /** Segment graphics container */
  segmentsContainer: Container;
  /** Text labels container */
  textContainer: Container;
  /** Nails graphics container */
  nailsContainer: Container;
  /** Ball graphics */
  ballGraphics: Graphics;
  /** Whether renderer is initialized */
  isInitialized: boolean;
}

/**
 * Spin animation state
 */
export interface SpinState {
  /** Whether wheel is currently spinning */
  isSpinning: boolean;
  /** Spin start timestamp */
  startTime: number;
  /** Spin duration in milliseconds */
  duration: number;
  /** Initial spin velocity */
  initialVelocity: number;
  /** Current spin velocity */
  currentVelocity: number;
  /** Target final position (angle) */
  targetAngle?: number;
}

/**
 * Wheel component events
 */
export interface WheelEvents {
  /** Fired when spin starts */
  'wheel-spin-start': CustomEvent<{ force: number }>;
  /** Fired during spin animation */
  'wheel-spinning': CustomEvent<{ velocity: number; angle: number }>;
  /** Fired when spin completes */
  'wheel-spin-complete': CustomEvent<{ 
    winningSegment: WheelSegment;
    finalAngle: number;
    duration: number;
  }>;
  /** Fired when ball settles in a segment */
  'wheel-result': CustomEvent<{ segment: WheelSegment }>;
  /** Fired on wheel interaction */
  'wheel-interaction': CustomEvent<{ type: 'touch' | 'click'; position: { x: number; y: number } }>;
  /** Fired on error */
  'wheel-error': CustomEvent<{ error: Error; context: string }>;
}

/**
 * Wheel data from backend (flexible for both simple mock data and full WheelItem objects)
 */
export interface WheelData {
  /** Array of wheel segments (supports both simple and full WheelItem format) */
  segments: Array<{
    id: string;
    text?: string;        // For simple format
    name?: string;        // For full WheelItem format
    description?: string; // For full WheelItem format
    percentage: number;
    color: string;
    domain?: string;      // For full WheelItem format
    base_challenge_rating?: number; // For full WheelItem format
    activity_tailored_id?: string;  // For full WheelItem format
    activityId?: string;  // For compatibility
    type?: 'tailored' | 'generic'; // Activity type
    wheel_item_id?: string; // Wheel item ID for removal operations
  }>;
  /** Optional wheel configuration overrides */
  config?: Partial<WheelConfig>;
  /** Unique identifier for this wheel instance (optional for mock data) */
  wheelId?: string;
  /** Timestamp when wheel data was created (optional for mock data) */
  createdAt?: string;
}

/**
 * Touch/mouse interaction data
 */
export interface InteractionData {
  /** Interaction type */
  type: 'touch' | 'mouse';
  /** Start position */
  startPosition: { x: number; y: number };
  /** Current position */
  currentPosition: { x: number; y: number };
  /** Interaction start time */
  startTime: number;
  /** Whether interaction is active */
  isActive: boolean;
  /** Calculated velocity */
  velocity: { x: number; y: number };
}

/**
 * Animation frame data
 */
export interface AnimationFrame {
  /** Frame timestamp */
  timestamp: number;
  /** Delta time since last frame */
  deltaTime: number;
  /** Ball position */
  ballPosition: { x: number; y: number };
  /** Ball velocity */
  ballVelocity: { x: number; y: number; magnitude: number };
  /** Ball angle relative to wheel center */
  ballAngle: number;
  /** Wheel rotation angle */
  wheelRotation: number;
  /** Wheel rotation velocity */
  wheelVelocity: number;
}

/**
 * Default wheel configuration
 */
export const DEFAULT_WHEEL_CONFIG: WheelConfig = {
  radius: 200,
  centerX: 250,
  centerY: 250,
  nailCount: 100, // FIXED: Always 100 segments total
  nailRadius: 1.6, // Smaller nails for precise segment delimiting
  ballRadius: 8, // LARGER: Twice the space between nails
  ballStartOffset: 120, // Inside wheel (radius=200, so 120 is safe)
  spinForce: 0.15, // Moderate spin force for 10-second duration
  maxSpinDuration: 12000, // 12 seconds max for 10-second target
  wheelFriction: 0.001, // Very low friction for longer spinning
  airResistance: 0.999, // Very low air resistance for longer movement
  wheelRotationFriction: 0.05, // Lower friction for longer wheel spinning (10s)
  initialWheelVelocity: 5.0, // Moderate speed for 10-second duration
};

/**
 * Color palette for wheel segments
 */
export const WHEEL_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
  '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
] as const;

/**
 * Type guard to check if an object is a WheelSegment
 */
export function isWheelSegment(obj: any): obj is WheelSegment {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    typeof obj.id === 'string' &&
    typeof obj.text === 'string' &&
    typeof obj.percentage === 'number' &&
    typeof obj.color === 'string' &&
    typeof obj.startAngle === 'number' &&
    typeof obj.endAngle === 'number' &&
    typeof obj.centerAngle === 'number'
  );
}

/**
 * Type guard to check if an object is WheelData (flexible for both simple and full objects)
 */
export function isWheelData(obj: any): obj is WheelData {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    Array.isArray(obj.segments) &&
    obj.segments.length > 0 &&
    // Check that segments have required properties (flexible for both simple and full WheelItem objects)
    obj.segments.every((segment: any) =>
      typeof segment === 'object' &&
      segment !== null &&
      typeof segment.id === 'string' &&
      (typeof segment.text === 'string' || typeof segment.name === 'string') && // Support both 'text' and 'name'
      typeof segment.percentage === 'number' &&
      typeof segment.color === 'string'
    )
    // wheelId and createdAt are optional for mock data
  );
}
