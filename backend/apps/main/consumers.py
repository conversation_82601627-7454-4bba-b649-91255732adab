import json
import asyncio
import uuid
from typing import Dict, Any, Optional, List, TypedDict, Union
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
import logging
from django.db import close_old_connections
from asgiref.sync import async_to_sync
from dataclasses import dataclass
from datetime import datetime, timezone # Added datetime

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.main.models import AgentRun
from apps.activity.models import ActivityTailored

# Import connection tracker for monitoring
try:
    from apps.admin_tools.services.connection_tracker import connection_tracker
    from apps.admin_tools.consumers import ConnectionMonitorConsumer
except ImportError:
    # Graceful fallback if admin_tools is not available
    connection_tracker = None
    ConnectionMonitorConsumer = None

# Import WebSocket Manager for scalable connection management
from apps.main.services.websocket_integration import ws_integration

logger = logging.getLogger(__name__)

# Type definitions for API messages according to the API contract
class ChatMessageContent(TypedDict):
    message: str
    user_profile_id: str
    timestamp: Optional[str]
    metadata: Optional[Dict[str, Any]]

class SpinResultContent(TypedDict):
    activity_tailored_id: str  # Maps to the ActivityTailored model ID
    name: str
    description: Optional[str]
    user_profile_id: str

class WorkflowStatusRequest(TypedDict):
    workflow_id: str

class WheelItem(TypedDict):
    id: str
    name: str
    description: str
    percentage: float
    color: str
    domain: str
    base_challenge_rating: int
    activity_tailored_id: str  # This maps to the ActivityTailored model ID

class Wheel(TypedDict):
    name: str
    items: List[WheelItem]

class WheelData(TypedDict):
    wheel: Wheel

class UserSessionConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for the Game of Life application.

    Handles real-time communication between the frontend and backend according to the API contract.
    Manages chat messages, wheel data, activity selection, and workflow status updates.
    
    API Contract: This class implements the WebSocket API contract for the Game of Life system:
    
    Client → Server Messages:
    - chat_message: User sends a text message
    - spin_result: User selects an activity from the wheel
    - workflow_status_request: Request current status of a workflow
    
    Server → Client Messages:
    - system_message: System notifications
    - chat_message: Messages from agent or system
    - processing_status: Indicates processing state
    - wheel_data: Activity wheel configuration
    - error: Error notifications
    - workflow_status: Workflow execution status
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        logger.error(f"🔧 DEBUG: UserSessionConsumer.__init__() called")

    async def connect(self):
        """
        Called when a WebSocket connection is established.

        Adds the user to a unique session group for broadcasts and accepts the connection.
        Sends a welcome message according to the API contract format.

        API Contract: Initiates connection and sends system_message
        """
        logger.info(f"🔧 UserSessionConsumer.connect() called - START")

        # Initialize user profile ID (will be set on first message)
        self.user_profile_id = None
        self.user_specific_group_name = None # Group based on user_id, joined after ID is known

        # Track active workflows
        self.active_workflows = {}

        # Use WebSocket Manager for scalable connection handling
        try:
            # Register connection with WebSocket Manager
            session_id = ws_integration.on_connect(
                consumer=self,
                user_id=None,  # Will be set when user authenticates
                metadata={
                    'connected_via': 'websocket',
                    'connection_time': datetime.now(timezone.utc).isoformat()
                }
            )

            # Store session info for backward compatibility
            self.user_ws_session_name = f"client_session_{session_id}"
            logger.info(f"🔧 Generated session name: {self.user_ws_session_name}")

            # Join the user's unique session group (UUID-based for direct comms if needed)
            await self.channel_layer.group_add(
                self.user_ws_session_name,
                self.channel_name
            )

            # Accept the WebSocket connection
            await self.accept()

            # Send a welcome message using the new helper
            await self._send_to_client_and_admin({
                'type': 'system_message',
                'content': 'Connected to the Game of Life server. Ready for your journey!'
            })

            logger.info(f"WebSocket connection established: {self.channel_name}, session: {session_id}")

            # Legacy connection tracking for backward compatibility
            if connection_tracker:
                try:
                    result = connection_tracker.track_connection(
                        session_id=session_id,
                        user_id='unknown',  # Will be updated when user_profile_id is set
                        metadata={
                            'channel_name': self.channel_name,
                            'connected_via': 'websocket'
                        }
                    )
                    logger.debug(f"Legacy connection tracker result: {result}")
                except Exception as e:
                    logger.warning(f"Legacy connection tracker failed: {e}")

            # Legacy global connection monitor for backward compatibility
            if ConnectionMonitorConsumer:
                try:
                    result = ConnectionMonitorConsumer.register_global_connection(session_id, 'unknown')
                    logger.debug(f"Legacy global connection monitor result: {result}")
                except Exception as e:
                    logger.warning(f"Legacy global connection monitor failed: {e}")

        except Exception as e:
            logger.error(f"Failed to establish WebSocket connection: {e}")
            await self.close(code=1011)  # Internal server error

    async def disconnect(self, close_code):
        """
        Called when a WebSocket connection is closed.

        Removes the user from their session group and cleans up resources.
        """
        # Use WebSocket Manager for disconnection handling
        ws_integration.on_disconnect(self)

        # Leave the session group
        if hasattr(self, 'user_ws_session_name'):
            await self.channel_layer.group_discard(
                self.user_ws_session_name,
                self.channel_name
            )

        # Leave the user-specific group if joined
        if hasattr(self, 'user_specific_group_name') and self.user_specific_group_name:
            logger.info(f"Leaving user-specific group {self.user_specific_group_name} for {self.channel_name}")
            await self.channel_layer.group_discard(
                self.user_specific_group_name,
                self.channel_name
            )

        # Cancel the heartbeat task on disconnect
        if hasattr(self, 'heartbeat_task') and self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task # Allow task to finish cancellation
            except asyncio.CancelledError:
                logger.info(f"Heartbeat task cancelled for {self.channel_name}")

        logger.info(f"WebSocket connection closed: {self.channel_name}, code: {close_code}")

        # Legacy connection tracking cleanup for backward compatibility
        if hasattr(self, 'user_ws_session_name'):
            session_id = self.user_ws_session_name.replace('client_session_', '')

            if connection_tracker:
                connection_tracker.untrack_connection(session_id)

            # Also untrack from global connection monitor
            if ConnectionMonitorConsumer:
                ConnectionMonitorConsumer.unregister_global_connection(session_id)

    async def _send_heartbeat(self):
        """Periodically sends a ping message to keep the connection alive."""
        while True:
            try:
                await asyncio.sleep(30) # Send ping every 30 seconds
                await self.send(text_data=json.dumps({'type': 'ping'}))
                logger.debug(f"Sent ping to {self.channel_name}")
            except asyncio.CancelledError:
                logger.info(f"Heartbeat task cancelled for {self.channel_name}")
                break
            except Exception as e:
                logger.error(f"Error in heartbeat task for {self.channel_name}: {e}", exc_info=True)
                # Stop the task on error to prevent infinite loops
                break

    async def receive(self, text_data):
        """
        Called when a message is received from the WebSocket.

        Parses the message and routes it to the appropriate handler based on the message type.
        Validates all incoming messages according to the API contract.

        API Contract: Handles the three defined client message types:
        - chat_message: User text messages
        - spin_result: Activity selection
        - workflow_status_request: Status queries
        """
        try:
            # Parse the JSON message
            data = json.loads(text_data)

            # --- BEGIN ADDITION ---
            # Broadcast the received message to the admin listener group *before* processing
            # We need user_profile_id first, which might be in the message content
            temp_content = data.get('content', {})
            temp_user_profile_id = self.user_profile_id or (temp_content.get('user_profile_id') if isinstance(temp_content, dict) else None)

            if temp_user_profile_id: # Only broadcast if user ID is known or provided in this message
                admin_group_name = f"admin_listener_for_user_{temp_user_profile_id}"
                admin_broadcast_payload = {
                    'type': 'admin_tester.message_broadcast',
                    'target_user_id': temp_user_profile_id,
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'direction': 'client_to_server', # Indicate message direction
                    'original_message': data # Send the parsed incoming data
                }
                try:
                    logger.debug(f"Broadcasting received message to admin group {admin_group_name}")
                    await self.channel_layer.group_send(
                        admin_group_name,
                        admin_broadcast_payload
                    )
                except Exception as e:
                    logger.error(f"Error broadcasting received message to admin group {admin_group_name}: {e}", exc_info=True)
            # --- END ADDITION ---

            message_type = data.get('type', '')
            content = data.get('content', {})

            logger.info(f"Received message: {message_type}")

            # Use WebSocket Manager for message activity tracking
            ws_integration.on_message_received(self, data)

            # Legacy tracking for backward compatibility
            if hasattr(self, 'user_ws_session_name'):
                session_id = self.user_ws_session_name.replace('client_session_', '')

                if connection_tracker:
                    connection_tracker.update_connection_activity(
                        session_id=session_id,
                        activity_data={
                            'type': 'message',
                            'message_type': message_type
                        }
                    )

                # Also track in global connection monitor
                if ConnectionMonitorConsumer:
                    ConnectionMonitorConsumer.update_global_connection_activity(session_id, {
                        'type': 'message',
                        'message_type': message_type
                    })

                    # Broadcast incoming message to monitoring dashboard
                    try:
                        logger.debug(f"Broadcasting incoming message to monitoring dashboard - session_id={session_id}, message_type={message_type}")
                        await ConnectionMonitorConsumer.broadcast_message_flow({
                            'direction': 'incoming',
                            'session_id': session_id,
                            'user_id': self.user_profile_id or temp_user_profile_id or 'unknown',
                            'message': data,
                            'timestamp': datetime.now(timezone.utc).isoformat()
                        })
                        logger.debug(f"UserSessionConsumer: Broadcasted incoming message to monitoring dashboard for session {session_id}")
                    except Exception as e:
                        logger.error(f"UserSessionConsumer: Failed to broadcast incoming message to monitoring dashboard: {e}", exc_info=True)
            
            # Set user_profile_id from the message if provided and join user-specific group
            if isinstance(content, dict) and 'user_profile_id' in content and not self.user_profile_id:
                new_user_profile_id = content['user_profile_id']
                self.user_profile_id = new_user_profile_id
                logger.info(f"Set user_profile_id to {self.user_profile_id} for channel {self.channel_name}")

                # Authenticate with WebSocket Manager
                ws_integration.on_authenticate(self, str(new_user_profile_id))

                # Join the predictable user-specific group
                self.user_specific_group_name = f"user_session_group_{new_user_profile_id}"
                logger.info(f"Adding channel {self.channel_name} to user-specific group {self.user_specific_group_name}")
                await self.channel_layer.group_add(
                    self.user_specific_group_name,
                    self.channel_name
                )

                # Legacy connection tracking for backward compatibility
                if hasattr(self, 'user_ws_session_name'):
                    session_id = self.user_ws_session_name.replace('client_session_', '')

                    # Sanitize user_id to prevent database issues
                    sanitized_user_id = str(new_user_profile_id)
                    if isinstance(new_user_profile_id, str) and new_user_profile_id.startswith('persistent-user-'):
                        sanitized_user_id = f"test-{new_user_profile_id.split('-')[-1]}"
                        logger.debug(f"Sanitized user_id from {new_user_profile_id} to {sanitized_user_id}")

                    if connection_tracker:
                        try:
                            connection_tracker.update_connection_activity(
                                session_id=session_id,
                                activity_data={
                                    'user_id': sanitized_user_id,
                                    'type': 'user_identified'
                                }
                            )
                            logger.debug(f"Updated connection_tracker with user_id={sanitized_user_id}")
                        except Exception as e:
                            logger.warning(f"Failed to update connection_tracker: {e}")

                    # Also update global connection monitor
                    if ConnectionMonitorConsumer:
                        try:
                            ConnectionMonitorConsumer.update_global_connection_activity(session_id, {
                                'user_id': sanitized_user_id,
                                'type': 'user_identified'
                            })
                            logger.debug(f"Updated global connection monitor with user_id={sanitized_user_id}")
                        except Exception as e:
                            logger.warning(f"Failed to update global connection monitor: {e}")
            elif isinstance(content, dict) and 'user_profile_id' in content and self.user_profile_id != content['user_profile_id']:
                 # Log a warning if a message arrives with a *different* user ID than expected for this connection
                 logger.warning(f"Received message with user_profile_id '{content['user_profile_id']}' but connection is associated with '{self.user_profile_id}'. Ignoring user ID change.")


            # Route the message to the appropriate handler per API contract
            if message_type == 'chat_message':
                # Validate required fields
                if not self._validate_chat_message(content):
                    await self._send_error("Invalid chat message format")
                    return
                await self.handle_chat_message(content)
            elif message_type == 'spin_result':
                # Validate required fields
                if not self._validate_spin_result(content):
                    await self._send_error("Invalid spin result format")
                    return
                await self.handle_spin_result(content)
            elif message_type == 'workflow_status_request':
                # Validate required fields
                if not isinstance(content, dict) or 'workflow_id' not in content:
                    await self._send_error("Workflow ID is required for status requests")
                    return
                await self.handle_workflow_status_request(content)
            elif message_type == 'onboarding_message':
                # This message type is deprecated per API contract but handle it for backward compatibility
                logger.warning("Received deprecated onboarding_message, handling as chat_message")
                if not isinstance(content, dict) or 'message' not in content:
                    await self._send_error("Invalid onboarding message format")
                    return
                # Convert to chat_message format and handle
                await self.handle_chat_message(content)
            else:
                logger.warning(f"Unknown message type: {message_type}")
                await self._send_error(f"Unknown message type: {message_type}")

        except json.JSONDecodeError:
            logger.error(f"Invalid JSON received: {text_data}")
            await self._send_error("Invalid message format")
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}", exc_info=True)
            await self._send_error("Server error processing your request")

    def _validate_chat_message(self, content: Any) -> bool:
        """
        Validates that a chat message contains the required fields.
        
        Args:
            content: The message content to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not isinstance(content, dict):
            return False
        
        # Message is the only required field per API contract
        return 'message' in content

    def _validate_spin_result(self, content: Any) -> bool:
        """
        Validates that a spin result contains the required fields.
        
        Args:
            content: The spin result content to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not isinstance(content, dict):
            return False
        
        # Check for either activity_tailored_id or activity_id (for backward compatibility)
        has_activity_id = 'activity_tailored_id' in content or 'activity_id' in content
        
        # Required fields per API contract
        return has_activity_id and 'name' in content

    async def _send_error(self, message: str):
        """
        Sends an error message to the client according to the API contract.
        
        Args:
            message: The error message to send
        """
        # Use the new helper to send the error message
        await self._send_to_client_and_admin({
            'type': 'error',
            'content': message
        })

    async def handle_chat_message(self, content):
        """
        Handles incoming chat messages from the user.

        Processes the message through the ConversationDispatcher to determine
        the appropriate workflow and initiate the agent processing.

        API Contract: Processes messages of type 'chat_message' from client.

        Args:
            content: A dictionary containing the message content with the following fields:
                - message: The user's message text (required)
                - user_profile_id: Unique identifier for the user (required)
                - timestamp: ISO-8601 formatted timestamp (optional)
                - metadata: Additional context information (optional)
        """
        # Extract content
        message = content.get('message', '')
        user_profile_id = self.user_profile_id or content.get('user_profile_id')

        if not user_profile_id:
            await self._send_error("User profile ID is required for chat messages")
            return
        
        # REMOVED: Echo back user message to prevent duplicates
        # The frontend already displays user messages immediately
        # The ConversationDispatcher will handle all response messaging
        
        # Notify that we're processing - SINGLE MESSAGE ONLY
        await self.channel_layer.group_send(
            self.user_ws_session_name,
            {
                'type': 'processing_status',
                'status': 'processing'
            }
        )

        try:
            # Extract LLM config ID from metadata if provided (debug mode)
            metadata = content.get('metadata', {})
            llm_config_id = metadata.get('llm_config_id')

            # Initialize the conversation dispatcher with WebSocket session
            dispatcher = ConversationDispatcher(
                user_profile_id,
                user_ws_session_name=self.user_ws_session_name,
                llm_config_id=llm_config_id
            )

            # Process the message through the dispatcher
            result = await dispatcher.process_message({
                'text': message,
                'timestamp': content.get('timestamp'),
                'metadata': metadata
            })
            
            # Track the workflow if initiated
            if 'workflow_id' in result:
                self.active_workflows[result['workflow_id']] = {
                    'type': result.get('workflow_type'),
                    'status': result.get('status'),
                    'initiated_at': result.get('session_timestamp')
                }
                
                # Send workflow status update via group send for consistency
                await self.channel_layer.group_send(
                    self.user_ws_session_name,
                    {
                        'type': 'workflow_status',
                        'workflow_id': result['workflow_id'],
                        'status': result.get('status', 'initiated') # Use status from result if available
                    }
                )
            
            # --- REMOVED SENDING PLACEHOLDER MESSAGE ---
            # The final agent response will be sent asynchronously via EventService
            # from the workflow itself (e.g., run_discussion_workflow).
            # We only send status updates here.
            # --- END REMOVAL ---

        except Exception as e:
            # If an exception occurs *within the consumer itself* (outside the dispatcher call),
            # log it, send a structured error message to the client, and emit a debug event.
            logger.error(f"Consumer error handling chat message: {str(e)}", exc_info=True)
            # Send a user-facing error message
            await self._send_error("A server error occurred while handling your message.")
            # Emit a detailed debug event using EventService
            from apps.main.services.event_service import EventService # Import locally
            import traceback
            tb_str = traceback.format_exc()
            await EventService.emit_debug_info(
                level='error',
                message=f"Consumer error handling chat message: {str(e)}",
                source='UserSessionConsumer',
                details={'exception_type': type(e).__name__, 'traceback': tb_str},
                user_profile_id=self.user_profile_id,
                session_id=self.user_ws_session_name
            )
            # --- END CHANGE ---

    def _generate_workflow_init_message(self, workflow_result: Dict[str, Any]) -> str:
        """
        Generate a user-friendly message based on workflow type.

        The workflow types match those defined in the ConversationDispatcher
        and DispatcherAgentNode classes.

        Args:
            workflow_result: The workflow result containing workflow_type

        Returns:
            str: A human-readable message indicating what's happening
        """
        workflow_type = workflow_result.get('workflow_type')

        # These workflow types are defined in DispatcherAgentNode
        # See the _apply_classification_rules and _estimate_completion_time methods
        messages = {
            'wheel_generation': "[wheel_generation] I'm putting together some activity suggestions for you. This should take just a moment...",
            'activity_feedback': "[activity_feedback] I'm processing your feedback on that activity. Let me analyze that...",
            'pre_spin_feedback': "[pre_spin_feedback] Thanks for sharing your current state. I'll take that into account...",
            'post_spin': "[post_spin] I'm preparing information about your selected activity...",
            'user_onboarding': "[user_onboarding] Let's get to know each other better. I'm processing your information...",
            'progress_review': "[progress_review] I'm analyzing your progress so far. Let me gather some insights...",
            'discussion': "[discussion] Okay, tell me more if you'd like..." # Added default discussion message
        }

        return messages.get(workflow_type, "I'm processing your request...")

    # --- Handler for messages injected from admin tester view ---
    async def admin_message(self, event):
        """
        Handles messages sent from the admin_tools websocket_tester view.
        The 'event' dict contains:
        {
            'type': 'admin.message',
            'text': message_json_string # The original JSON string from the view
        }
        """
        logger.info(f"UserSessionConsumer: admin_message handler invoked for channel {self.channel_name}. Event text: {event.get('text')}")
        text_data = event.get('text')
        if text_data:
            # Call the main receive method to process the message as if it came from the client WS
            await self.receive(text_data=text_data)
        else:
            logger.warning(f"UserSessionConsumer: admin_message handler received empty text data.")

    # Note: The original system_message handler logic was moved to _generate_workflow_init_message
    # This handler now correctly processes system messages sent via the channel layer.
    async def system_message(self, event):
        """
        Sends a system message to the WebSocket according to API contract.

        API Contract: Messages follow format:
        {
          "type": "system_message",
          "content": "string"
        }

        Args:
            event: The event data containing:
                - content: The system message text
        """
        # Use the new helper
        await self._send_to_client_and_admin({
            'type': 'system_message',
            'content': event['content']
        })

    async def handle_spin_result(self, content):
        """
        Handles the result of a wheel spin from the frontend.
        
        Processes the selected activity through the agent system.
        
        API Contract: Processes messages of type 'spin_result' from client.
        
        Args:
            content: A dictionary containing the spin result with the following fields:
                - activity_tailored_id: Unique identifier for the selected activity (required)
                - name: Display name of the activity (required)
                - description: Activity description (optional)
                - user_profile_id: Unique identifier for the user (required)
        """
        # Support both activity_tailored_id (new) and activity_id (backward compatibility)
        activity_id = content.get('activity_tailored_id') or content.get('activity_id')
        activity_name = content.get('name')
        activity_description = content.get('description', '')
        user_profile_id = self.user_profile_id or content.get('user_profile_id')
        
        if not user_profile_id:
            await self._send_error("User profile ID is required for spin results")
            return
        
        logger.info(f"Received spin result for activity: {activity_name} (ID: {activity_id})")
        
        # Notify that we're processing the selection
        await self.channel_layer.group_send(
            self.user_ws_session_name,
            {
                'type': 'processing_status',
                'status': 'processing'
            }
        )
        
        try:
            # Initialize the conversation dispatcher with WebSocket session
            dispatcher = ConversationDispatcher(
                user_profile_id, 
                user_ws_session_name=self.user_ws_session_name
            )
            
            # Process the spin result through the dispatcher
            # Use type: 'spin_result' to trigger the post_spin workflow type
            result = await dispatcher.process_message({
                'text': f"I've selected the activity: {activity_name}",
                'metadata': {
                    'type': 'spin_result',
                    'activity_id': activity_id,  # The dispatcher uses activity_id
                    'activity_name': activity_name
                },
                'original_content': content  # Pass the original spin_result content
            })

            # DON'T send immediate response here - let the workflow handle it
            # The post-spin workflow will send a consolidated mentor response
            # This prevents duplicate messages

            # Track the workflow and send status update LAST
            if 'workflow_id' in result:
                self.active_workflows[result['workflow_id']] = {
                    'type': result.get('workflow_type', 'post_spin'),
                    'status': result.get('status'),
                    'initiated_at': result.get('session_timestamp')
                }

                # Send workflow status update via group send for consistency
                await self.channel_layer.group_send(
                    self.user_ws_session_name,
                    {
                        'type': 'workflow_status',
                        'workflow_id': result['workflow_id'],
                        'status': result.get('status', 'initiated') # Use status from result if available
                    }
                )

        except Exception as e:
            # --- BEGIN CHANGE ---
            # Consistent error handling for spin result handler
            logger.error(f"Consumer error handling spin result: {str(e)}", exc_info=True)
            # Send a user-facing error message
            await self._send_error("A server error occurred while handling your activity selection.")
            # Emit a detailed debug event using EventService
            from apps.main.services.event_service import EventService # Import locally
            import traceback
            tb_str = traceback.format_exc()
            await EventService.emit_debug_info(
                level='error',
                message=f"Consumer error handling spin result: {str(e)}",
                source='UserSessionConsumer',
                details={'exception_type': type(e).__name__, 'traceback': tb_str},
                user_profile_id=self.user_profile_id,
                session_id=self.user_ws_session_name
            )
            # --- END CHANGE ---
    
    async def handle_workflow_status_request(self, content):
        """
        Handle requests for workflow status updates.
        
        API Contract: Processes messages of type 'workflow_status_request' from client.
        
        Args:
            content: A dictionary containing the request with the following fields:
                - workflow_id: Unique identifier for the workflow to check
        """
        workflow_id = content.get('workflow_id')
        
        if not workflow_id:
            await self._send_error("Workflow ID is required for status requests")
            return
            
        # Check if we have this workflow in our tracking
        workflow_status = 'unknown'
        if workflow_id in self.active_workflows:
            workflow_status = self.active_workflows[workflow_id]['status']
        else:
            # Try to get the status from the database
            try:
                workflow_status = await self._check_workflow_status_from_db(workflow_id)
            except Exception as e:
                logger.error(f"Error checking workflow status from DB: {str(e)}", exc_info=True)
                workflow_status = 'unknown' # Default to unknown on error

        # Send status update via group send for consistency
        await self.channel_layer.group_send(
            self.user_ws_session_name,
            {
                'type': 'workflow_status',
                'workflow_id': workflow_id,
                'status': workflow_status
            }
        )
    
    @database_sync_to_async
    def _check_workflow_status_from_db(self, workflow_id):
        """Check workflow status from the database"""
        # Close old connections before accessing the DB
        close_old_connections()
        
        # Look for runs with this workflow ID
        latest_run = AgentRun.objects.filter(
            workflow_id=workflow_id
        ).order_by('-completed_at').first()
        
        if latest_run:
            return latest_run.status
        return 'unknown'

    async def _send_to_client_and_admin(self, message_payload: Dict[str, Any]):
        """
        Helper method to send a message payload to the connected client
        and broadcast a copy to the admin listener group if user_profile_id is set.
        Also broadcasts to real-time monitoring dashboard for session focus and message inspection.
        """
        try:
            # 1. Send to the connected client
            json_string = json.dumps(message_payload)
            await self.send(text_data=json_string)

            # 2. Get session ID for monitoring
            session_id = self.user_ws_session_name.replace('client_session_', '') if self.user_ws_session_name else None

            # 3. Broadcast to real-time monitoring dashboard
            if session_id and ConnectionMonitorConsumer:
                try:
                    logger.info(f"🔧 DEBUG: Broadcasting outgoing message to monitoring dashboard - session_id={session_id}, message_type={message_payload.get('type')}")

                    # Broadcast to session monitoring (for focused sessions)
                    await ConnectionMonitorConsumer.broadcast_session_message(session_id, message_payload)
                    logger.info(f"🔧 DEBUG: ✅ broadcast_session_message completed")

                    # Broadcast to message inspector (for all messages)
                    await ConnectionMonitorConsumer.broadcast_message_flow({
                        'direction': 'outgoing',
                        'session_id': session_id,
                        'user_id': self.user_profile_id or 'unknown',
                        'message': message_payload,
                        'timestamp': datetime.now(timezone.utc).isoformat()
                    })
                    logger.info(f"🔧 DEBUG: ✅ broadcast_message_flow completed")

                    logger.debug(f"UserSessionConsumer: Broadcasted message to monitoring dashboard for session {session_id}")
                except Exception as e:
                    logger.error(f"UserSessionConsumer: Failed to broadcast to monitoring dashboard: {e}", exc_info=True)

            # 4. Broadcast to admin listener group (if user ID is known)
            if self.user_profile_id:
                admin_group_name = f"admin_listener_for_user_{self.user_profile_id}"
                admin_broadcast_payload = {
                    # Use a specific type for the admin consumer to handle
                    'type': 'admin_tester.message_broadcast',
                    'target_user_id': self.user_profile_id,
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'direction': 'server_to_client',
                    'original_message': message_payload # Send the original payload
                }
                try:
                    await self.channel_layer.group_send(
                        admin_group_name,
                        admin_broadcast_payload
                    )
                    # Add log after successful group_send
                    logger.info(f"UserSessionConsumer: Successfully sent broadcast to admin group {admin_group_name} for user {self.user_profile_id}.")
                except Exception as e:
                    # Log error but don't prevent sending to client
                    logger.error(f"UserSessionConsumer: FAILED sending broadcast to admin group {admin_group_name}: {e}", exc_info=True) # Make error more specific

        except Exception as e:
            logger.error(f"UserSessionConsumer: Error sending message directly to client {self.channel_name}: {e}", exc_info=True) # Clarify error source


    # Channel layer message handlers - These convert internal channel messages to WebSocket messages
    
    async def chat_message(self, event):
        """
        Sends a chat message to the WebSocket according to API contract.

        This is called by the channel layer when a message is broadcast to the group.
        
        API Contract: Messages follow format:
        {
          "type": "chat_message",
          "content": "string",
          "is_user": boolean
        }
        
        Args:
            event: The event data containing:
                - content or user_message: The message text
                - is_user: Whether the message is from the user
        """
        logger.info(f"UserSessionConsumer: chat_message handler invoked for channel {self.channel_name}. Event: {event}") # <-- Add log here
        # Support both field names for backward compatibility
        # The new field name is 'content' per API contract
        message_content = event.get('content', event.get('user_message', ''))
        
        # Use the new helper
        await self._send_to_client_and_admin({
            'type': 'chat_message',
            'content': message_content,
            'is_user': event.get('is_user', False)
        })

    async def system_message(self, event):
        """
        Sends a system message to the WebSocket according to API contract.
        
        API Contract: Messages follow format:
        {
          "type": "system_message",
          "content": "string"
        }
        
        Args:
            event: The event data containing:
                - content: The system message text
        """
        # Use the new helper
        await self._send_to_client_and_admin({
            'type': 'system_message',
            'content': event['content']
        })

    async def processing_status(self, event):
        """
        Sends processing status updates to the WebSocket according to API contract.
        
        API Contract: Messages follow format:
        {
          "type": "processing_status",
          "status": "processing|completed|error"
        }
        
        Args:
            event: The event data containing:
                - status: Current processing status
        """
        # Use the new helper
        await self._send_to_client_and_admin({
            'type': 'processing_status',
            'status': event['status']
        })

    async def wheel_data(self, event):
        """
        Sends wheel data to the WebSocket according to API contract.
        
        API Contract: Messages follow format:
        {
          "type": "wheel_data",
          "wheel": {
            "name": "string",
            "items": [
              {
                "id": "string",
                "name": "string",
                "description": "string",
                "percentage": 15.5,
                "color": "#66BB6A",
                "domain": "string",
                "base_challenge_rating": 35,
                "activity_tailored_id": "string"
              }
            ]
          }
        }
        
        Args:
            event: The event data containing:
                - wheel: The wheel configuration data
        """
        # Get the wheel data from the event
        wheel = event.get('wheel')
        
        # If the wheel data is present, validate and send it
        if wheel:
            # Validate wheel before sending
            wheel = await self._validate_wheel_data(wheel)
            # Use the new helper
            await self._send_to_client_and_admin({
                'type': 'wheel_data',
                'wheel': wheel
            })
        else:
            # Log error if wheel data is missing
            logger.error("Received wheel_data event without wheel data")
    
    async def _validate_wheel_data(self, wheel_data):
        """
        Validates and transforms wheel data to match the API contract format.

        Ensures the wheel data contains all required fields and formats
        according to the API contract.

        API Contract: Ensures wheel_data follows:
        {
          "name": "string",
          "items": [
            {
              "id": "string",
              "name": "string",
              "description": "string",
              "percentage": 15.5,
              "color": "#66BB6A",
              "domain": "string",
              "base_challenge_rating": 35,
              "activity_tailored_id": "string"
            }
          ]
        }

        Args:
            wheel_data: The wheel data from the workflow output

        Returns:
            dict: Validated and transformed wheel data
        """
        if not isinstance(wheel_data, dict):
            wheel_data = {'name': 'Activity Wheel', 'items': []}

        # Handle wheel data from WheelAndActivityAgent which has separate 'activities' and 'items' fields
        if 'activities' in wheel_data and 'items' in wheel_data:
            logger.info(f"🔧 ARCHITECTURAL FIX: Converting wheel data with {len(wheel_data.get('activities', []))} activities and {len(wheel_data.get('items', []))} items")
            logger.info(f"🔧 ARCHITECTURAL FIX: Using database wheel item IDs instead of generating new ones")

            # Merge activities and items data
            activities = wheel_data.get('activities', [])
            items = wheel_data.get('items', [])

            # Create merged items array
            merged_items = []
            for i, activity in enumerate(activities):
                if not isinstance(activity, dict):
                    continue

                # Find corresponding item data
                item_data = {}
                if i < len(items) and isinstance(items[i], dict):
                    item_data = items[i]

                # CRITICAL FIX: Use the actual database wheel item ID from item_data
                # The wheel_generation_graph.py already provides the correct wheel item ID
                wheel_item_id = item_data.get('id')

                if not wheel_item_id:
                    # Fallback only if no ID provided (should not happen with proper workflow)
                    wheel_item_id = f'item_{i}_{activity.get("id", f"activity-{i}")[:8]}'
                    logger.warning(f"🚨 No wheel item ID provided, using fallback: {wheel_item_id}")
                else:
                    logger.debug(f"✅ Using database wheel item ID: {wheel_item_id}")

                # Create merged item with activity data and wheel-specific fields
                merged_item = {
                    'id': wheel_item_id,  # Use wheel item ID for removal operations
                    'name': activity.get('name', activity.get('title', f'Activity {i+1}')),
                    'description': activity.get('description', ''),
                    'instructions': activity.get('instructions', ''),
                    'percentage': item_data.get('percentage', 100.0 / len(activities)),
                    'color': item_data.get('color', self._get_default_color(i)),
                    'domain': activity.get('domain', 'general'),
                    'base_challenge_rating': activity.get('challenge_level', 50),
                    'activity_tailored_id': activity.get('id', f'activity-{i}'),  # Keep activity ID separate
                    'duration_minutes': activity.get('duration', activity.get('duration_minutes', 20)),
                    'resources_required': activity.get('resources_required', [])
                }
                merged_items.append(merged_item)
                logger.debug(f"🔧 Using wheel item: ID={wheel_item_id}, Activity={activity.get('id')}, Name={activity.get('name', 'Unknown')}")

            # Update wheel_data with merged items
            wheel_data['items'] = merged_items

            # Remove the separate activities field to avoid confusion
            if 'activities' in wheel_data:
                del wheel_data['activities']

        # Extract name from metadata if available
        if 'metadata' in wheel_data and isinstance(wheel_data['metadata'], dict):
            if 'name' not in wheel_data:
                wheel_data['name'] = wheel_data['metadata'].get('name', 'Activity Wheel')

        # Ensure required fields
        if 'name' not in wheel_data:
            wheel_data['name'] = "Activity Wheel"

        if 'items' not in wheel_data:
            wheel_data['items'] = []
            
        # Validate and transform each item
        for i, item in enumerate(wheel_data.get('items', [])):
            if not isinstance(item, dict):
                continue
                
            # Ensure required fields exist
            if 'id' not in item:
                # CRITICAL FIX: This should not happen with proper workflow, but provide fallback
                activity_id = item.get('activity_tailored_id', item.get('activity_id', f'activity-{i}'))
                item['id'] = f"item_{i}_{str(activity_id)[:8]}"
                logger.warning(f"🚨 Wheel item missing ID, using fallback: {item['id']}")
                
            if 'name' not in item:
                # If no name, this item is invalid and should be skipped
                logger.warning(f"Wheel item {i} missing 'name' field, using default")
                item['name'] = f"Activity {i+1}"
            
            # Add default values for missing fields per API contract
            item.setdefault('description', '')
            item.setdefault('percentage', 0.0)
            
            # Default color based on index for visual variety
            colors = ["#66BB6A", "#42A5F5", "#7E57C2", "#FFA726", "#EC407A", "#26A69A"]
            item.setdefault('color', colors[i % len(colors)])
            
            # Handle activity_id to activity_tailored_id mapping
            if 'activity_id' in item and 'activity_tailored_id' not in item:
                # If this is already using the new field name format, keep it
                # Otherwise, copy the value to the correct field name
                item['activity_tailored_id'] = item['activity_id']
                
            # Get primary domain from activity if available
            activity_id = item.get('activity_tailored_id', None) or item.get('activity_id', None)
            if activity_id and 'domain' not in item:
                try:
                    activity = await database_sync_to_async(
                        lambda: ActivityTailored.objects.filter(id=activity_id).first()
                    )()
                    if activity:
                        domain = await database_sync_to_async(activity.get_primary_domain)()
                        if domain:
                            item['domain'] = domain.code
                        else:
                            item['domain'] = ""
                    else:
                        item['domain'] = ""
                except Exception as e:
                    logger.error(f"Error getting domain for activity {activity_id}: {str(e)}")
                    item['domain'] = ""
            else:
                item.setdefault('domain', "")
            
            # Default challenge rating
            item.setdefault('base_challenge_rating', 50)
            
            # Ensure activity_tailored_id exists, use id as fallback
            item.setdefault('activity_tailored_id', item.get('id'))
            
            # Remove old activity_id field if it exists to avoid confusion
            if 'activity_id' in item:
                del item['activity_id']
            
        return wheel_data

    def _get_default_color(self, index):
        """Get a default color for wheel items based on index."""
        colors = ["#66BB6A", "#42A5F5", "#7E57C2", "#FFA726", "#EC407A", "#26A69A", "#FF7043", "#9CCC65"]
        return colors[index % len(colors)]

    async def workflow_status(self, event):
        """
        Sends workflow status updates to the WebSocket.
        
        API Contract: Messages follow format:
        {
          "type": "workflow_status",
          "workflow_id": "string",
          "status": "initiated|completed|failed|unknown"
        }
        
        Args:
            event: The event data containing:
                - workflow_id: The workflow ID
                - status: Current workflow status
        """
        workflow_id = event.get('workflow_id')
        status = event.get('status')
        
        if workflow_id and status:
            # Update internal tracking if we know about this workflow
            if workflow_id in self.active_workflows:
                self.active_workflows[workflow_id]['status'] = status
            
            # Send status update using the new helper
            await self._send_to_client_and_admin({
                'type': 'workflow_status',
                'workflow_id': workflow_id,
                'status': status
            })
        else:
            logger.warning("Received incomplete workflow_status event")
    
    async def error(self, event):
        """
        Sends error notifications to the WebSocket.
        
        API Contract: Messages follow format:
        {
          "type": "error",
          "content": "string"
        }
        
        Args:
            event: The event data containing:
                - content: Error message to display
        """
        # Use the new helper
        await self._send_to_client_and_admin({
            'type': 'error',
            'content': event.get('content', "An error occurred")
        })
    
    async def activity_details(self, event):
        """
        Sends activity details to the WebSocket.
        
        API Contract: Messages follow format:
        {
          "type": "activity_details",
          "details": {
            // Activity details object
          }
        }
        
        Args:
            event: The event data containing:
                - details: Activity details object
        """
        # Use the new helper
        await self._send_to_client_and_admin({
            'type': 'activity_details',
                'details': event.get('details', {})
        })

    async def conversation_state_update(self, event):
        """
        Handle conversation state update messages.

        API Contract: Messages follow format:
        {
          "type": "conversation_state_update",
          "updates": {
            "phase": "string",
            "awaiting_response_type": "string",
            "last_workflow": "string",
            "context": {}
          }
        }

        Args:
            event: The event data containing:
                - updates: Dictionary of state updates
        """
        # Use the new helper to send state updates to client
        await self._send_to_client_and_admin({
            'type': 'conversation_state_update',
            'updates': event.get('updates', {})
        })

    async def debug_info(self, event):
        """
        Sends internal debug information to staff users only.

        API Contract: Messages follow format:
        {
          "type": "debug_info",
          "content": {
            "timestamp": "string",
            "source": "string",
            "level": "info|warning|error|debug",
            "message": "string",
            "details": {}
          }
        }

        Args:
            event: The event data containing the debug content.
                   Expected keys in event['content']: timestamp, source, level, message, details (optional)
        """
        # TODO: [Temporary Debug] Remove/comment out the staff check to allow debug messages for any user during development.
        # user = self.scope.get('user')
        # if user and hasattr(user, 'is_staff') and user.is_staff:
        # Always send for now, assuming the check will be restored later
        if True: # Temporarily bypass staff check
            user = self.scope.get('user') # Still get user for logging if possible
            username = getattr(user, 'username', 'Unknown/Anonymous')
            logger.debug(f"[Temporary Debug] Bypassing staff check for user '{username}'. Sending debug_info for channel {self.channel_name}.")

            debug_content = event.get('content', {})

            # Construct the message payload according to the API contract
            payload = {
                'type': 'debug_info',
                'content': {
                    'timestamp': debug_content.get('timestamp', datetime.now(timezone.utc).isoformat()),
                    'source': debug_content.get('source', 'Unknown'),
                    'level': debug_content.get('level', 'info'),
                    'message': debug_content.get('message', 'No message provided.'),
                    'details': debug_content.get('details', {}) # Include optional details
                }
            }
            
            try:
                # Send directly to this specific client connection
                # Do NOT use _send_to_client_and_admin as this is staff-only info
                await self.send(text_data=json.dumps(payload))
                # Use the safe 'username' variable retrieved earlier
                logger.info(f"Sent debug_info to staff user '{username}' on channel {self.channel_name}") 
            except Exception as e:
                logger.error(f"[Temporary Debug] Error sending debug_info (staff check bypassed) for user '{username}' on channel {self.channel_name}: {e}", exc_info=True)
        # else: # Temporarily commented out the 'else' block
            # Log if user is not staff or not found, but don't send anything
            # username = getattr(user, 'username', 'Anonymous')
            # logger.debug(f"Skipping debug_info for non-staff user '{username}' or unauthenticated user on channel {self.channel_name}.")

    async def onboarding_response(self, event):
        """
        Handles onboarding responses from Celery tasks.
        
        This is a legacy handler for backward compatibility.
        
        Args:
            event: The event data containing:
                - mentor_response: The mentor's response message
                - task_id: The Celery task ID
                - status: Task status
        """
        mentor_response = event.get('mentor_response', '')
        status = event.get('status', 'completed')
        
        # Send as regular chat message
        await self.channel_layer.group_send(
            self.user_ws_session_name,
            {
                'type': 'chat_message',
                'content': mentor_response,
                'is_user': False
            }
        )
        
        # Send processing status
        await self.channel_layer.group_send(
            self.user_ws_session_name,
            {
                'type': 'processing_status',
                'status': 'completed' if status == 'completed' else 'error'
            }
        )

    async def progress_update(self, event):
        """
        Sends real-time progress updates to the WebSocket.

        API Contract: Messages follow format:
        {
          "type": "progress_update",
          "data": {
            "tracker_id": "string",
            "stage_id": "string",
            "stage_name": "string",
            "stage": "initializing|processing|executing|completing|completed|error",
            "progress_percent": number,
            "message": "string",
            "timestamp": "ISO string",
            "priority": "low|normal|high|critical",
            "workflow_type": "string",
            "metrics": {
              "duration_ms": number,
              "tokens_used": number,
              "cost_estimate": number,
              "memory_usage_mb": number,
              "cpu_usage_percent": number,
              "custom_metrics": {}
            }
          }
        }
        """
        await self._send_to_client_and_admin({
            'type': 'progress_update',
            'data': event.get('data', {})
        })

    async def progress_monitoring_update(self, event):
        """
        Sends progress monitoring updates for admin dashboard.
        """
        # Only send to admin monitoring group
        await self.channel_layer.group_send(
            "progress_monitoring",
            {
                'type': 'send_monitoring_data',
                'data': event.get('data', {})
            }
        )

    async def performance_metrics(self, event):
        """
        Sends detailed performance metrics to the WebSocket.

        API Contract: Messages follow format:
        {
          "type": "performance_metrics",
          "data": {
            "tracker_id": "string",
            "workflow_type": "string",
            "total_duration_ms": number,
            "stage_breakdown": [...],
            "performance_score": number,
            "bottlenecks": [...],
            "recommendations": [...]
          }
        }
        """
        await self._send_to_client_and_admin({
            'type': 'performance_metrics',
            'data': event.get('data', {})
        })

    async def stage_timing(self, event):
        """
        Sends stage timing data to the WebSocket.

        API Contract: Messages follow format:
        {
          "type": "stage_timing",
          "data": {
            "tracker_id": "string",
            "stage_id": "string",
            "stage_name": "string",
            "start_time": "ISO string",
            "end_time": "ISO string",
            "duration_ms": number,
            "expected_duration_ms": number,
            "performance_ratio": number,
            "timing_category": "fast|normal|slow|critical"
          }
        }
        """
        await self._send_to_client_and_admin({
            'type': 'stage_timing',
            'data': event.get('data', {})
        })

    async def workflow_progress(self, event):
        """
        Sends comprehensive workflow progress to the WebSocket.

        API Contract: Messages follow format:
        {
          "type": "workflow_progress",
          "data": {
            "workflow_id": "string",
            "workflow_type": "string",
            "overall_progress": number,
            "current_stage": "string",
            "stages_completed": [...],
            "stages_remaining": [...],
            "estimated_completion_time_ms": number,
            "performance_indicators": {...},
            "real_time_metrics": {...}
          }
        }
        """
        await self._send_to_client_and_admin({
            'type': 'workflow_progress',
            'data': event.get('data', {})
        })

    async def send_monitoring_data(self, event):
        """
        Helper method to send monitoring data to admin dashboard.
        """
        await self.send(text_data=json.dumps({
            'type': 'progress_monitoring_update',
            'data': event.get('data', {})
        }))

    async def observability_events(self, event):
        """
        Send observability events to WebSocket.

        API Contract: Messages follow format:
        {
          "type": "observability_events",
          "events": [
            {
              "event_id": "string",
              "event_type": "workflow_start|node_start|...",
              "timestamp": number,
              "trace_id": "string",
              "span_id": "string",
              "component": "langgraph|celery|django|llm",
              "operation": "string",
              "duration_ms": number,
              "severity": "debug|info|warning|error|critical",
              "metadata": {},
              "tags": {},
              "metrics": {}
            }
          ]
        }
        """
        await self.send(text_data=json.dumps({
            'type': 'observability_events',
            'events': event.get('events', [])
        }))
