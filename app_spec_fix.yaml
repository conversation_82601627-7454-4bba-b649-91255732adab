alerts:
- rule: DEPLOYMENT_FAILED
- rule: DOMAIN_FAILED
features:
- buildpack-stack=ubuntu-22
ingress:
  rules:
  - component:
      name: goali-backend
    match:
      path:
        prefix: /
  - component:
      name: goali-backend2
    match:
      path:
        prefix: /goali-backend2
name: monkfish-app
region: lon
services:
- dockerfile_path: backend/Dockerfile
  envs:
  - key: PORT
    scope: RUN_TIME
    value: "8080"
  github:
    branch: gguine/prod-slow
    deploy_on_push: true
    repo: elgui/goali
  http_port: 8080
  instance_count: 2
  instance_size_slug: apps-s-1vcpu-1gb
  name: goali-backend
  source_dir: backend
- environment_slug: python
  envs:
  - key: DJANGO_SECRET_KEY
    scope: RUN_AND_BUILD_TIME
    value: tDTGVJKlzGke7RQydZd6OJvVzVibKzp5bLf3ZPqjsk7s6LyF2PxeYwDk084XPvDW
  - key: STATIC_ROOT
    scope: RUN_AND_BUILD_TIME
    value: /app/staticfiles
  - key: DISABLE_COLLECTSTATIC
    scope: RUN_AND_BUILD_TIME
    value: "1"
  github:
    branch: gguine/prod-slow
    deploy_on_push: true
    repo: elgui/goali
  http_port: 8080
  instance_count: 2
  instance_size_slug: apps-s-1vcpu-1gb
  name: goali-backend2
  run_command: gunicorn --worker-tmp-dir /dev/shm --bind 0.0.0.0:8080 config.wsgi:application
  source_dir: backend
