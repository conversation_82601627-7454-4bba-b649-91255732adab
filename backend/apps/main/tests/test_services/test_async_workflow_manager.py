from unittest.mock import MagicMock

import pytest

from apps.main.services.async_workflow_manager import WorkflowBenchmarker
# These imports are for the patch paths
from apps.main.models import BenchmarkRun, BenchmarkScenario


class TestWorkflowBenchmarker:
    @pytest.mark.django_db
    def test_create_benchmark_run_sync_with_raw_results_as_list(
        self,
        monkeypatch,  # Use monkeypatch for better parallel execution isolation
    ):
        # Arrange
        manager = WorkflowBenchmarker()

        # Mock scenario object with Django model attributes
        mock_scenario = MagicMock(spec=BenchmarkScenario)
        mock_scenario.id = 1
        mock_scenario.name = "Test Scenario"
        # Add Django model _state attribute to prevent AttributeError
        mock_scenario._state = MagicMock()
        mock_scenario._state.db = 'default'

        raw_results_as_list = [{"some_key": "some_value"}] # This is the crucial part

        # Mock the return value of BenchmarkRun.objects.create
        mock_benchmark_run = MagicMock(spec=BenchmarkRun)
        mock_benchmark_run.id = 1
        mock_benchmark_run._state = MagicMock()
        mock_benchmark_run._state.db = 'default'

        # Create mock for BenchmarkRun.objects.create using monkeypatch
        mock_benchmark_run_create = MagicMock(return_value=mock_benchmark_run)
        monkeypatch.setattr('apps.main.models.BenchmarkRun.objects.create', mock_benchmark_run_create)

        # Act & Assert
        # With our defensive programming, this should NOT raise an error anymore
        # Instead, the list should be converted to an empty dict
        try:
            manager._create_benchmark_run_sync(
                scenario=mock_scenario,
                raw_results=raw_results_as_list, # Here is the list
            )
            # If we get here, the defensive programming worked
            print("Defensive programming successfully handled list input")
            # Verify that BenchmarkRun.objects.create was called with converted dict
            mock_benchmark_run_create.assert_called_once()
        except AttributeError as e:
            if "'list' object has no attribute 'get'" in str(e):
                pytest.fail("Defensive programming failed - list was not converted to dict")
            else:
                # Some other AttributeError, re-raise
                raise