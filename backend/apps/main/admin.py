from django.contrib import admin
from django import forms
from django.utils.html import format_html
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from import_export.widgets import ForeignKeyWidget, ManyToManyWidget
from .resources import LLMConfigResource # Import the resource
from import_export.fields import Field
# Import the custom admin site instance
from config.admin import admin_site
from django.urls import reverse
from django.core.serializers.json import DjangoJSONEncoder
import json
from django.db.models import Count

# Removed imports for custom format: base_formats, tablib, ImportExportBaseError

from apps.main.agents.tools.tools_util import sync_tool_registry_with_database

from .models import (
    AgentRole, GenericAgent, AgentTool, BenchmarkMetric, AgentRun, AgentMetric,
    AgentMemory, AgentRecommendation, CustomAgent, AgentGoal,
    BenchmarkScenario, BenchmarkRun, BenchmarkTag, EvaluationCriteriaTemplate, LLMConfig, BetaSignup # Added LLMConfig and BetaSignup
)

# --- Resources for Import/Export ---

class BenchmarkScenarioResource(resources.ModelResource):
    # Handle ManyToMany relationship with BenchmarkTag using tag names
    tags = Field(
        attribute='tags',
        widget=ManyToManyWidget(BenchmarkTag, field='name', separator=',')
    )
    # Handle ForeignKey relationship with parent_scenario using its ID
    parent_scenario = Field(
        attribute='parent_scenario',
        widget=ForeignKeyWidget(BenchmarkScenario, 'pk') # Use primary key (id)
    )
    # Handle ManyToMany relationship with variations using scenario IDs
    variations = Field(
        attribute='variations',
        widget=ManyToManyWidget(BenchmarkScenario, field='pk', separator=',') # Use primary key (id)
    )

    class Meta:
        model = BenchmarkScenario
        # Specify fields to include in export/import
        fields = ('id', 'name', 'description', 'agent_role', 'input_data', 'metadata', 'is_active', 'version', 'parent_scenario', 'tags', 'variations')
        # Exclude fields managed automatically or less relevant for direct import/export
        export_order = fields # Maintain order
        skip_unchanged = True
        report_skipped = True
        # Use database transactions for imports
        use_transactions = True
        # Define how to identify existing records for updates (using name and version)
        import_id_fields = ('name', 'version')

    # Clean JSON fields before import
    def before_import_row(self, row, **kwargs):
        for field_name in ['input_data', 'metadata']:
            if field_name in row and isinstance(row[field_name], str):
                try:
                    row[field_name] = json.loads(row[field_name])
                except json.JSONDecodeError:
                    # Handle potential errors or leave as string if invalid JSON is acceptable
                    pass # Or raise an error

    # Ensure is_latest is handled correctly after import/update
    def after_save_instance(self, instance, using_transactions, dry_run):
        if not dry_run:
            # If this instance is marked active and is the highest version for its name, mark it as latest
            latest_for_name = BenchmarkScenario.objects.filter(name=instance.name).order_by('-version').first()
            if latest_for_name and latest_for_name.pk == instance.pk:
                # Unset is_latest for other versions of the same scenario
                BenchmarkScenario.objects.filter(name=instance.name).exclude(pk=instance.pk).update(is_latest=False)
                # Set is_latest for this instance if it wasn't already
                if not instance.is_latest:
                    instance.is_latest = True
                    instance.save(update_fields=['is_latest'])
            elif instance.is_latest: # If it's marked latest but isn't the highest version
                instance.is_latest = False
                instance.save(update_fields=['is_latest'])


# --- Custom admin forms ---
class JSONSchemaWidget(forms.Textarea):
    """Custom widget for better JSON schema editing"""
    def __init__(self, attrs=None):
        default_attrs = {
            'rows': 10,
            'cols': 80,
            'class': 'vLargeTextField code-editor json-schema'
        }
        if attrs:
            default_attrs.update(attrs)
        super().__init__(default_attrs)

    def render(self, name, value, attrs=None, renderer=None):
        if value and isinstance(value, dict):
            value = json.dumps(value, indent=2, cls=DjangoJSONEncoder)
        return super().render(name, value, attrs, renderer)

class GenericAgentAdminForm(forms.ModelForm):
    """Custom form for GenericAgent admin with improved JSON handling"""
    class Meta:
        model = GenericAgent
        fields = '__all__'
        widgets = {
            'input_schema': JSONSchemaWidget(),
            'output_schema': JSONSchemaWidget(),
            'state_schema': JSONSchemaWidget(),
            'memory_schema': JSONSchemaWidget(),
            'read_models': JSONSchemaWidget(),
            'write_models': JSONSchemaWidget(),
            'recommend_models': JSONSchemaWidget(),
            'system_instructions': forms.Textarea(attrs={'rows': 20, 'cols': 80, 'class': 'vLargeTextField'}),
        }

    def clean_input_schema(self):
        """Ensures input_schema is valid JSON"""
        data = self.cleaned_data['input_schema']
        if isinstance(data, str):
            try:
                return json.loads(data)
            except json.JSONDecodeError as e:
                raise forms.ValidationError(f"Invalid JSON: {str(e)}")
        return data

    def clean_output_schema(self):
        """Ensures output_schema is valid JSON"""
        data = self.cleaned_data['output_schema']
        if isinstance(data, str):
            try:
                return json.loads(data)
            except json.JSONDecodeError as e:
                raise forms.ValidationError(f"Invalid JSON: {str(e)}")
        return data

    def clean_state_schema(self):
        """Ensures state_schema is valid JSON"""
        data = self.cleaned_data['state_schema']
        if isinstance(data, str):
            try:
                return json.loads(data)
            except json.JSONDecodeError as e:
                raise forms.ValidationError(f"Invalid JSON: {str(e)}")
        return data

    def clean_memory_schema(self):
        """Ensures memory_schema is valid JSON"""
        data = self.cleaned_data['memory_schema']
        if isinstance(data, str):
            try:
                return json.loads(data)
            except json.JSONDecodeError as e:
                raise forms.ValidationError(f"Invalid JSON: {str(e)}")
        return data

    def clean_read_models(self):
        """Ensures read_models is valid JSON"""
        data = self.cleaned_data['read_models']
        if isinstance(data, str):
            try:
                return json.loads(data)
            except json.JSONDecodeError as e:
                raise forms.ValidationError(f"Invalid JSON: {str(e)}")
        return data

    def clean_write_models(self):
        """Ensures write_models is valid JSON"""
        data = self.cleaned_data['write_models']
        if isinstance(data, str):
            try:
                return json.loads(data)
            except json.JSONDecodeError as e:
                raise forms.ValidationError(f"Invalid JSON: {str(e)}")
        return data

    def clean_recommend_models(self):
        """Ensures recommend_models is valid JSON"""
        data = self.cleaned_data['recommend_models']
        if isinstance(data, str):
            try:
                return json.loads(data)
            except json.JSONDecodeError as e:
                raise forms.ValidationError(f"Invalid JSON: {str(e)}")
        return data

# Inline admin classes for related models
class CustomAgentInline(admin.TabularInline):
    model = CustomAgent
    extra = 0
    fields = ('name', 'user_profile', 'description')
    readonly_fields = ('name', 'user_profile', 'description')
    can_delete = False
    max_num = 0
    verbose_name = "Custom Agent Instance"
    verbose_name_plural = "Custom Agent Instances"

class AgentRunInline(admin.TabularInline):
    model = AgentRun
    extra = 0
    fields = ('id', 'workflow_id', 'user_profile', 'started_at', 'status', 'duration')
    readonly_fields = ('id', 'workflow_id', 'user_profile', 'started_at', 'status', 'duration')
    can_delete = False
    max_num = 5  # Use max_num instead of slicing
    verbose_name = "Recent Run"
    verbose_name_plural = "Recent Runs"

    def get_queryset(self, request):
        # Order the queryset, but don't slice it
        queryset = super().get_queryset(request)
        return queryset.order_by('-started_at')

    def duration(self, obj):
        return obj.duration or "In progress"

class AgentToolInline(admin.TabularInline):
    model = GenericAgent.available_tools.through
    extra = 1
    verbose_name = "Available Tool"
    verbose_name_plural = "Available Tools"

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "agenttool":
            kwargs["queryset"] = AgentTool.objects.filter(is_active=True)
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

class BenchmarkMetricInline(admin.TabularInline):
    model = GenericAgent.benchmark_metrics.through
    extra = 1
    verbose_name = "Benchmark Metric"
    verbose_name_plural = "Benchmark Metrics"

# @admin.register(GenericAgent) # Removed decorator
class GenericAgentAdmin(admin.ModelAdmin):
    form = GenericAgentAdminForm
    list_display = ('get_name_with_status', 'role', 'version', 'get_tools_count', 'get_custom_agents_count', 'get_runs_count', 'created_at')
    list_filter = ('role', 'is_active', 'version')
    search_fields = ('role', 'description', 'langgraph_node_class')
    readonly_fields = ('created_at', 'updated_at', 'get_processing_stats')
    inlines = [AgentToolInline, BenchmarkMetricInline, CustomAgentInline, AgentRunInline]
    save_on_top = True
    actions = ['duplicate_agent', 'activate_agents', 'deactivate_agents', 'sync_tool_registry']

    def sync_tool_registry(self, request, queryset):
        """
        Synchronize the tool registry with the database.
        This action can be triggered from the admin interface.
        """
        try:
            sync_tool_registry_with_database()
            self.message_user(request, "Successfully synchronized tool registry with the database.")
        except Exception as e:
            self.message_user(request, f"Error synchronizing tool registry: {str(e)}", level='error')
    sync_tool_registry.short_description = "Sync Tool Registry with Database"

    fieldsets = (
        (None, {
            'fields': ('role', 'llm_config', 'version', 'is_active', 'description')
        }),
        ('Instructions', {
            'fields': ('system_instructions',),
            'classes': ('wide',),
        }),
        ('Schema Definitions', {
            'fields': ('input_schema', 'output_schema', 'state_schema', 'memory_schema'),
            'classes': ('collapse',),
        }),
        ('Access Control', {
            'fields': ('read_models', 'write_models', 'recommend_models'),
            'classes': ('collapse',),
        }),
        ('Implementation Details', {
            'fields': ('langgraph_node_class', 'processing_timeout', 'get_processing_stats'),
            'classes': ('collapse',),
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )

    def get_exclude(self, request, obj=None):
        # Exclude available_tools and benchmark_metrics as they're handled by inlines
        return ['available_tools', 'benchmark_metrics']

    def get_name_with_status(self, obj):
        """Display role name with active/inactive status"""
        if obj.is_active:
            return format_html(
                '<span style="color: green;">⚫</span> {}',
                obj.get_role_display()
            )
        return format_html(
            '<span style="color: red;">⚫</span> {}',
            obj.get_role_display()
        )
    get_name_with_status.short_description = 'Agent'
    get_name_with_status.admin_order_field = 'role'

    def get_tools_count(self, obj):
        """Display count of available tools"""
        return obj.available_tools.count()
    get_tools_count.short_description = 'Tools'

    def get_custom_agents_count(self, obj):
        """Display count of custom agent instances"""
        return obj.custom_agents.count()
    get_custom_agents_count.short_description = 'Instances'

    def get_runs_count(self, obj):
        """Display count of runs"""
        return obj.runs.count()
    get_runs_count.short_description = 'Runs'

    def get_processing_stats(self, obj):
        """Display processing statistics from runs"""
        completed_runs = obj.runs.filter(status='completed')
        if not completed_runs.exists():
            return "No completed runs to analyze"

        # Calculate average duration
        total_duration = 0
        run_count = 0
        for run in completed_runs:
            if run.duration is not None:
                total_duration += run.duration
                run_count += 1

        if run_count == 0:
            avg_duration = "N/A"
        else:
            avg_duration = f"{total_duration / run_count:.2f}s"

        # Get status distribution
        status_counts = obj.runs.values('status').annotate(count=Count('status'))
        status_html = '<ul>'
        for status in status_counts:
            status_html += f'<li>{status["status"]}: {status["count"]}</li>'
        status_html += '</ul>'

        # Get tool usage
        tool_usage = {}
        for run in completed_runs[:100]:  # Limit to prevent performance issues
            for tool_call in run.tool_calls:
                tool_code = tool_call.get('tool_code')
                if tool_code:
                    tool_usage[tool_code] = tool_usage.get(tool_code, 0) + 1

        tool_html = '<ul>'
        for tool, count in sorted(tool_usage.items(), key=lambda x: x[1], reverse=True)[:5]:
            tool_html += f'<li>{tool}: {count} calls</li>'
        tool_html += '</ul>'

        return format_html(
            """
            <div style="margin-top: 10px;">
                <p><strong>Average Duration:</strong> {}</p>
                <p><strong>Status Distribution:</strong></p>
                {}
                <p><strong>Top Tool Usage:</strong></p>
                {}
            </div>
            """,
            avg_duration, status_html, tool_html
        )
    get_processing_stats.short_description = 'Processing Statistics'

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "agenttool":
            kwargs["queryset"] = AgentTool.objects.filter(is_active=True)
        if db_field.name == "llm_config":
            from .models import LLMConfig
            kwargs["queryset"] = LLMConfig.objects.all()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    def duplicate_agent(self, request, queryset):
        """Action to duplicate an agent"""
        for agent in queryset:
            # Create a new agent with incremented version
            parts = agent.version.split('.')
            if len(parts) >= 3:
                new_version = f"{parts[0]}.{parts[1]}.{int(parts[2]) + 1}"
            else:
                new_version = f"{agent.version}.1"

            new_agent = GenericAgent.objects.create(
                role=agent.role,
                version=new_version,
                is_active=False,  # Start inactive
                description=f"Copy of {agent.get_role_display()} ({agent.version})",
                system_instructions=agent.system_instructions,
                input_schema=agent.input_schema,
                output_schema=agent.output_schema,
                state_schema=agent.state_schema,
                memory_schema=agent.memory_schema,
                read_models=agent.read_models,
                write_models=agent.write_models,
                recommend_models=agent.recommend_models,
                langgraph_node_class=agent.langgraph_node_class,
                processing_timeout=agent.processing_timeout
            )

            # Copy tool relationships
            for tool in agent.available_tools.all():
                new_agent.available_tools.add(tool)

            # Copy benchmark metrics
            for metric in agent.benchmark_metrics.all():
                new_agent.benchmark_metrics.add(metric)

        self.message_user(request, f"Successfully duplicated {queryset.count()} agent(s).")
    duplicate_agent.short_description = "Duplicate selected agents"

    def activate_agents(self, request, queryset):
        """Action to activate agents"""
        queryset.update(is_active=True)
        self.message_user(request, f"{queryset.count()} agent(s) activated.")
    activate_agents.short_description = "Activate selected agents"

    def deactivate_agents(self, request, queryset):
        """Action to deactivate agents"""
        queryset.update(is_active=False)
        self.message_user(request, f"{queryset.count()} agent(s) deactivated.")
    deactivate_agents.short_description = "Deactivate selected agents"

    class Media:
        css = {
            'all': ('admin/css/jsoneditor.css',)
        }
        js = ('admin/js/jsoneditor.js',)

# @admin.register(AgentTool) # Removed decorator
class AgentToolAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'is_active', 'get_agent_count', 'usage_count', 'avg_response_time')
    list_filter = ('is_active',)
    search_fields = ('name', 'code', 'description', 'function_path')
    readonly_fields = ('avg_response_time', 'usage_count', 'error_rate', 'get_roles_display')

    fieldsets = (
        (None, {
            'fields': ('code', 'name', 'description', 'is_active')
        }),
        ('Tool Specifications', {
            'fields': ('input_schema', 'output_schema', 'function_path'),
            'classes': ('collapse',),
        }),
        ('Access Control', {
            'fields': ('allowed_agent_roles', 'get_roles_display'),
            'classes': ('collapse',),
        }),
        ('Performance Metrics', {
            'fields': ('avg_response_time', 'usage_count', 'error_rate'),
            'classes': ('collapse',),
        }),
    )

    def get_agent_count(self, obj):
        """Display count of agents using this tool"""
        return obj.available_to_agents.count()
    get_agent_count.short_description = 'Used By'

    def get_roles_display(self, obj):
        """Display formatted list of allowed roles"""
        if not obj.allowed_agent_roles:
            return "No roles specified"

        role_html = '<ul>'
        for role in obj.allowed_agent_roles:
            role_display = dict(AgentRole.choices).get(role, role)
            role_html += f'<li>{role_display}</li>'
        role_html += '</ul>'

        return format_html(role_html)
    get_roles_display.short_description = 'Allowed Roles'

    def formfield_for_dbfield(self, db_field, **kwargs):
        if db_field.name in ('input_schema', 'output_schema'):
            kwargs['widget'] = JSONSchemaWidget()
        elif db_field.name == 'allowed_agent_roles':
            from django import forms
            from .models import AgentRole
            kwargs['widget'] = forms.SelectMultiple(
                choices=AgentRole.choices
            )
        return super().formfield_for_dbfield(db_field, **kwargs)

# @admin.register(BenchmarkMetric) # Removed decorator
class BenchmarkMetricAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'data_type', 'get_agent_count')
    list_filter = ('data_type',)
    search_fields = ('name', 'code', 'description')

    fieldsets = (
        (None, {
            'fields': ('code', 'name', 'description', 'data_type')
        }),
        ('Measurement Properties', {
            'fields': ('unit', 'target_value'),
            'classes': ('collapse',),
        }),
        ('Categorical Options', {
            'fields': ('valid_values',),
            'classes': ('collapse',),
        }),
        ('Role Applicability', {
            'fields': ('applicable_roles',),
            'classes': ('collapse',),
        }),
    )

    def get_agent_count(self, obj):
        """Display count of agents using this metric"""
        return obj.measured_on_agents.count()
    get_agent_count.short_description = 'Used By'

    def formfield_for_dbfield(self, db_field, **kwargs):
        if db_field.name in ('target_value', 'valid_values'):
            kwargs['widget'] = JSONSchemaWidget(attrs={'rows': 5})
        return super().formfield_for_dbfield(db_field, **kwargs)

# @admin.register(AgentRun) # Removed decorator
class AgentRunAdmin(admin.ModelAdmin):
    list_display = ('id', 'agent', 'user_profile', 'status', 'started_at', 'get_duration', 'get_tool_calls_count')
    list_filter = ('status', 'agent', 'started_at')
    search_fields = ('id', 'workflow_id', 'user_profile__profile_name')
    readonly_fields = ('id', 'agent', 'workflow_id', 'user_profile', 'started_at', 'completed_at',
                     'input_data', 'output_data', 'initial_state', 'final_state', 'memory_updates',
                     'status', 'error_message', 'tool_calls', 'get_duration', 'get_tool_calls_summary')

    fieldsets = (
        (None, {
            'fields': ('id', 'agent', 'workflow_id', 'user_profile', 'status')
        }),
        ('Timing', {
            'fields': ('started_at', 'completed_at', 'get_duration'),
            'classes': ('collapse',),
        }),
        ('Run Details', {
            'fields': ('input_data', 'output_data', 'error_message'),
            'classes': ('collapse',),
        }),
        ('State', {
            'fields': ('initial_state', 'final_state', 'memory_updates'),
            'classes': ('collapse',),
        }),
        ('Tool Usage', {
            'fields': ('get_tool_calls_summary',),
            'classes': ('collapse',),
        }),
    )

    def get_duration(self, obj):
        """Display formatted duration"""
        if obj.duration is None:
            return "In progress"

        duration = obj.duration
        if duration < 1:
            return f"{duration * 1000:.2f}ms"
        return f"{duration:.2f}s"
    get_duration.short_description = 'Duration'

    def get_tool_calls_count(self, obj):
        """Display count of tool calls"""
        return len(obj.tool_calls) if obj.tool_calls else 0
    get_tool_calls_count.short_description = 'Tool Calls'

    def get_tool_calls_summary(self, obj):
        """Display formatted summary of tool calls"""
        if not obj.tool_calls:
            return "No tool calls recorded"

        calls_html = '<table style="width:100%; border-collapse: collapse;">'
        calls_html += '<tr><th style="border:1px solid #ddd; padding:8px; text-align:left;">Tool</th>'
        calls_html += '<th style="border:1px solid #ddd; padding:8px; text-align:left;">Duration</th>'
        calls_html += '<th style="border:1px solid #ddd; padding:8px; text-align:left;">Status</th></tr>'

        for i, call in enumerate(obj.tool_calls):
            tool_code = call.get('tool_code', 'Unknown')
            duration = call.get('duration_seconds', 'N/A')
            error = call.get('error')

            if error:
                status = f'<span style="color:red;">Error: {error}</span>'
            else:
                status = '<span style="color:green;">Success</span>'

            calls_html += f'<tr style="background-color:{i % 2 and "#f9f9f9" or "#fff"}">'
            calls_html += f'<td style="border:1px solid #ddd; padding:8px;">{tool_code}</td>'
            calls_html += f'<td style="border:1px solid #ddd; padding:8px;">{duration}s</td>'
            calls_html += f'<td style="border:1px solid #ddd; padding:8px;">{status}</td></tr>'

        calls_html += '</table>'

        return format_html(calls_html)
    get_tool_calls_summary.short_description = 'Tool Calls'

    def formfield_for_dbfield(self, db_field, **kwargs):
        if db_field.name in ('input_data', 'output_data', 'initial_state', 'final_state', 'memory_updates', 'tool_calls'):
            kwargs['widget'] = JSONSchemaWidget()
        return super().formfield_for_dbfield(db_field, **kwargs)

    def has_add_permission(self, request):
        return False  # Prevent direct creation of agent runs

# @admin.register(AgentMetric) # Removed decorator
class AgentMetricAdmin(admin.ModelAdmin):
    list_display = ('metric', 'agent_run', 'get_value_display', 'timestamp')
    list_filter = ('metric', 'timestamp')
    search_fields = ('agent_run__id', 'metric__name')
    readonly_fields = ('agent_run', 'metric', 'value', 'context', 'timestamp')

    fieldsets = (
        (None, {
            'fields': ('agent_run', 'metric', 'value')
        }),
        ('Details', {
            'fields': ('context', 'timestamp'),
            'classes': ('collapse',),
        }),
    )

    def get_value_display(self, obj):
        """Format value based on metric type"""
        if not obj.metric:
            return str(obj.value)

        if obj.metric.data_type == 'numeric':
            if obj.metric.unit:
                return f"{obj.value} {obj.metric.unit}"
            return f"{obj.value}"
        elif obj.metric.data_type == 'boolean':
            return "✓" if obj.value else "✗"
        elif obj.metric.data_type == 'duration':
            if float(obj.value) < 1:
                return f"{float(obj.value) * 1000:.2f}ms"
            return f"{obj.value}s"
        return str(obj.value)
    get_value_display.short_description = 'Value'

    def has_add_permission(self, request):
        return False  # Prevent direct creation of metrics


# @admin.register(AgentRecommendation) # Removed decorator
class AgentRecommendationAdmin(admin.ModelAdmin):
    list_display = ('id', 'target_model', 'operation', 'status', 'priority', 'created_at')
    list_filter = ('status', 'operation', 'priority', 'created_at')
    search_fields = ('target_model', 'target_instance_id', 'rationale')
    readonly_fields = ('id', 'created_by_run', 'target_model', 'target_instance_id', 'operation',
                     'field_updates', 'rationale', 'evidence', 'confidence', 'created_at')

    fieldsets = (
        (None, {
            'fields': ('id', 'status', 'priority')
        }),
        ('Target', {
            'fields': ('target_model', 'target_instance_id', 'operation'),
        }),
        ('Changes', {
            'fields': ('field_updates',),
        }),
        ('Justification', {
            'fields': ('rationale', 'evidence', 'confidence'),
            'classes': ('collapse',),
        }),
        ('Workflow', {
            'fields': ('created_by_run', 'reviewed_by', 'applied_at', 'created_at'),
            'classes': ('collapse',),
        }),
    )

    actions = ['approve_recommendations', 'reject_recommendations']

    def approve_recommendations(self, request, queryset):
        """Approve selected recommendations"""
        queryset.update(status='approved')
        self.message_user(request, f"{queryset.count()} recommendation(s) approved.")
    approve_recommendations.short_description = "Approve selected recommendations"

    def reject_recommendations(self, request, queryset):
        """Reject selected recommendations"""
        queryset.update(status='rejected')
        self.message_user(request, f"{queryset.count()} recommendation(s) rejected.")
    reject_recommendations.short_description = "Reject selected recommendations"

    def formfield_for_dbfield(self, db_field, **kwargs):
        if db_field.name in ('field_updates', 'evidence'):
            kwargs['widget'] = JSONSchemaWidget()
        return super().formfield_for_dbfield(db_field, **kwargs)



class AgentGoalInline(admin.TabularInline):
    model = AgentGoal
    extra = 0

# @admin.register(CustomAgent) # Removed decorator
class CustomAgentAdmin(admin.ModelAdmin):
    list_display = ('name', 'generic_agent', 'user_profile', 'get_memories_count', 'get_goals_count')
    list_filter = ('generic_agent',)
    search_fields = ('name', 'description', 'user_profile__profile_name')

    fieldsets = (
        (None, {
            'fields': ('name', 'generic_agent', 'user_profile')
        }),
        ('Configuration', {
            'fields': ('description', 'instruction'),
        }),
    )

    inlines = [AgentGoalInline]


    def get_memories_count(self, obj):
        """Display count of memories"""
        return obj.memories.count()
    get_memories_count.short_description = 'Memories'

    def get_goals_count(self, obj):
        """Display count of goals"""
        return obj.agent_goals.count()
    get_goals_count.short_description = 'Goals'

# @admin.register(AgentGoal) # Removed decorator
class AgentGoalAdmin(admin.ModelAdmin):
    list_display = ('id', 'description', 'custom_agent', 'priority', 'user_goal_id') # Added ID
    list_filter = ('priority',)
    search_fields = ('description', 'custom_agent__name', 'user_goal_id')

    fieldsets = (
        (None, {
            'fields': ('custom_agent', 'description')
        }),
        ('Configuration', {
            'fields': ('priority', 'user_goal_id'),
        }),
    )


# Admin classes for Benchmarking models

class BenchmarkTagAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'get_scenario_count', 'created_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at',)

    def get_scenario_count(self, obj):
        return obj.scenarios.count()
    get_scenario_count.short_description = 'Scenarios'

class LatestScenarioFilter(admin.SimpleListFilter):
    """Custom filter to show only latest or all scenario versions."""
    title = 'version status'
    parameter_name = 'version_status'

    def lookups(self, request, model_admin):
        return (
            ('latest', 'Latest Versions Only'),
            ('all', 'All Versions'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'all':
            return queryset
        # Default to latest or if 'latest' is selected
        return queryset.filter(is_latest=True)

    # Default to 'latest' if parameter is not provided
    def value(self):
        return super().value() or 'latest'


class BenchmarkScenarioAdmin(admin.ModelAdmin):
    list_display = ('name', 'version', 'agent_role', 'is_latest', 'is_active', 'get_tags_display', 'created_at', 'get_runs_count')
    list_filter = ('agent_role', 'is_active', LatestScenarioFilter, 'created_at', 'tags') # Added LatestScenarioFilter
    search_fields = ('name', 'description', 'agent_role', 'tags__name')
    readonly_fields = ('created_at', 'updated_at', 'version', 'parent_scenario', 'is_latest') # Make versioning fields read-only
    filter_horizontal = ('tags', 'variations',) # Added variations
    fieldsets = (
        (None, {'fields': ('name', 'agent_role', 'description', 'is_active')}),
        ('Data', {'fields': ('input_data', 'metadata')}),
        ('Organization', {'fields': ('tags', 'variations')}), # Added variations here
        ('Versioning', {'fields': ('version', 'is_latest', 'parent_scenario'), 'classes': ('collapse',)}), # Added Versioning section
        ('Timestamps', {'fields': ('created_at', 'updated_at'), 'classes': ('collapse',)}),
    )
    list_display_links = ('name',) # Link from name

    def get_queryset(self, request):
        """Default to showing only the latest versions unless filtered otherwise."""
        qs = super().get_queryset(request)
        # Apply the filter logic based on the request parameter
        filter_value = request.GET.get(LatestScenarioFilter.parameter_name, 'latest')
        if filter_value == 'latest':
            return qs.filter(is_latest=True)
        return qs # Return all if 'all' is selected

    def formfield_for_dbfield(self, db_field, **kwargs):
        # Use custom JSON widget for relevant fields
        if db_field.name in ('input_data', 'metadata'):
            kwargs['widget'] = JSONSchemaWidget(attrs={'rows': 15})
        # Handle ManyToMany fields if needed (though filter_horizontal is often preferred)
        # if db_field.name == "tags":
        #     kwargs['widget'] = forms.SelectMultiple(attrs={'size':'10'})
        return super().formfield_for_dbfield(db_field, **kwargs)

    def get_runs_count(self, obj):
        return obj.runs.count()
    get_runs_count.short_description = 'Runs Count'

    def get_tags_display(self, obj):
        """Display tags as comma-separated string."""
        return ", ".join([tag.name for tag in obj.tags.all()])
    get_tags_display.short_description = 'Tags'

# Use ImportExportModelAdmin for BenchmarkScenario
class BenchmarkScenarioAdmin(ImportExportModelAdmin): # Inherit from ImportExportModelAdmin
    resource_class = BenchmarkScenarioResource # Link the resource
    list_display = ('name', 'version', 'agent_role', 'is_latest', 'is_active', 'get_tags_display', 'created_at', 'get_runs_count')
    list_filter = ('agent_role', 'is_active', LatestScenarioFilter, 'created_at', 'tags') # Added LatestScenarioFilter
    search_fields = ('name', 'description', 'agent_role', 'tags__name')
    readonly_fields = ('created_at', 'updated_at', 'parent_scenario', 'is_latest') # Keep versioning fields read-only for safety, allow version editing
    filter_horizontal = ('tags', 'variations',) # Added variations
    fieldsets = (
        (None, {'fields': ('name', 'agent_role', 'description', 'is_active', 'version')}), # Allow editing version
        ('Data', {'fields': ('input_data', 'metadata')}),
        ('Organization', {'fields': ('tags', 'variations')}), # Added variations here
        ('Versioning (Read-Only)', {'fields': ('is_latest', 'parent_scenario'), 'classes': ('collapse',)}), # Versioning info display
        ('Timestamps', {'fields': ('created_at', 'updated_at'), 'classes': ('collapse',)}),
    )
    list_display_links = ('name',) # Link from name

    def get_queryset(self, request):
        """Default to showing only the latest versions unless filtered otherwise."""
        qs = super().get_queryset(request)
        # Apply the filter logic based on the request parameter
        filter_value = request.GET.get(LatestScenarioFilter.parameter_name, 'latest')
        if filter_value == 'latest':
            return qs.filter(is_latest=True)
        return qs # Return all if 'all' is selected

    def formfield_for_dbfield(self, db_field, **kwargs):
        # Use custom JSON widget for relevant fields
        if db_field.name in ('input_data', 'metadata'):
            kwargs['widget'] = JSONSchemaWidget(attrs={'rows': 15})
        # Handle ManyToMany fields if needed (though filter_horizontal is often preferred)
        # if db_field.name == "tags":
        #     kwargs['widget'] = forms.SelectMultiple(attrs={'size':'10'})
        return super().formfield_for_dbfield(db_field, **kwargs)

    def get_runs_count(self, obj):
        return obj.runs.count()
    get_runs_count.short_description = 'Runs Count'

    def get_tags_display(self, obj):
        """Display tags as comma-separated string."""
        return ", ".join([tag.name for tag in obj.tags.all()])
    get_tags_display.short_description = 'Tags'

    # Override save_model to handle versioning logic
    def save_model(self, request, obj, form, change):
        is_new = not obj.pk
        if is_new or 'version' in form.changed_data or 'name' in form.changed_data:
            # Find existing latest version for this name
            latest_existing = BenchmarkScenario.objects.filter(
                name=obj.name, is_latest=True
            ).exclude(pk=obj.pk).first()

            if latest_existing and obj.version <= latest_existing.version:
                # If saving an older or same version, it cannot be the latest
                obj.is_latest = False
                obj.parent_scenario = latest_existing # Link to the current latest as parent
            else:
                # This is potentially the new latest version
                obj.is_latest = True
                if latest_existing:
                    # Link to the previous latest as parent
                    obj.parent_scenario = latest_existing
                    # Demote the previous latest
                    latest_existing.is_latest = False
                    latest_existing.save(update_fields=['is_latest'])

        super().save_model(request, obj, form, change)


class BenchmarkRunAdmin(admin.ModelAdmin):
    list_display = ('id', 'scenario_link', 'agent_definition', 'llm_config', 'execution_date', 'mean_duration', 'success_rate', 'semantic_score') # Changed scenario to scenario_link, llm_model to llm_config
    list_filter = ('agent_definition__role', 'llm_config__name', 'execution_date', 'scenario__name') # Filter by llm_config name
    search_fields = ('id', 'scenario__name', 'scenario__version', 'agent_definition__role', 'llm_config__name', 'llm_config__model_name') # Search by llm_config name/model
    readonly_fields = [f.name for f in BenchmarkRun._meta.fields if f.name != 'scenario'] + ['scenario_link_display'] # Make most fields read-only
    list_display_links = ('id',) # Only link from ID
    # change_form_template = "admin/main/benchmarkrun/change_form.html" # Use custom template with Plotly - Revisit if needed

    fieldsets = (
        ('Run Info', {'fields': ('id', 'scenario_link_display', 'agent_definition', 'agent_version', 'llm_config', 'execution_date')}), # Use display field, replaced llm_model with llm_config
        ('Parameters', {'fields': ('parameters',), 'classes': ('collapse',)}),
        ('Performance Metrics', {'fields': ('runs_count', 'mean_duration', 'median_duration', 'min_duration', 'max_duration', 'std_dev', 'success_rate'), 'classes': ('collapse',)}),
        ('Operational Metrics', {'fields': ('llm_calls', 'tool_calls', 'tool_breakdown', 'memory_operations'), 'classes': ('collapse',)}),
        ('Semantic Evaluation', {'fields': ('semantic_score', 'semantic_evaluation_details', 'semantic_evaluations'), 'classes': ('collapse',)}), # Added semantic_evaluations
        ('Statistical Comparison', {'fields': ('compared_to_run', 'performance_p_value', 'is_performance_significant'), 'classes': ('collapse',)}), # Added comparison fields
        ('Stage Performance', {'fields': ('stage_performance_details',), 'classes': ('collapse',)}), # Added stage performance
        ('Raw Data', {'fields': ('raw_results',), 'classes': ('collapse',)}),
    )

    def scenario_link(self, obj):
        """Provide a link to the related BenchmarkScenario admin page."""
        if obj.scenario:
            link = reverse("admin:main_benchmarkscenario_change", args=[obj.scenario.id])
            return format_html('<a href="{}">{}</a>', link, obj.scenario)
        return "-"
    scenario_link.short_description = 'Scenario'
    scenario_link.admin_order_field = 'scenario' # Allow sorting by scenario

    def scenario_link_display(self, obj):
        """Display field for readonly scenario link in fieldsets."""
        return self.scenario_link(obj)
    scenario_link_display.short_description = 'Scenario'


    def has_add_permission(self, request):
        return False # Prevent adding runs manually

    # Keep change permission false as runs should be immutable records
    def has_change_permission(self, request, obj=None):
        return False # Prevent changing runs

    def has_delete_permission(self, request, obj=None):
        return True # Allow deletion if needed

    def formfield_for_dbfield(self, db_field, **kwargs):
        # Use custom JSON widget for relevant fields
        if db_field.name in ('parameters', 'tool_breakdown', 'semantic_evaluation_details', 'semantic_evaluations', 'raw_results', 'stage_performance_details'):
            kwargs['widget'] = JSONSchemaWidget(attrs={'rows': 15})
        return super().formfield_for_dbfield(db_field, **kwargs)


class EvaluationCriteriaTemplateAdmin(admin.ModelAdmin):
    """Admin interface for Evaluation Criteria Templates."""
    list_display = ('name', 'description', 'created_at', 'updated_at')
    search_fields = ('name', 'description', 'criteria')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {'fields': ('name', 'description')}),
        ('Criteria Definition (JSON)', {'fields': ('criteria',)}),
        ('Timestamps', {'fields': ('created_at', 'updated_at'), 'classes': ('collapse',)}),
    )

    def formfield_for_dbfield(self, db_field, **kwargs):
        if db_field.name == 'criteria':
            kwargs['widget'] = JSONSchemaWidget(attrs={'rows': 15})
        return super().formfield_for_dbfield(db_field, **kwargs)


# --- Beta Signup Admin ---
class BetaSignupAdmin(admin.ModelAdmin):
    """Admin interface for managing beta signup requests."""
    list_display = ('email', 'status', 'created_at', 'processed_at', 'processed_by')
    list_filter = ('status', 'created_at', 'processed_at')
    search_fields = ('email', 'message', 'notes')
    readonly_fields = ('created_at', 'ip_address', 'user_agent')
    actions = ['mark_as_contacted', 'mark_as_invited', 'mark_as_declined']

    fieldsets = (
        ('Contact Information', {
            'fields': ('email', 'message')
        }),
        ('Status Management', {
            'fields': ('status', 'notes', 'processed_at', 'processed_by')
        }),
        ('Metadata', {
            'fields': ('created_at', 'ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
    )

    def mark_as_contacted(self, request, queryset):
        """Mark selected signups as contacted"""
        updated = queryset.update(status='contacted')
        self.message_user(request, f"{updated} signup(s) marked as contacted.")
    mark_as_contacted.short_description = "Mark as contacted"

    def mark_as_invited(self, request, queryset):
        """Mark selected signups as invited to beta"""
        updated = queryset.update(status='invited')
        self.message_user(request, f"{updated} signup(s) marked as invited to beta.")
    mark_as_invited.short_description = "Mark as invited to beta"

    def mark_as_declined(self, request, queryset):
        """Mark selected signups as declined"""
        updated = queryset.update(status='declined')
        self.message_user(request, f"{updated} signup(s) marked as declined.")
    mark_as_declined.short_description = "Mark as declined"

    def save_model(self, request, obj, form, change):
        """Auto-set processed_by and processed_at when status changes"""
        if change and 'status' in form.changed_data and obj.status != 'pending':
            if not obj.processed_by:
                obj.processed_by = request.user
            if not obj.processed_at:
                from django.utils import timezone
                obj.processed_at = timezone.now()
        super().save_model(request, obj, form, change)


# --- Register models with the custom admin site instance ---
admin_site.register(GenericAgent, GenericAgentAdmin)
admin_site.register(AgentTool, AgentToolAdmin)
admin_site.register(BenchmarkMetric, BenchmarkMetricAdmin)
admin_site.register(AgentRun, AgentRunAdmin)
admin_site.register(AgentMetric, AgentMetricAdmin)
# AgentMemory is missing an Admin class, skipping for now. Add if needed.
# admin_site.register(AgentMemory, AgentMemoryAdmin)
admin_site.register(AgentRecommendation, AgentRecommendationAdmin)
admin_site.register(CustomAgent, CustomAgentAdmin)
admin_site.register(AgentGoal, AgentGoalAdmin)
admin_site.register(BenchmarkTag, BenchmarkTagAdmin) # Register new Tag model
admin_site.register(BenchmarkScenario, BenchmarkScenarioAdmin) # Register updated Scenario admin with Import/Export
admin_site.register(BenchmarkRun, BenchmarkRunAdmin) # Register Run admin
admin_site.register(EvaluationCriteriaTemplate, EvaluationCriteriaTemplateAdmin) # Register new Template admin
admin_site.register(BetaSignup, BetaSignupAdmin) # Register Beta Signup admin

# Also register GenericAgentAdmin with the default admin site for MAIN app reference
from django.contrib import admin as default_admin

# --- LLMConfig Admin (Reverted) ---
class LLMConfigAdmin(ImportExportModelAdmin):
    resource_class = LLMConfigResource # Link the resource
    list_display = ('name', 'model_name', 'temperature', 'is_evaluation', 'is_default', 'input_token_price', 'output_token_price', 'created_at', 'updated_at')
    list_filter = ('is_evaluation', 'is_default', 'model_name')
    search_fields = ('name', 'model_name')
    fields = ('name', 'model_name', 'temperature', 'input_token_price', 'output_token_price', 'is_evaluation', 'is_default')

    # Removed custom import_formats
    # import_formats = [NormalizedJsonFormat]

    def get_resource_class(self):
        """
        Explicitly return the custom resource class to ensure it's used.
        """
        print("--- LLMConfigAdmin: get_resource_class called, returning LLMConfigResource ---") # Keep this debug log
        return LLMConfigResource

admin_site.register(LLMConfig, LLMConfigAdmin) # Keep the registration


# Add CSS for JSON editor enhancement - This seems unused as the custom site is defined in config/admin.py
# The GameOfLifeAdminSite class definition and its methods are removed as they are not needed here.
# The custom admin site instance is imported from config.admin and used for registration.

# Uncomment to use custom admin site:
# admin_site = GameOfLifeAdminSite(name='gameoflife_admin')
# The registration is now done above using the imported admin_site instance.
# The commented-out code below was likely for testing the custom site locally.
# Removed orphaned CSS rules from previous edit.
