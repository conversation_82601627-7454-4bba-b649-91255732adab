/**
 * Message Handler Service
 * Processes incoming WebSocket messages and routes them to appropriate handlers
 */

import type { WebSocketManager } from './websocket-manager';
import type { StateManager } from './state-manager';
import type {
  ServerMessage,
  ChatMessageResponse,
  WheelDataResponse,
  ProcessingStatusResponse,
  ErrorResponse,
  ActivityDetailsResponse,
  SystemMessageResponse,
} from '@/types/websocket-types';
import type { ChatMessage, WheelData, ActivityDetails } from '@/types/app-types';
import { DebugMessageFilter } from '../utils/debug-message-filter';
import { ConversationState } from '../utils/conversationState';

export class MessageHandler extends EventTarget {
  private static instance: MessageHandler;
  private wsManager: WebSocketManager | null = null;
  private stateManager: StateManager | null = null;
  private messageId = 0;
  private debugFilter = DebugMessageFilter.getInstance();

  private constructor() {
    super();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): MessageHandler {
    if (!MessageHandler.instance) {
      MessageHandler.instance = new MessageHandler();
    }
    return MessageHandler.instance;
  }

  /**
   * Initialize message handler
   */
  public initialize(
    wsManager: WebSocketManager, 
    stateManager: StateManager
  ): void {
    this.wsManager = wsManager;
    this.stateManager = stateManager;
    
    // Listen for WebSocket messages
    this.wsManager.addEventListener('message', this.handleMessage.bind(this));
    
    // Listen for connection state changes
    this.wsManager.addEventListener('open', this.handleConnectionOpen.bind(this));
    this.wsManager.addEventListener('close', this.handleConnectionClose.bind(this));
    this.wsManager.addEventListener('error', this.handleConnectionError.bind(this));
    
    console.log('✅ Message handler initialized');
  }

  /**
   * Handle incoming WebSocket message
   */
  private handleMessage(event: Event): void {
    const { data } = (event as CustomEvent).detail as { data: ServerMessage };

    try {
      // Progress updates are handled by the switch statement below

      switch (data.type) {
        case 'system_message':
          this.handleSystemMessage(data as SystemMessageResponse);
          break;
          
        case 'chat_message':
          this.handleChatMessage(data as ChatMessageResponse);
          break;
          
        case 'processing_status':
          this.handleProcessingStatus(data as ProcessingStatusResponse);
          break;
          
        case 'wheel_data':
          this.handleWheelData(data as WheelDataResponse);
          break;
          
        case 'activity_details':
          this.handleActivityDetails(data as ActivityDetailsResponse);
          break;
          
        case 'error':
          this.handleError(data as ErrorResponse);
          break;

        case 'debug_info':
          this.handleDebugInfo(data);
          break;

        case 'workflow_status':
          this.handleWorkflowStatus(data);
          break;

        case 'conversation_state_update':
          this.handleConversationStateUpdate(data);
          break;

        case 'progress_update':
          this.handleProgressUpdate(data);
          break;

        default:
          console.warn('Unknown message type:', (data as any).type);
      }
    } catch (error) {
      console.error('Error handling message:', error);
      this.stateManager?.setError('Failed to process server message');
    }
  }

  /**
   * Handle system messages
   */
  private handleSystemMessage(message: SystemMessageResponse): void {
    console.log('📢 System message:', message.content);
    
    // Add system message to chat
    const chatMessage: ChatMessage = {
      id: `sys_${++this.messageId}`,
      content: message.content,
      isUser: false,
      timestamp: Date.now(),
      type: 'system',
    };
    
    this.stateManager?.addChatMessage(chatMessage);
    
    // Dispatch system message event
    this.dispatchEvent(new CustomEvent('system-message', {
      detail: { message: message.content }
    }));
  }

  /**
   * Handle chat messages
   */
  private handleChatMessage(message: ChatMessageResponse): void {
    const chatMessage: ChatMessage = {
      id: `chat_${++this.messageId}`,
      content: message.content,
      isUser: message.is_user,
      timestamp: Date.now(),
      type: 'text',
    };
    
    this.stateManager?.addChatMessage(chatMessage);
    
    // Dispatch chat message event
    this.dispatchEvent(new CustomEvent('chat-message', {
      detail: { message: chatMessage }
    }));
  }

  /**
   * Handle processing status updates
   */
  private handleProcessingStatus(message: ProcessingStatusResponse): void {
    const isLoading = message.status === 'processing';
    this.stateManager?.setLoadingState(isLoading);
    
    // Clear error if processing completed successfully
    if (message.status === 'completed') {
      this.stateManager?.clearError();
    }
    
    // Dispatch processing status event
    this.dispatchEvent(new CustomEvent('processing-status', {
      detail: { status: message.status }
    }));
  }

  /**
   * Handle wheel data
   */
  private handleWheelData(message: WheelDataResponse): void {
    const wheelData: WheelData = {
      name: message.wheel.name,
      items: message.wheel.items.map(item => ({
        id: item.id,
        name: item.name,
        description: item.description,
        percentage: item.percentage,
        color: item.color,
        domain: item.domain,
        base_challenge_rating: item.base_challenge_rating,
        activity_tailored_id: item.activity_tailored_id,
      })),
    };
    
    // Validate wheel data
    const validation = this.validateWheelData(wheelData);
    if (!validation.isValid) {
      console.error('Invalid wheel data:', validation.errors);
      this.stateManager?.setError('Received invalid wheel data');
      return;
    }
    
    this.stateManager?.setCurrentWheel(wheelData);
    this.stateManager?.setLoadingState(false);
    
    // Dispatch wheel data event
    this.dispatchEvent(new CustomEvent('wheel-data', {
      detail: { 
        wheel: wheelData,
        mentorContext: message.mentor_context,
        workflowInsights: message.workflow_insights,
      }
    }));
  }

  /**
   * Handle activity details
   */
  private handleActivityDetails(message: ActivityDetailsResponse): void {
    const activityDetails: ActivityDetails = {
      id: message.details.id,
      name: message.details.name,
      detailed_description: message.details.detailed_description,
      preparation_steps: message.details.preparation_steps,
      tips_for_success: message.details.tips_for_success,
      reflection_questions: message.details.reflection_questions,
    };
    
    // Dispatch activity details event
    this.dispatchEvent(new CustomEvent('activity-details', {
      detail: { activity: activityDetails }
    }));
  }

    /**
   * Handle error messages
   */
  private handleError(message: ErrorResponse): void {
    const errorContent = typeof message.content === 'string'
      ? message.content
      : message.content.content;

    console.error('❌ Server error:', errorContent);
    this.stateManager?.setError(errorContent);
    this.stateManager?.setLoadingState(false);

    // Add error message to chat with proper formatting
    const errorChatMessage: ChatMessage = {
      id: `error_${++this.messageId}`,
      content: `⚠️ ${errorContent}`,
      isUser: false,
      timestamp: Date.now(),
      type: 'error',
    };
    
    this.stateManager?.addChatMessage(errorChatMessage);

    // Dispatch error event
    this.dispatchEvent(new CustomEvent('error', {
      detail: {
        error: errorContent,
        code: typeof message.content === 'object' ? message.content.code : undefined,
        details: typeof message.content === 'object' ? message.content.details : undefined,
      }
    }));
  }

  /**
   * Handle debug info messages
   */
  private handleDebugInfo(message: any): void {
    // Use debug filter to control spam
    if (this.debugFilter.shouldShowDebugMessage(message)) {
      console.log('🐛 Debug info:', message);
    }

    // Don't add debug messages to chat - they should only go to debug panel
    // This prevents the chat from being overwhelmed with debug spam
    
    // Dispatch debug info event for debug panel only
    this.dispatchEvent(new CustomEvent('debug-info', {
      detail: {
        info: message.content || message,
        timestamp: Date.now()
      }
    }));
  }



  /**
   * Handle workflow status messages
   */
  private handleWorkflowStatus(message: any): void {
    console.log('🔄 Workflow status:', message);

    // Update loading state based on workflow status
    if (message.status === 'running' || message.status === 'processing') {
      this.stateManager?.setLoadingState(true);
    } else if (message.status === 'completed' || message.status === 'failed') {
      this.stateManager?.setLoadingState(false);
    }

    // Dispatch workflow status event
    this.dispatchEvent(new CustomEvent('workflow-status', {
      detail: {
        status: message.status,
        workflow: message.workflow,
        details: message.details,
        timestamp: Date.now()
      }
    }));
  }

  /**
   * Handle conversation state updates
   */
  private handleConversationStateUpdate(message: any): void {
    console.log('🔄 Conversation state update:', message);

    if (message.updates && typeof message.updates === 'object') {
      // Update conversation state with new data
      ConversationState.update(message.updates);

      // Dispatch conversation state update event
      this.dispatchEvent(new CustomEvent('conversation-state-update', {
        detail: {
          updates: message.updates,
          timestamp: Date.now()
        }
      }));
    }
  }

  /**
   * Handle progress updates
   */
  private handleProgressUpdate(data: any): void {
    console.log('📊 Progress update:', data);

    // Extract progress data
    const progressData = data.data || data;

    // Emit the progress update event for the progress bar component
    window.dispatchEvent(new CustomEvent('progress-update', {
      detail: {
        data: {
          tracker_id: progressData.tracker_id,
          stage_id: progressData.stage_id,
          stage_name: progressData.stage_name,
          stage: progressData.stage,
          progress_percent: progressData.progress_percent,
          message: progressData.message,
          timestamp: progressData.timestamp,
          priority: progressData.priority,
          workflow_type: progressData.workflow_type,
          metrics: progressData.metrics
        }
      }
    }));
  }

  /**
   * Handle connection open
   */
  private handleConnectionOpen(): void {
    this.stateManager?.setConnectionState(true);
    this.stateManager?.clearError();
    
    console.log('✅ Connected to server');
  }

  /**
   * Handle connection close
   */
  private handleConnectionClose(): void {
    this.stateManager?.setConnectionState(false);
    this.stateManager?.setLoadingState(false);
    
    console.log('🔌 Disconnected from server');
  }

  /**
   * Handle connection error
   */
  private handleConnectionError(): void {
    this.stateManager?.setConnectionState(false);
    this.stateManager?.setError('Connection error occurred');
    
    console.error('❌ Connection error');
  }

  /**
   * Send chat message to server
   */
  public async sendChatMessage(
    message: string,
    userId: string,
    workflowType?: string,
    llmConfigId?: string,
    additionalMetadata?: Record<string, any>
  ): Promise<void> {
    if (!this.wsManager) {
      throw new Error('WebSocket manager not initialized');
    }

    // Add user message to chat immediately
    const chatMessage: ChatMessage = {
      id: `user_${++this.messageId}`,
      content: message,
      isUser: true,
      timestamp: Date.now(),
      type: 'text',
    };

    this.stateManager?.addChatMessage(chatMessage);

    // Build metadata with debug selections and conversation state
    const metadata: any = {};
    if (workflowType) {
      metadata.requested_workflow = workflowType;
    }
    if (llmConfigId) {
      metadata.llm_config_id = llmConfigId;
    }

    // Include additional metadata if provided
    if (additionalMetadata) {
      Object.assign(metadata, additionalMetadata);
    }

    // Include conversation state in metadata
    const conversationState = ConversationState.get();
    metadata.conversation_phase = conversationState.phase || 'initial';
    metadata.awaiting_response_type = conversationState.awaiting_response_type;
    metadata.last_workflow = conversationState.last_workflow;
    metadata.session_context = conversationState.context || {};

    // Send to server
    await this.wsManager.send({
      type: 'chat_message',
      content: {
        message,
        user_profile_id: userId,
        timestamp: new Date().toISOString(),
        metadata,
      },
    });

    // Set loading state
    this.stateManager?.setLoadingState(true);
  }

  /**
   * Send spin result to server
   */
  public async sendSpinResult(
    activityId: string,
    activityName: string,
    activityDescription: string,
    userId: string
  ): Promise<void> {
    if (!this.wsManager) {
      throw new Error('WebSocket manager not initialized');
    }
    
    // Include conversation state in spin result
    const conversationState = ConversationState.get();

    await this.wsManager.send({
      type: 'spin_result',
      content: {
        activity_tailored_id: activityId,
        name: activityName,
        description: activityDescription,
        user_profile_id: userId,
        metadata: {
          conversation_phase: conversationState.phase || 'initial',
          awaiting_response_type: conversationState.awaiting_response_type,
          last_workflow: conversationState.last_workflow,
          session_context: conversationState.context || {}
        }
      },
    });
    
    // Set loading state
    this.stateManager?.setLoadingState(true);
  }

  /**
   * Validate wheel data
   */
  private validateWheelData(wheel: WheelData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!wheel.name) {
      errors.push('Wheel name is required');
    }
    
    if (!Array.isArray(wheel.items) || wheel.items.length === 0) {
      errors.push('Wheel must have at least one item');
    }
    
    // Validate and auto-fix percentages to sum to 100
    const totalPercentage = wheel.items.reduce((sum, item) => sum + item.percentage, 0);

    if (totalPercentage <= 0) {
      errors.push('Wheel percentages must be positive values');
    } else if (Math.abs(totalPercentage - 100) > 0.01) {
      // Auto-fix percentage discrepancies by normalizing to 100
      console.log(`🔧 Auto-fixing wheel percentages: total was ${totalPercentage.toFixed(2)}, normalizing to 100`);
      const scaleFactor = 100 / totalPercentage;
      wheel.items.forEach(item => {
        item.percentage = Math.round((item.percentage * scaleFactor) * 100) / 100; // Round to 2 decimal places
      });

      // Verify the fix worked
      const newTotal = wheel.items.reduce((sum, item) => sum + item.percentage, 0);
      console.log(`✅ Percentages normalized: new total = ${newTotal.toFixed(2)}`);
    }
    
    // Validate individual items
    for (const [index, item] of wheel.items.entries()) {
      if (!item.id || !item.name || !item.color) {
        errors.push(`Invalid item at index ${index}: missing required fields`);
      }
      
      if (item.percentage <= 0 || item.percentage > 100) {
        errors.push(`Invalid percentage for item ${item.name}: ${item.percentage}`);
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Destroy message handler
   */
  public destroy(): void {
    if (this.wsManager) {
      this.wsManager.removeEventListener('message', this.handleMessage.bind(this));
      this.wsManager.removeEventListener('open', this.handleConnectionOpen.bind(this));
      this.wsManager.removeEventListener('close', this.handleConnectionClose.bind(this));
      this.wsManager.removeEventListener('error', this.handleConnectionError.bind(this));
    }
    
    this.wsManager = null;
    this.stateManager = null;
  }
}
