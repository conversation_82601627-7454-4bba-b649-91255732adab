# backend/apps/main/celery_results.py

from celery.result import AsyncResult
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import logging
import json
from celery.signals import task_success, task_failure

from apps.main.services.workflow_result_handler import workflow_result_handler

logger = logging.getLogger(__name__)

@task_success.connect
def handle_task_success(sender=None, result=None, **kwargs):
    """
    Signal handler for successful Celery task completion.
    
    This function is called when a Celery task completes successfully.
    It routes the result to the appropriate handler based on task type.
    
    Args:
        sender: The task that generated the signal
        result: The result of the task
        kwargs: Additional keyword arguments
    """
    try:
        # Get the task ID
        task_id = sender.request.id
        task_name = sender.name
        
        logger.info(f"Task completed successfully: {task_name} ({task_id})")
        
        # Check the task type and route accordingly
        if 'execute_onboarding_workflow' in task_name:
            # Handle onboarding workflow
            handle_onboarding_result(result, task_id)

        elif 'execute_graph_workflow' in task_name:
            # Handle LangGraph workflow result
            handle_graph_workflow_result(result, task_id)

        elif 'execute_wheel_generation_workflow' in task_name:
            # Handle wheel generation workflow result
            handle_wheel_generation_result(result, task_id)

        elif 'execute_agent_node' in task_name:
            # Handle individual agent node
            handle_agent_node_result(result, task_id)

        elif 'run_template_test' in task_name:
            # Handle template test completion
            logger.info(f"Template test completed successfully: {task_id}")
            # Template test results are handled by the admin interface polling

        else:
            # Unknown task type
            logger.warning(f"Unhandled task type in success handler: {task_name}")
            
    except Exception as e:
        logger.error(f"Error handling task success: {str(e)}", exc_info=True)

@task_failure.connect
def handle_task_failure(sender=None, exception=None, task_id=None, **kwargs):
    """
    Signal handler for failed Celery task.
    
    This function is called when a Celery task fails.
    It sends an error message to the appropriate handlers and WebSocket clients.
    
    Args:
        sender: The task that generated the signal
        exception: The exception that caused the failure
        task_id: The ID of the failed task
        kwargs: Additional keyword arguments
    """
    try:
        # Get task information
        task_name = sender.name if sender else "Unknown task"
        task_id = task_id or (sender.request.id if sender else "Unknown task ID")
        
        logger.error(f"Task failed: {task_name} ({task_id}) - {str(exception)}")
        
        # Extract workflow information if available
        workflow_id = None
        workflow_type = None
        user_ws_session = None
        
        # Extract additional info from kwargs
        if hasattr(sender, 'request') and hasattr(sender.request, 'kwargs'):
            task_kwargs = sender.request.kwargs
            # Handle case where task_kwargs might be None (e.g., during timeout)
            if task_kwargs is not None:
                workflow_id = task_kwargs.get('workflow_id')
                workflow_type = task_kwargs.get('workflow_type')
            else:
                logger.warning(f"Task kwargs is None for failed task: {task_id}")
                workflow_id = None
                workflow_type = None

            # For agent nodes, extract from state if present
            if task_kwargs is not None and 'state' in task_kwargs and isinstance(task_kwargs['state'], dict):
                state = task_kwargs['state']
                if not workflow_id and 'workflow_id' in state:
                    workflow_id = state['workflow_id']
        
        # Route to appropriate error handler based on task type
        if 'execute_onboarding_workflow' in task_name:
            # Handle onboarding workflow failure
            # Send a direct message to the 'game' group
            channel_layer = get_channel_layer()
            async_to_sync(channel_layer.group_send)(
                'game',
                {
                    'type': 'onboarding_response',
                    'mentor_response': f"I'm sorry, I encountered an issue while processing your message. Let's try again.",
                    'task_id': task_id,
                    'status': 'failed',
                    'error': str(exception)
                }
            )
            
        elif 'execute_graph_workflow' in task_name or 'execute_agent_node' in task_name:
            # Handle workflow failure with the result handler
            if workflow_id:
                workflow_result_handler.sync_handle_error(
                    workflow_id=workflow_id,
                    error=str(exception),
                    task_id=task_id,
                    workflow_type=workflow_type,
                    user_ws_session=user_ws_session
                )
            else:
                logger.warning(f"No workflow_id found for failed task: {task_id}")

        elif 'run_template_test' in task_name:
            # Handle template test failure
            logger.error(f"Template test failed: {task_id} - {str(exception)}")
            # Template test failures are handled by the admin interface polling

        else:
            # Unknown task type
            logger.warning(f"Unhandled task type in failure handler: {task_name}")
            
    except Exception as e:
        logger.error(f"Error handling task failure: {str(e)}", exc_info=True)

def handle_onboarding_result(result, task_id):
    """
    Handle onboarding workflow result.
    
    Args:
        result: The task result
        task_id: The task ID
    """
    try:
        # Extract mentor response
        mentor_response = result.get('mentor_response', '')
        
        # Get the channel layer
        channel_layer = get_channel_layer()
        
        # Send the result to the 'game' group
        async_to_sync(channel_layer.group_send)(
            'game',
            {
                'type': 'onboarding_response',
                'mentor_response': mentor_response,
                'task_id': task_id,
                'status': 'completed'
            }
        )
        
        logger.info(f"Sent onboarding task result to WebSocket: {task_id}")
        
    except Exception as e:
        logger.error(f"Error handling onboarding result: {str(e)}", exc_info=True)

def handle_graph_workflow_result(result, task_id):
    """
    Handle LangGraph workflow result.
    
    Args:
        result: The task result
        task_id: The task ID
    """
    try:
        # Extract workflow information
        workflow_id = result.get('workflow_id')

        # FIXED: Handle both nested and direct result formats
        # Some workflows return results nested under 'result' key, others return directly
        if 'result' in result and isinstance(result['result'], dict):
            # Nested format: {'workflow_id': '...', 'result': {...}}
            workflow_result = result['result']
        else:
            # Direct format: {'workflow_id': '...', 'user_ws_session_name': '...', 'output_data': {...}, ...}
            workflow_result = result

        if not workflow_id:
            logger.warning(f"No workflow_id in graph workflow result: {task_id}")
            return

        # Determine workflow type from result data
        workflow_type = workflow_result.get('workflow_type')
        if not workflow_type and 'input' in workflow_result:
            workflow_type = workflow_result['input'].get('task_type')

        # Process the result with our handler
        workflow_result_handler.sync_process_result(
            workflow_id=workflow_id,
            result=workflow_result,
            task_id=task_id,
            workflow_type=workflow_type or 'unknown'
        )
        
        logger.info(f"Processed graph workflow result: {workflow_id}")
        
    except Exception as e:
        logger.error(f"Error handling graph workflow result: {str(e)}", exc_info=True)

def handle_wheel_generation_result(result, task_id):
    """
    Handle wheel generation workflow result.

    Args:
        result: The task result from execute_wheel_generation_workflow
        task_id: The task ID
    """
    try:
        logger.info(f"🎡 Processing wheel generation workflow result: {task_id}")

        # Extract workflow information
        workflow_id = result.get('workflow_id')

        if not workflow_id:
            logger.warning(f"No workflow_id in wheel generation result: {task_id}")
            return

        # Wheel generation results are returned directly (not nested)
        workflow_result = result

        # Extract workflow type (should be 'wheel_generation')
        workflow_type = workflow_result.get('workflow_type', 'wheel_generation')

        # Debug logging for wheel generation
        logger.info(f"🎡 Wheel generation result details:")
        logger.info(f"  - Workflow ID: {workflow_id}")
        logger.info(f"  - Workflow Type: {workflow_type}")
        logger.info(f"  - Has output_data: {'output_data' in workflow_result}")
        logger.info(f"  - Has user_ws_session_name: {'user_ws_session_name' in workflow_result}")

        if 'output_data' in workflow_result:
            output_data = workflow_result['output_data']
            logger.info(f"  - Has wheel in output_data: {'wheel' in output_data}")
            if 'wheel' in output_data:
                wheel_data = output_data['wheel']
                logger.info(f"  - Wheel items count: {len(wheel_data.get('items', []))}")

        # Process the result using the workflow result handler
        workflow_result_handler.sync_process_result(
            workflow_id=workflow_id,
            result=workflow_result,
            task_id=task_id,
            workflow_type=workflow_type
        )

        logger.info(f"✅ Successfully processed wheel generation result: {workflow_id}")

    except Exception as e:
        logger.error(f"❌ Error handling wheel generation result: {str(e)}", exc_info=True)

def handle_agent_node_result(result, task_id):
    """
    Handle individual agent node result.
    
    Args:
        result: The task result
        task_id: The task ID
    """
    try:
        # Extract information
        output_data = result.get('output_data', {})
        updated_state = result.get('updated_state', {})
        run_id = result.get('run_id')
        
        # Check if this is the final node in a workflow
        if updated_state and updated_state.get('completed'):
            workflow_id = updated_state.get('workflow_id')
            
            if workflow_id:
                # Determine workflow type
                workflow_type = None
                if 'input' in updated_state and isinstance(updated_state['input'], dict):
                    workflow_type = updated_state['input'].get('task_type')
                
                # Process as a completed workflow
                workflow_result_handler.sync_process_result(
                    workflow_id=workflow_id,
                    result={
                        'output_data': output_data,
                        'state': updated_state
                    },
                    task_id=task_id,
                    workflow_type=workflow_type or 'unknown'
                )
                
                logger.info(f"Processed final agent node result as workflow completion: {workflow_id}")
        
    except Exception as e:
        logger.error(f"Error handling agent node result: {str(e)}", exc_info=True)


    