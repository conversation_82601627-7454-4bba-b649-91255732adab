from celery import shared_task
import logging
import asyncio
from typing import Dict, Any, Optional

from apps.main.graphs.wheel_generation_graph import run_wheel_generation_workflow
from apps.main.graphs.profile_completion_graph import run_profile_completion_workflow, run_onboarding_workflow
from apps.main.graphs.discussion_graph import run_discussion_workflow # Added import
from apps.main.graphs.post_spin_graph import run_post_spin_workflow # Added post-spin workflow
from apps.main.graphs.post_activity_graph import run_post_activity_workflow # Added post-activity workflow
from apps.user.models import UserProfile
from apps.main.models import HistoryEvent # Keep UserFeedback and ActivityTailored if needed by graphs
# Import UserFeedback and ActivityTailored if they are used indirectly by the workflows or history logging
# from apps.main.models import UserFeedback, ActivityTailored
from django.contrib.contenttypes.models import ContentType
from apps.main.services.event_service import EventService # Import EventService

logger = logging.getLogger(__name__)

@shared_task(bind=True, name="execute_graph_workflow")
def execute_graph_workflow(self, 
                           workflow_type: str, 
                           user_profile_id: str, 
                           initial_input: Dict[str, Any], 
                           workflow_id: Optional[str] = None):
    """
    Execute a complete workflow using LangGraph.
    
    Args:
        workflow_type: Type of workflow to execute (e.g., 'wheel_generation', 'onboarding')
        user_profile_id: The ID of the user profile
        initial_input: Initial context information
        workflow_id: Optional workflow ID (will generate a new one if not provided)
        
    Returns:
        dict: Workflow result
    """
    try:
        # Select the appropriate workflow based on type
        workflow_runners = {
            'wheel_generation': run_wheel_generation_workflow,
            'profile_completion': run_profile_completion_workflow,
            'onboarding': run_onboarding_workflow,  # Unified: Routes to profile_completion_graph.py
            'discussion': run_discussion_workflow, # Added discussion workflow
            'post_spin': run_post_spin_workflow, # Added post-spin workflow
            'post_activity': run_post_activity_workflow # Added post-activity workflow
        }
        
        # Get the corresponding workflow runner
        workflow_runner = workflow_runners.get(workflow_type)
        if not workflow_runner:
            raise ValueError(f"Unknown workflow type: {workflow_type}")
        
        # Run the workflow (using asyncio to handle async methods)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # FIXED: Extract workflow_input from initial_input for benchmarking interface
            # This enables real database access for user-initiated workflows
            workflow_input = initial_input.get('workflow_input') if isinstance(initial_input, dict) else None
            context_packet = initial_input.get('context_packet', initial_input) if isinstance(initial_input, dict) else initial_input

            # DEBUG: Log the parameters being passed to the workflow
            logger.info(f"🔧 DEBUG: workflow_input present: {workflow_input is not None}")
            if isinstance(context_packet, dict):
                logger.info(f"🔧 DEBUG: context_packet keys: {list(context_packet.keys())}")
                logger.info(f"🔧 DEBUG: user_ws_session_name in context_packet: {'user_ws_session_name' in context_packet}")
                if 'user_ws_session_name' in context_packet:
                    logger.info(f"🔧 DEBUG: user_ws_session_name value: {context_packet['user_ws_session_name']}")

            # Call workflow with proper parameters for benchmarking interface
            if workflow_input is not None:
                # Use benchmarking interface with execution mode parameters
                result = loop.run_until_complete(
                    workflow_runner(
                        user_profile_id,
                        context_packet,
                        workflow_id,
                        workflow_input  # Pass workflow_input as 4th parameter
                    )
                )
            else:
                # Use legacy interface (backward compatibility)
                result = loop.run_until_complete(
                    workflow_runner(
                        user_profile_id,
                        initial_input,
                        workflow_id
                    )
                )
        finally:
            loop.close()
        
        # Record workflow completion in history
        try:
            # Get user profile content type
            user_profile = UserProfile.objects.get(id=user_profile_id)
            content_type = ContentType.objects.get_for_model(UserProfile)
            
            # Create history event
            HistoryEvent.objects.create(
                event_type=f'{workflow_type}_workflow',
                content_type=content_type,
                object_id=user_profile_id,
                user_profile=user_profile,
                details={
                    'workflow_id': workflow_id or result.get('workflow_id', str(self.request.id)),
                    'status': 'completed' if not result.get('error', None) else 'failed',
                    'workflow_type': workflow_type,
                    'workflow_summary': result.get('summary', {})
                }
            )
        except Exception as e:
            logger.error(f"Error recording workflow history: {str(e)}")
            # Non-critical error, don't re-raise

        # --- REMOVED: Duplicate response emission ---
        # The workflow itself handles sending responses to the user via WebSocket.
        # Emitting an additional response here was causing duplicate messages.
        # This section has been removed to fix the duplicate response issue.
        logger.info(f"Workflow {workflow_type} completed for user {user_profile_id}. Response handled by workflow.")
        # --- End of removed section ---


        # Return result, converting to dict if needed
        return result.dict() if hasattr(result, 'dict') else result

    except Exception as e:
        import traceback
        # EventService is already imported at the top level now

        error_message = str(e)
        tb_str = traceback.format_exc()
        logger.error(f"Error executing {workflow_type} workflow: {error_message}", exc_info=True)

        # Emit debug info event with traceback
        # Use EventService.emit_event_sync as we are in a sync Celery task context
        EventService.emit_event_sync(
            event_type='debug_info',
            data={
                'level': 'error',
                'message': f"Celery task failed during {workflow_type} workflow execution.",
                'source': f"CeleryTask:{workflow_type}",
                'details': {
                    'error_message': error_message,
                    'exception_type': type(e).__name__,
                    'traceback': tb_str
                }
            },
            user_profile_id=user_profile_id
            # Assuming session_id isn't readily available here, rely on user_profile_id targeting
        )

        # Return error dictionary as before
        return {
            "error": error_message,
            "workflow_type": workflow_type,
            "workflow_id": workflow_id,
            "completed": False,
            "user_profile_id": user_profile_id
        }
