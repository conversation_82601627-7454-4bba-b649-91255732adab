import asyncio
import uuid
import datetime
import logging
from typing import Dict, Any, Optional
import json
from channels.db import database_sync_to_async # Import the decorator
from channels.layers import get_channel_layer # Import channel layer
from datetime import datetime, timezone # Ensure timezone is imported

# Import tool utilities
from apps.main.agents.tools.tools_util import execute_tool
# Import MentorService
from apps.main.services.mentor_service import MentorService

# Set up logger for the dispatcher
logger = logging.getLogger(__name__)

class ConversationDispatcher:
    """
    Central message routing hub that processes user messages and orchestrates workflow execution.

    This dispatcher serves as the primary entry point for all user interactions, leveraging
    the MentorService singleton architecture to maintain consistent user context and state
    across workflow boundaries.

    Key Responsibilities:
    - Message preprocessing through MentorService
    - Context extraction and enhancement
    - Intelligent workflow classification
    - Workflow initiation with comprehensive context packets
    - Integration with enhanced metadata collection system

    Architecture Integration:
    - Integrates with MentorService singleton for persistent user state
    - Leverages enhanced metadata collection for improved context
    - Supports real-mode execution with proper error handling
    - Provides comprehensive debugging and monitoring capabilities
    """

    def __init__(self, user_profile_id: str, user_ws_session_name: Optional[str] = None, fail_fast_on_errors: bool = False, llm_config_id: Optional[str] = None):
        """
        Initialize the conversation dispatcher with enhanced architecture integration.

        Args:
            user_profile_id: The ID of the user profile
            user_ws_session_name: Optional WebSocket session name for result routing
            fail_fast_on_errors: If True, errors in individual steps will cause the entire process to fail
                                instead of using fallback values (useful for testing)
            llm_config_id: Optional LLM config ID for debug mode
        """
        self.user_profile_id = user_profile_id
        self.user_ws_session_name = user_ws_session_name
        self.fail_fast_on_errors = fail_fast_on_errors
        self.llm_config_id = llm_config_id

        # Initialize performance tracking
        self._initialization_start = asyncio.get_event_loop().time() if asyncio.get_event_loop().is_running() else None

        # Initialize MentorService for this user (singleton pattern)
        try:
            self.mentor_service = MentorService.get_instance(
                user_profile_id=user_profile_id,
                session_id=user_ws_session_name
            )
            logger.debug(f"MentorService initialized for user {user_profile_id}")
        except Exception as e:
            logger.error(f"Failed to initialize MentorService: {str(e)}", exc_info=True)
            self.mentor_service = None

        # Initialize LLM client for classification with enhanced error handling
        self.llm_client = None
        self._llm_initialization_error = None
        try:
            from apps.main.llm.service import RealLLMClient

            # Use specific LLM config if provided (debug mode)
            if llm_config_id:
                try:
                    from apps.main.models import LLMConfig
                    llm_config = LLMConfig.objects.get(id=llm_config_id)
                    self.llm_client = RealLLMClient(llm_config=llm_config)
                    logger.info(f"Using debug LLM config: {llm_config.name} (ID: {llm_config_id})")
                except Exception as config_error:
                    logger.warning(f"Failed to load LLM config {llm_config_id}: {config_error}, using default")
                    self.llm_client = RealLLMClient()
            else:
                self.llm_client = RealLLMClient()

            logger.debug(f"LLM client initialized for ConversationDispatcher")
        except Exception as e:
            self._llm_initialization_error = str(e)

            # Check if we're in a production environment where fallbacks should not be allowed
            import os
            execution_mode = os.getenv('GOALI_DEFAULT_EXECUTION_MODE', 'development')

            if execution_mode == 'production':
                # In production, LLM failures should not fall back silently
                logger.error(f"LLM client initialization failed in production mode: {str(e)}")
                raise RuntimeError(f"LLM client initialization failed in production mode: {str(e)}") from e
            else:
                # In development/testing, allow fallback but log warning
                logger.warning(f"Failed to initialize LLM client: {str(e)}. Will use rule-based classification fallback.")

                # Schedule async debug info sending (non-blocking)
                if asyncio.get_event_loop().is_running():
                    asyncio.create_task(self._send_debug_info(
                        'warning',
                        f"LLM Client Initialization Failed: {str(e)}",
                        {
                            'exception_type': type(e).__name__,
                            'fallback_active': True,
                            'user_profile_id': user_profile_id,
                            'execution_mode': execution_mode
                        }
                    ))

        logger.info(f"ConversationDispatcher initialized for user {user_profile_id} "
                   f"(MentorService: {'✓' if self.mentor_service else '✗'}, "
                   f"LLM: {'✓' if self.llm_client else '✗ (fallback)'})")

    async def _send_debug_info(self, level: str, message: str, details: Optional[Dict[str, Any]] = None):
        """Helper to send debug info to the user's session group if available."""
        from apps.main.services.event_service import EventService

        await EventService.emit_debug_info(
            level=level,
            message=message,
            source='ConversationDispatcher',
            details=details,
            user_profile_id=self.user_profile_id,
            session_id=self.user_ws_session_name
        )

    async def process_message(self, user_message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process initial user message with enhanced architecture integration.

        This method orchestrates the complete message processing pipeline:
        1. MentorService preprocessing for user context enhancement
        2. Profile completion assessment for onboarding determination
        3. Enhanced context extraction leveraging metadata collection
        4. Intelligent workflow classification with confidence scoring
        5. Comprehensive context packet construction
        6. Workflow initiation with enhanced monitoring

        Args:
            user_message: The user's message content and metadata

        Returns:
            dict: Response data including workflow selection, context, and enhanced metadata
        """
        processing_start_time = asyncio.get_event_loop().time()
        message_preview = user_message.get('text', '')[:50]
        logger.info(f"Processing message for user {self.user_profile_id}: {message_preview}...")

        try:
            # Generate a workflow ID for this conversation
            workflow_id = str(uuid.uuid4())

            # Store the initial user message in conversation history
            try:
                from apps.main.agents.tools.tools_util import execute_tool
                await execute_tool(
                    tool_code="store_conversation_message",
                    tool_input={
                        "user_profile_id": self.user_profile_id,
                        "message_content": user_message.get('text', ''),
                        "message_role": "user",
                        "message_metadata": {
                            "workflow_id": workflow_id,
                            "session_id": self.user_ws_session_name
                        }
                    },
                    user_profile_id=self.user_profile_id,
                    session_id=self.user_ws_session_name
                )
                logger.debug(f"Stored user message in conversation history for user {self.user_profile_id}")
            except Exception as e:
                logger.warning(f"Failed to store user message in conversation history: {e}")

            # Enhanced Step 0: Route message through MentorService with error handling
            enhanced_message = user_message  # Default fallback
            mentor_context_available = False

            if self.mentor_service:
                try:
                    enhanced_message = await self.mentor_service.process_incoming_message(user_message)
                    mentor_context_available = 'mentor_context' in enhanced_message
                    await self._send_debug_info('debug', 'Message processed by MentorService', {
                        'has_mentor_context': mentor_context_available,
                        'trust_level': enhanced_message.get('mentor_context', {}).get('trust_level'),
                        'processing_time_ms': round((asyncio.get_event_loop().time() - processing_start_time) * 1000, 2)
                    })
                except Exception as e:
                    logger.warning(f"MentorService processing failed: {str(e)}")
                    await self._send_debug_info('warning', 'MentorService processing failed, using original message', {
                        'error': str(e),
                        'fallback_active': True
                    })

            # Enhanced Step 1: Check profile completion and analyze gaps
            profile_status = 0.0  # Default fallback
            profile_gaps = {}
            try:
                profile_status = await self._check_profile_completion()
                profile_gaps = await self._analyze_profile_gaps()
                await self._send_debug_info('debug', 'Profile analysis completed', {
                    'completion_percentage': profile_status,
                    'critical_gaps': len(profile_gaps.get('critical', [])),
                    'important_gaps': len(profile_gaps.get('important', [])),
                    'onboarding_needed': profile_status < 0.5
                })
            except Exception as e:
                if self.fail_fast_on_errors:
                    raise  # Re-raise the exception to fail fast
                logger.warning(f"Profile analysis failed: {str(e)}")
                await self._send_debug_info('warning', 'Profile analysis failed, using defaults', {
                    'error': str(e),
                    'default_status': profile_status
                })

            # Enhanced Step 2: Extract context with enhanced metadata support
            context_extraction = {}  # Default fallback
            try:
                context_extraction = await self._extract_message_context(enhanced_message)
                await self._send_debug_info('debug', 'Context extracted successfully', {
                    'context_keys': list(context_extraction.keys()),
                    'extraction_confidence': context_extraction.get('extraction_confidence', 0.0),
                    'has_enhanced_metadata': mentor_context_available
                })
            except Exception as e:
                if self.fail_fast_on_errors:
                    raise  # Re-raise the exception to fail fast
                logger.warning(f"Context extraction failed: {str(e)}")
                context_extraction = self._get_fallback_context()
                await self._send_debug_info('warning', 'Context extraction failed, using fallback', {
                    'error': str(e),
                    'fallback_context': list(context_extraction.keys())
                })

            # Enhanced Step 3: Classify message with state awareness and improved confidence scoring
            workflow_classification = {'workflow_type': 'discussion', 'confidence': 0.5, 'reason': 'fallback'}  # Default

            # Extract conversation state from metadata
            metadata = user_message.get('metadata', {})
            conversation_phase = metadata.get('conversation_phase', 'initial')
            awaiting_response = metadata.get('awaiting_response_type')
            session_context = metadata.get('session_context', {})

            # Enhanced Step 3: Check for wheel requests and provide direct response with consistent routing
            direct_response_result = None
            try:
                direct_response_result = await self._handle_wheel_request_with_direct_response(enhanced_message, profile_gaps)
                if direct_response_result:
                    await self._send_debug_info('info', 'Direct response provided for wheel request', {
                        'response_type': 'wheel_request',
                        'workflow_type': direct_response_result.get('workflow_type'),
                        'has_direct_response': True
                    })
            except Exception as e:
                logger.warning(f"Direct response handling failed: {str(e)}")
                await self._send_debug_info('warning', 'Direct response handling failed', {
                    'error': str(e)
                })

            # Enhanced Step 4: Classify message with enhanced context and error handling
            workflow_classification = {"workflow_type": "discussion", "confidence": 0.5, "reason": "Default fallback"}

            if direct_response_result:
                # Use direct response result as classification
                workflow_classification = {
                    'workflow_type': direct_response_result.get('workflow_type', 'discussion'),
                    'confidence': direct_response_result.get('confidence', 0.8),
                    'reason': direct_response_result.get('reason', 'Direct response handling'),
                    'direct_response': direct_response_result.get('direct_response'),
                    'profile_gaps': direct_response_result.get('profile_gaps', []),
                    'conversation_state_update': direct_response_result.get('conversation_state_update'),  # CRITICAL FIX: Include conversation state update
                    'launch_workflow': direct_response_result.get('launch_workflow', False)  # CRITICAL FIX: Include launch_workflow flag
                }
                await self._send_debug_info('info', 'Using direct response classification', {
                    'workflow_type': workflow_classification.get('workflow_type'),
                    'has_direct_response': True,
                    'launch_workflow': workflow_classification.get('launch_workflow', False)
                })
            else:
                try:
                    if awaiting_response:
                        # Handle follow-up responses with state awareness
                        workflow_classification = await self._handle_follow_up_message(
                            enhanced_message, awaiting_response, session_context, context_extraction, profile_status
                        )
                    else:
                        # Normal classification flow with profile gap awareness
                        workflow_classification = await self._classify_message(
                            enhanced_message,  # Use enhanced message for better classification
                            context_extraction,
                            profile_status
                        )

                        # Enhance classification with profile gap information
                        # Only override wheel generation if critical gaps exist AND it's not an explicit wheel request
                        if (profile_gaps.get('critical') and
                            workflow_classification.get('workflow_type') == 'wheel_generation' and
                            not self._is_explicit_wheel_request(enhanced_message.get('text', ''))):
                            # Override wheel generation if critical gaps exist for non-explicit requests
                            workflow_classification = {
                                'workflow_type': 'onboarding',
                                'confidence': 0.9,
                                'reason': f"Critical profile gaps detected: {', '.join([gap['field'] for gap in profile_gaps['critical']])}",
                                'launch_workflow': True  # CRITICAL FIX: Ensure onboarding workflows launch
                            }

                    await self._send_debug_info('info', 'Message classified successfully', {
                        'workflow_type': workflow_classification.get('workflow_type', 'unknown'),
                        'confidence': workflow_classification.get('confidence', 0.0),
                        'reason': workflow_classification.get('reason', 'no reason provided'),
                        'mentor_context_used': mentor_context_available,
                        'has_direct_response': 'direct_response' in workflow_classification,
                        'launch_workflow': workflow_classification.get('launch_workflow', False)
                    })
                except Exception as e:
                    if self.fail_fast_on_errors:
                        raise  # Re-raise the exception to fail fast
                    logger.warning(f"Message classification failed: {str(e)}")
                    await self._send_debug_info('warning', 'Message classification failed, using fallback', {
                        'error': str(e),
                        'fallback_workflow': workflow_classification.get('workflow_type', 'discussion')
                    })
            # Enhanced Step 4: Build comprehensive context packet with enhanced metadata
            context_packet = {}
            try:
                context_packet = self._build_context_packet(
                    context_extraction,
                    enhanced_message,
                    workflow_classification,
                    mentor_context_available
                )
                await self._send_debug_info('debug', 'Context packet built successfully', {
                    'packet_keys': list(context_packet.keys()),
                    'has_mentor_context': mentor_context_available,
                    'workflow_type': workflow_classification.get('workflow_type', 'unknown')
                })
            except Exception as e:
                if self.fail_fast_on_errors:
                    raise  # Re-raise the exception to fail fast
                logger.warning(f"Context packet building failed: {str(e)}")
                context_packet = self._get_fallback_context_packet(context_extraction)
                await self._send_debug_info('warning', 'Context packet building failed, using fallback', {
                    'error': str(e),
                    'fallback_keys': list(context_packet.keys())
                })

            # Step 5: Check if additional information is needed
            action_required_info = self._check_if_action_required(
                workflow_classification["workflow_type"],
                context_extraction,
                user_message
            )
            await self._send_debug_info('debug', 'Checked if action required', {'action_required': action_required_info or 'None'})

            # --- START CHANGE ---
            # Determine the actual workflow to launch
            workflow_to_launch = workflow_classification["workflow_type"]
            if action_required_info:
                # If action is required, override the launch target to 'discussion'
                workflow_to_launch = 'discussion'
                # Add target workflow and missing field to the context for the discussion flow
                context_packet['target_workflow'] = workflow_classification["workflow_type"]
                context_packet['missing_field'] = action_required_info['missing_field']
                # Removed 'collection_goal' for standardization, the discussion flow can use the missing_field context.
                await self._send_debug_info('info', f"Action required, overriding workflow to 'discussion'. Target: {workflow_classification['workflow_type']}", {'missing_field': action_required_info['missing_field']})
            # --- END CHANGE ---

            # Step 6: Enhanced MentorService coordination with contextual tool injection
            if self.mentor_service:
                try:
                    # Update workflow state
                    await self.mentor_service.update_workflow_state(workflow_id, workflow_to_launch)

                    # Enhanced Phase 2: Coordinate contextual tool injection based on workflow and profile gaps
                    await self._coordinate_mentor_tools_and_instructions(
                        workflow_to_launch,
                        profile_gaps,
                        context_extraction,
                        workflow_classification
                    )

                    # CRITICAL FIX: Pass specific instructions in context packet for cross-process communication
                    # This ensures the Mentor agent in the Celery worker gets the specific gap-based instructions
                    if workflow_to_launch == 'onboarding' and profile_gaps:
                        next_priority = profile_gaps.get('next_priority_field', {})
                        if next_priority:
                            context_packet['mentor_specific_instructions'] = {
                                'priority_field': next_priority.get('field', 'information'),
                                'specific_question': next_priority.get('question', 'Could you tell me more about yourself?'),
                                'context_hint': next_priority.get('context_hint', ''),
                                'critical_gaps_count': len(profile_gaps.get('critical', [])),
                                'instruction_type': 'profile_gap_targeted'
                            }
                            logger.info(f"🎯 Added specific instructions to context packet: {next_priority.get('question', 'N/A')[:50]}...")

                except Exception as e:
                    logger.warning(f"MentorService coordination failed: {str(e)}")
                    await self._send_debug_info('warning', 'MentorService coordination failed', {
                        'error': str(e),
                        'workflow_type': workflow_to_launch
                    })

            # Step 7: Launch the workflow (using the potentially overridden type)
            # CRITICAL FIX: Enhanced workflow launch logic for profile completion
            # This ensures profile completion workflows actually run when users provide information
            should_launch_workflow = (
                workflow_to_launch != "direct_response_only" and
                not workflow_classification.get('direct_response_only', False)
            ) or (
                # Exception: Always launch onboarding/profile_completion workflow when explicitly requested
                (workflow_to_launch == "onboarding" or workflow_to_launch == "profile_completion") and
                workflow_classification.get('launch_workflow', False)
            )

            if not should_launch_workflow:
                await self._send_debug_info('info', f"Direct response only - not launching workflow", {
                    'workflow_id': workflow_id,
                    'reason': 'Waiting for user response to profile information request',
                    'direct_response_only_flag': workflow_classification.get('direct_response_only', False)
                })
                logger.info(f"🔄 Direct response sent, waiting for user response (workflow_id: {workflow_id})")
            else:
                # Send the full context packet in debug details, not just keys
                await self._send_debug_info('info', f"Launching workflow: {workflow_to_launch}", {'workflow_id': workflow_id, 'context_packet': context_packet})
                self._launch_workflow(
                    workflow_to_launch, # Use the potentially modified workflow type
                    context_packet,
                    workflow_id
                )

            # Prepare response with all the information
            response = {
                'workflow_id': workflow_id,
                # Report the *intended* workflow type, even if discussion is launched for info gathering
                'workflow_type': workflow_classification["workflow_type"],
                'confidence': workflow_classification["confidence"],
                'context_packet': context_packet, # Context now includes target/missing info if needed
                'status': 'initiated',
                'session_timestamp': context_packet.get('session_timestamp'),
                'estimated_completion_time': self._estimate_completion_time(workflow_to_launch) # Estimate based on actual launched workflow
            }

            # Add action required info if it exists (for the consumer/frontend awareness)
            if action_required_info:
                response['action_required'] = action_required_info

            # Add direct response if it exists (for immediate user feedback)
            if workflow_classification.get('direct_response'):
                response['direct_response'] = workflow_classification['direct_response']
                response['profile_gaps'] = workflow_classification.get('profile_gaps', [])

                # CRITICAL FIX: Send immediate response to user via WebSocket
                await self._send_immediate_user_response(workflow_classification['direct_response'])

            # Add conversation state update if it exists and send it immediately
            if workflow_classification.get('conversation_state_update'):
                response['conversation_state_update'] = workflow_classification['conversation_state_update']
                logger.debug(f"Added conversation_state_update to response: {workflow_classification['conversation_state_update']}")

                # CRITICAL FIX: Send conversation state update immediately via WebSocket
                try:
                    await self._send_conversation_state_update(workflow_classification['conversation_state_update'])
                    logger.info(f"Sent conversation state update via WebSocket: {workflow_classification['conversation_state_update']}")
                except Exception as e:
                    logger.error(f"Failed to send conversation state update via WebSocket: {str(e)}")

            # Step 7: Record workflow initiation (using the actual launched type)
            # Record if we actually launched a workflow (including onboarding with launch_workflow flag)
            if should_launch_workflow:
                await self._record_workflow_initiation(
                    workflow_to_launch, # Record the actual workflow launched
                    workflow_id,
                    context_packet
                )

            # Debug: Log final response keys before returning
            logger.debug(f"Final response keys before return: {list(response.keys())}")
            return response

        except Exception as e:
            logger.error(f"Error in dispatcher: {str(e)}", exc_info=True)

            # Send detailed debug info on error, including traceback
            try:
                import traceback
                tb_str = traceback.format_exc()
                await self._send_debug_info(
                    'error',
                    f"Dispatcher error: {str(e)}",
                    {'exception_type': type(e).__name__, 'traceback': tb_str}
                )
            except Exception as debug_error:
                logger.warning(f"Failed to send debug info: {debug_error}")

            # Return error response
            return {
                "status": "error",
                "error": str(e),
                "fallback_response": "I'm having trouble processing your request right now. Please try again."
            }

    async def _check_profile_completion(self) -> float:
        """
        Check the user's profile completion status using the dedicated tool.

        Returns:
            float: Completion percentage (0.0 to 1.0)
        """
        try:
            # Use the get_user_profile tool and extract completion info
            # --- BEGIN FIX: Wrap arguments in 'input_data' ---
            result = await execute_tool(
                tool_code="get_user_profile",
                tool_input={"input_data": {"user_profile_id": self.user_profile_id}}, # Correct structure
                user_profile_id=self.user_profile_id, # Pass for event targeting
                session_id=self.user_ws_session_name # Pass for event targeting
            )
            # --- END FIX ---
            # Extract completion percentage from the nested structure
            user_profile_data = result.get("user_profile", {})
            return user_profile_data.get("profile_completion", 0.0) # Default to 0% for empty profiles
        except Exception as e:
            logger.warning(f"Error getting user profile for completion check: {str(e)}")
            # Default to 0% completion if the tool fails - this ensures onboarding is triggered
            return 0.0

    async def _analyze_profile_gaps(self) -> Dict[str, Any]:
        """
        Enhanced profile gap analysis with intelligent prioritization and contextual recommendations.

        Phase 2 Enhancement: Provides detailed analysis of missing critical data (resources, aspirations,
        preferences) with context-aware decision making for profile completion needs.

        Returns:
            dict: Analysis of profile gaps with prioritized recommendations and contextual guidance
        """
        try:
            # ARCHITECTURAL FIX: Use consistent tool invocation pattern
            # This ensures proper input format and follows the established tool contract
            result = await self._call_user_profile_tool()

            user_profile = result.get("user_profile", {})
            completion_percentage = user_profile.get("profile_completion", 0.0)

            # Debug logging to track the fix
            logger.debug(f"Profile gap analysis for user {self.user_profile_id}: completion={completion_percentage:.1%}, "
                        f"demographics={'✓' if user_profile.get('demographics') else '✗'}, "
                        f"goals={len(user_profile.get('goals', []))}, "
                        f"current_env={'✓' if user_profile.get('current_environment') else '✗'}")

            # Enhanced gap analysis with contextual intelligence
            gaps = {
                'critical': [],
                'important': [],
                'optional': [],
                'completion_percentage': completion_percentage,
                'profile_readiness': self._assess_profile_readiness(completion_percentage),
                'next_priority_field': None,
                'contextual_guidance': {}
            }

            # Critical gaps analysis - blocks wheel generation
            critical_fields = [
                {
                    'field': 'basic_demographics',
                    'data_key': 'demographics',
                    'description': 'Basic personal information',
                    'question': 'Could you tell me a bit about yourself? (age, location, current situation)',
                    'context_hint': 'Basic information helps me understand your context and suggest appropriate activities.',
                    'priority_weight': 10
                },
                {
                    'field': 'goals_aspirations',
                    'data_key': 'goals',
                    'description': 'Goals and aspirations',
                    'question': 'What are your main goals or things you want to achieve?',
                    'context_hint': 'Your goals help me tailor activities that support what matters most to you.',
                    'priority_weight': 9,
                    'min_count': 1  # Need at least 1 goal
                },
                {
                    'field': 'current_environment',
                    'data_key': 'current_environment',
                    'description': 'Current environment and context',
                    'question': 'Can you tell me about your current environment and situation?',
                    'context_hint': 'Understanding your environment helps me suggest activities that work in your space.',
                    'priority_weight': 8
                }
            ]

            # Check critical fields
            for field_info in critical_fields:
                data_value = user_profile.get(field_info['data_key'])

                # Handle different data types
                if field_info.get('min_count'):
                    # For lists/arrays, check if we have minimum count
                    if not data_value or (isinstance(data_value, list) and len(data_value) < field_info['min_count']):
                        gaps['critical'].append(field_info)
                else:
                    # For other fields, check if they exist and are not None/empty
                    if not data_value:
                        gaps['critical'].append(field_info)

            # Important gaps analysis - affects quality
            important_fields = [
                {
                    'field': 'traits',
                    'data_key': 'traits',
                    'description': 'Personal traits and characteristics',
                    'question': 'How would you describe your personality and traits?',
                    'context_hint': 'Understanding your personality helps me match activities to your style.',
                    'priority_weight': 7,
                    'min_count': 3  # Need at least 3 traits for good personalization
                },
                {
                    'field': 'current_mood',
                    'data_key': 'current_mood',
                    'description': 'Current emotional state',
                    'question': 'How are you feeling right now?',
                    'context_hint': 'Your current mood helps me suggest activities that fit how you\'re feeling.',
                    'priority_weight': 6
                },
                {
                    'field': 'beliefs',
                    'data_key': 'beliefs',
                    'description': 'Personal beliefs and values',
                    'question': 'What are some of your core beliefs or values?',
                    'context_hint': 'Your beliefs help me suggest activities that align with your values.',
                    'priority_weight': 5,
                    'min_count': 2  # Need at least 2 beliefs for value alignment
                }
            ]

            # Check important fields
            for field_info in important_fields:
                data_value = user_profile.get(field_info['data_key'])

                # Handle different data types
                if field_info.get('min_count'):
                    # For lists/arrays, check if we have minimum count
                    if not data_value or (isinstance(data_value, list) and len(data_value) < field_info['min_count']):
                        gaps['important'].append(field_info)
                else:
                    # For other fields, check if they exist and are not None/empty
                    if not data_value:
                        gaps['important'].append(field_info)

            # Optional gaps analysis - nice to have
            optional_fields = [
                {
                    'field': 'trust_level',
                    'data_key': 'trust_level',
                    'description': 'Trust and confidence level',
                    'question': 'How confident do you feel about trying new activities?',
                    'context_hint': 'Your confidence level helps me suggest appropriately challenging activities.',
                    'priority_weight': 4
                }
            ]

            # Check optional fields
            for field_info in optional_fields:
                data_value = user_profile.get(field_info['data_key'])

                # Handle different data types
                if field_info.get('min_count'):
                    # For lists/arrays, check if we have minimum count
                    if not data_value or (isinstance(data_value, list) and len(data_value) < field_info['min_count']):
                        gaps['optional'].append(field_info)
                else:
                    # For other fields, check if they exist and are not None/empty
                    if not data_value:
                        gaps['optional'].append(field_info)

            # Determine next priority field for focused questioning
            all_gaps = gaps['critical'] + gaps['important'] + gaps['optional']
            if all_gaps:
                # Sort by priority weight (highest first)
                all_gaps.sort(key=lambda x: x['priority_weight'], reverse=True)
                gaps['next_priority_field'] = all_gaps[0]

            # Generate contextual guidance
            gaps['contextual_guidance'] = self._generate_contextual_guidance(gaps, completion_percentage)

            logger.debug(f"Enhanced profile gap analysis: {len(gaps['critical'])} critical, "
                        f"{len(gaps['important'])} important, {len(gaps['optional'])} optional, "
                        f"readiness: {gaps['profile_readiness']}")

            return gaps

        except Exception as e:
            logger.warning(f"Error in enhanced profile gap analysis: {str(e)}")
            return {
                'critical': [],
                'important': [],
                'optional': [],
                'completion_percentage': 0.0,  # Default to 0% to ensure onboarding is triggered
                'profile_readiness': 'insufficient',
                'next_priority_field': None,
                'contextual_guidance': {'error': str(e)},
                'error': str(e)
            }

    def _is_explicit_wheel_request(self, message_text: str) -> bool:
        """
        Check if the message is an explicit wheel/activity request.

        NOTE: This method only detects wheel requests - it does NOT bypass profile completion.
        Profile completion requirements are enforced separately in the main workflow logic.

        Args:
            message_text: The user's message text

        Returns:
            bool: True if this is a wheel/activity request (regardless of profile completion)
        """
        if not message_text:
            return False

        message_lower = message_text.lower()

        # Comprehensive list of wheel/activity request keywords
        # These indicate user intent for activities, but don't bypass profile requirements
        wheel_request_keywords = [
            "wheel", "activity", "activities", "suggest", "suggestion", "recommendations", "recommend",
            "what should i do", "help me choose", "give me something", "need ideas", "bored",
            "what can i do", "help me decide", "show me options", "give me options",
            "generate wheel", "create wheel", "spin wheel", "show me activities",
            "give me activities", "i want activities", "need activities", "make me a wheel"
        ]

        return any(keyword in message_lower for keyword in wheel_request_keywords)

    def _assess_profile_readiness(self, completion_percentage: float) -> str:
        """
        Assess profile readiness for wheel generation based on completion percentage.

        Args:
            completion_percentage: Profile completion percentage (0.0 to 1.0)

        Returns:
            str: Readiness level ('ready', 'partial', 'insufficient')
        """
        if completion_percentage >= 0.7:
            return 'ready'
        elif completion_percentage >= 0.3:
            return 'partial'
        else:
            return 'insufficient'

    def _generate_contextual_guidance(self, gaps: Dict[str, Any], completion_percentage: float) -> Dict[str, Any]:
        """
        Generate contextual guidance for profile completion based on current gaps.

        Args:
            gaps: Profile gaps analysis
            completion_percentage: Current profile completion percentage

        Returns:
            dict: Contextual guidance for profile completion
        """
        guidance = {
            'completion_status': self._assess_profile_readiness(completion_percentage),
            'next_steps': [],
            'encouragement': '',
            'estimated_time': '2-3 minutes'
        }

        critical_count = len(gaps['critical'])
        important_count = len(gaps['important'])

        if critical_count > 0:
            guidance['next_steps'].append(f"Complete {critical_count} essential field{'s' if critical_count > 1 else ''}")
            guidance['encouragement'] = "Just a few quick questions to get you the best activity recommendations!"
            guidance['estimated_time'] = f"{critical_count * 1} minute{'s' if critical_count > 1 else ''}"
        elif important_count > 0:
            guidance['next_steps'].append(f"Enhance profile with {important_count} additional detail{'s' if important_count > 1 else ''}")
            guidance['encouragement'] = "Your profile looks good! A bit more info will help me give you even better suggestions."
            guidance['estimated_time'] = f"{important_count * 1} minute{'s' if important_count > 1 else ''}"
        else:
            guidance['next_steps'].append("Profile is complete - ready for activities!")
            guidance['encouragement'] = "Great! Your profile has everything I need to suggest perfect activities."
            guidance['estimated_time'] = "0 minutes"

        return guidance

    async def _coordinate_mentor_tools_and_instructions(
        self,
        workflow_type: str,
        profile_gaps: Dict[str, Any],
        context: Dict[str, Any],
        workflow_classification: Dict[str, Any]
    ) -> None:
        """
        Enhanced MentorService coordination for contextual tool injection and instruction enhancement.

        Phase 2 Enhancement: Coordinates with MentorService to provide contextual instructions
        and tools based on identified profile gaps and workflow requirements.

        Args:
            workflow_type: The workflow being launched
            profile_gaps: Profile gap analysis results
            context: Extracted message context
            workflow_classification: Workflow classification results
        """
        if not self.mentor_service:
            return

        try:
            mentor_agent = await self.mentor_service.get_mentor_agent()
            if not mentor_agent:
                logger.warning("Could not get mentor agent for tool coordination")
                return

            # Clear any previous runtime enhancements
            if hasattr(mentor_agent, 'clear_runtime_enhancements'):
                mentor_agent.clear_runtime_enhancements()

            # Coordinate based on workflow type and profile gaps
            if workflow_type == 'onboarding':
                await self._coordinate_onboarding_tools(mentor_agent, profile_gaps, context)
            elif workflow_type == 'wheel_generation':
                await self._coordinate_wheel_generation_tools(mentor_agent, profile_gaps, context)
            elif workflow_type == 'discussion':
                await self._coordinate_discussion_tools(mentor_agent, profile_gaps, context, workflow_classification)

            logger.debug(f"Successfully coordinated MentorService tools for {workflow_type} workflow")

        except Exception as e:
            logger.warning(f"Error in MentorService tool coordination: {str(e)}")

    async def _coordinate_onboarding_tools(self, mentor_agent, profile_gaps: Dict[str, Any], context: Dict[str, Any]) -> None:
        """Coordinate tools and instructions for onboarding workflow."""
        try:
            # Inject profile enrichment tools
            profile_tools = [
                'create_user_demographics',
                'create_user_goal',
                'create_user_preference'
            ]

            if hasattr(mentor_agent, 'inject_tools'):
                mentor_agent.inject_tools(profile_tools)

            # Generate contextual instructions based on profile gaps
            next_priority = profile_gaps.get('next_priority_field', {})
            critical_gaps = profile_gaps.get('critical', [])

            if next_priority:
                field_name = next_priority.get('field', 'information')
                context_hint = next_priority.get('context_hint', '')
                question = next_priority.get('question', 'Could you tell me more about yourself?')

                instructions = f"""
                CONTEXTUAL ENHANCEMENT - Onboarding Mode:

                PRIORITY FOCUS: {field_name}
                QUESTION TO ASK: {question}
                CONTEXT EXPLANATION: {context_hint}

                APPROACH:
                - Be warm, welcoming, and encouraging
                - Ask ONE focused question at a time
                - Explain briefly why this information helps
                - Use the profile enrichment tools to store information
                - Acknowledge and validate user responses

                COMPLETION GOAL: Gather enough information for quality activity recommendations
                """
            else:
                instructions = """
                CONTEXTUAL ENHANCEMENT - Onboarding Mode:

                The user's profile needs basic information for quality activity recommendations.

                APPROACH:
                - Be warm and welcoming
                - Ask about basic information (age, location, goals, preferences)
                - Use profile enrichment tools to store responses
                - Keep questions conversational and friendly
                """

            if hasattr(mentor_agent, 'inject_instructions'):
                mentor_agent.inject_instructions(instructions)

        except Exception as e:
            logger.warning(f"Error coordinating onboarding tools: {str(e)}")

    async def _coordinate_wheel_generation_tools(self, mentor_agent, profile_gaps: Dict[str, Any], context: Dict[str, Any]) -> None:
        """Coordinate tools and instructions for wheel generation workflow."""
        try:
            # For wheel generation, mentor mainly provides encouragement
            instructions = """
            CONTEXTUAL ENHANCEMENT - Wheel Generation Mode:

            The user is getting a personalized activity wheel generated.

            APPROACH:
            - Be encouraging and excited about the wheel being created
            - If the user asks questions, provide helpful information
            - Keep responses brief since the focus is on wheel generation
            - Celebrate the user's profile completeness
            """

            if hasattr(mentor_agent, 'inject_instructions'):
                mentor_agent.inject_instructions(instructions)

        except Exception as e:
            logger.warning(f"Error coordinating wheel generation tools: {str(e)}")

    async def _coordinate_discussion_tools(self, mentor_agent, profile_gaps: Dict[str, Any], context: Dict[str, Any], workflow_classification: Dict[str, Any]) -> None:
        """Coordinate tools and instructions for discussion workflow."""
        try:
            # Check if this is a discussion due to missing information
            target_workflow = workflow_classification.get('target_workflow')

            if target_workflow == 'wheel_generation' and profile_gaps.get('critical'):
                # Discussion to gather missing info before wheel generation
                instructions = """
                CONTEXTUAL ENHANCEMENT - Pre-Wheel Discussion Mode:

                The user wants activities but we need a bit more information first.

                APPROACH:
                - Acknowledge their request for activities
                - Explain briefly why the information helps
                - Ask for the missing information conversationally
                - Be encouraging about getting to activities soon
                - Use profile enrichment tools if appropriate
                """
            else:
                # General discussion
                instructions = """
                CONTEXTUAL ENHANCEMENT - Discussion Mode:

                Engage in supportive conversation with the user.

                APPROACH:
                - Be empathetic and understanding
                - Ask clarifying questions if needed
                - Provide helpful insights and support
                - Guide toward actionable next steps when appropriate
                """

            if hasattr(mentor_agent, 'inject_instructions'):
                mentor_agent.inject_instructions(instructions)

        except Exception as e:
            logger.warning(f"Error coordinating discussion tools: {str(e)}")

    async def _call_user_profile_tool(self) -> Dict[str, Any]:
        """
        ARCHITECTURAL METHOD: Centralized user profile tool invocation.

        This method encapsulates the proper way to call the get_user_profile tool,
        ensuring consistent input format and following the Single Responsibility Principle.

        Returns:
            Dict containing user profile data with consistent structure
        """
        try:
            from apps.main.agents.tools.get_user_profile_tool import get_user_profile

            # Use the correct input format as defined by the tool contract
            result = await get_user_profile({"user_profile_id": self.user_profile_id})

            # Validate the result structure
            if not isinstance(result, dict) or "user_profile" not in result:
                logger.warning(f"Invalid user profile tool response structure: {type(result)}")
                return {"user_profile": {"profile_completion": 0.0}}

            return result

        except Exception as e:
            logger.error(f"Error calling user profile tool for user {self.user_profile_id}: {str(e)}")
            # Return safe default to prevent system failure - 0.0 ensures onboarding is triggered
            return {"user_profile": {"profile_completion": 0.0}}

    async def _check_recent_profile_information(self) -> bool:
        """
        Check if the user has provided profile information in recent conversation.

        This helps handle the case where profile completion workflows are running asynchronously
        and the profile completion percentage hasn't been updated yet.

        Returns:
            bool: True if recent messages contain profile information
        """
        try:
            # Get recent conversation history
            from apps.main.agents.tools.mentor_tools import get_conversation_history

            history_result = await get_conversation_history(
                user_profile_id=self.user_profile_id,
                limit=5  # Check last 5 messages
            )

            if not history_result.get('success'):
                return False

            messages = history_result.get('messages', [])

            # Look for profile-related keywords in recent user messages
            profile_keywords = [
                'student', 'berlin', 'stressed', 'exams', 'goals', 'improve', 'focus',
                'reduce stress', 'work-life balance', 'creative', 'exercise', 'activities',
                'year', 'old', 'feeling', 'need', 'help', 'like', 'want', 'prefer'
            ]

            user_messages = [msg for msg in messages if msg.get('role') == 'user']

            for message in user_messages[-3:]:  # Check last 3 user messages
                content = message.get('content', '').lower()

                # Count profile-related keywords
                keyword_count = sum(1 for keyword in profile_keywords if keyword in content)

                # If message has multiple profile keywords and is substantial, consider it profile info
                if keyword_count >= 3 and len(content.split()) >= 10:
                    logger.debug(f"Found profile information in recent message: {keyword_count} keywords, {len(content.split())} words")
                    return True

            return False

        except Exception as e:
            logger.warning(f"Error checking recent profile information: {str(e)}")
            return False

    async def _handle_wheel_request_with_direct_response(self, user_message: Dict[str, Any], profile_gaps: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle wheel requests with direct response but consistent routing logic.

        CRITICAL FIX: Provides immediate user feedback while ensuring ALL incomplete profiles
        route to onboarding workflow regardless of request type, UNLESS forced_wheel_generation is True.

        Args:
            user_message: The user's message
            profile_gaps: Enhanced profile gap analysis results

        Returns:
            dict: Direct response with consistent workflow routing
        """
        message_text = user_message.get("text", "").lower()

        # Check for forced wheel generation flag in metadata
        metadata = user_message.get("metadata", {})
        forced_wheel_generation = metadata.get("forced_wheel_generation", False)

        # Detect wheel request keywords
        wheel_keywords = [
            "wheel", "activity", "activities", "suggest", "suggestion", "recommendations", "recommend",
            "what should i do", "help me choose", "give me something", "need ideas", "bored",
            "what can i do", "help me decide", "show me options", "give me options",
            "create wheel", "generate wheel", "make me a wheel", "spin wheel"
        ]

        is_wheel_request = any(keyword in message_text for keyword in wheel_keywords)

        if not is_wheel_request and not forced_wheel_generation:
            return None  # Not a wheel request

        # Get profile completion status
        completion_percentage = profile_gaps.get('completion_percentage', 0.0)
        profile_readiness = profile_gaps.get('profile_readiness', 'insufficient')

        # ENHANCED ROUTING LOGIC: Consider both completion percentage AND critical gaps
        critical_gaps = profile_gaps.get('critical', [])
        has_critical_gaps = len(critical_gaps) > 0

        # Route to onboarding if EITHER condition is true:
        # 1. Profile completion < 70% (increased threshold for better quality)
        # 2. Critical gaps exist (regardless of percentage)
        # UNLESS forced_wheel_generation is True
        if (completion_percentage < 0.7 or has_critical_gaps) and not forced_wheel_generation:
            if has_critical_gaps:
                # Use specific question from gap analysis
                next_priority = profile_gaps.get('next_priority_field', {})
                specific_question = next_priority.get('question', 'Could you tell me a bit about yourself?')
                context_hint = next_priority.get('context_hint', '')

                logger.info(f"🎯 WHEEL REQUEST with critical gaps - routing to onboarding (profile: {completion_percentage:.1%}, gaps: {len(critical_gaps)})")

                # Provide specific, targeted response based on gap analysis
                direct_response = f"I'd love to create a personalized activity wheel for you! To make sure I give you the perfect recommendations, I need to know: {specific_question} {context_hint}"
            else:
                logger.info(f"🎯 WHEEL REQUEST from incomplete profile - routing to onboarding (profile: {completion_percentage:.1%})")

                # Provide encouraging direct response for low completion
                direct_response = "I'd love to create a personalized activity wheel for you! To make sure I give you the perfect recommendations, let me ask you a few quick questions first. This will only take a couple of minutes and will help me suggest activities that truly fit your situation."

            # Send conversation state update
            await self._send_conversation_state_update({
                'phase': 'awaiting_profile_info',
                'awaiting_response_type': 'profile_info',
                'context': {
                    'completion_percentage': completion_percentage,
                    'wheel_request_pending': True
                }
            })

            return {
                "direct_response": direct_response,
                "workflow_type": "onboarding",  # CONSISTENT: Always onboarding for incomplete profiles
                "confidence": 1.0,
                "reason": f"Wheel request with incomplete profile - routing to onboarding (profile: {completion_percentage:.1%})",
                "conversation_state_update": {
                    'phase': 'awaiting_profile_info',
                    'awaiting_response_type': 'profile_info'
                }
            }
        else:
            # Profile sufficient OR forced wheel generation - allow wheel generation
            if forced_wheel_generation:
                logger.info(f"🚀 FORCED WHEEL GENERATION - bypassing profile completion (profile: {completion_percentage:.1%}, forced: True)")
                direct_response = "Perfect! I'll create your personalized activity wheel right away. Let's get you some great activities to choose from!"
            else:
                logger.info(f"✅ WHEEL REQUEST from complete profile - allowing wheel generation (profile: {completion_percentage:.1%})")
                direct_response = "Great! I'll create a personalized activity wheel for you right away using your profile information. This should just take a moment..."

            # Send conversation state update
            await self._send_conversation_state_update({
                'phase': 'initial',
                'awaiting_response_type': None,
                'context': {
                    'wheel_generation_initiated': True,
                    'profile_completion': completion_percentage
                }
            })

            reason = f"Forced wheel generation - bypassing profile completion (profile: {completion_percentage:.1%})" if forced_wheel_generation else f"Wheel request with complete profile - routing to wheel generation (profile: {completion_percentage:.1%})"

            return {
                "direct_response": direct_response,
                "workflow_type": "wheel_generation",
                "confidence": 0.98,
                "reason": reason,
                "conversation_state_update": {
                    'phase': 'initial',
                    'awaiting_response_type': None
                },
                "launch_workflow": True  # Ensure workflow is launched
            }

    async def _extract_message_context(self, user_message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract context from the user's message using the extract_context tool.

        Args:
            user_message: The user's message

        Returns:
            dict: Extracted context elements
        """
        try:
            # Use the extract_context tool with the message text
            # Ensure user_profile_id is passed as it's required by the tool signature
            result = await execute_tool(
                tool_code="extract_message_context",
                tool_input={
                    "message": user_message.get("text", ""), # Use 'message' key
                    "user_profile_id": self.user_profile_id # Pass user_profile_id
                    # Removed metadata as the tool doesn't accept it
                },
                user_profile_id=self.user_profile_id, # Pass for event targeting
                session_id=self.user_ws_session_name # Pass for event targeting
            )

            # Get recent user state to enrich context if available
            try:
                user_state = await execute_tool(
                    tool_code="get_user_state",
                    tool_input={"user_profile_id": self.user_profile_id},
                    user_profile_id=self.user_profile_id, # Pass for event targeting
                    session_id=self.user_ws_session_name # Pass for event targeting
                )

                # Merge user state with extracted context, prioritizing explicit context
                merged_context = {**user_state.get("current_state", {}), **result}
                return merged_context
            except:
                # Continue with just the extracted context if user state isn't available
                return result
        except Exception as e:
            logger.warning(f"Error extracting context: {str(e)}")
            # Return minimal context in case of failure, including enhanced context fields (schema v2.0.0)
            return {
                "mood": "",
                "environment": "",
                "time_availability": "",
                "focus": "",
                "extraction_confidence": 0.0,  # Indicate failure with 0 confidence

                # Enhanced context fields (schema v2.0.0)
                "user_state": {
                    "trust_level": 50,
                    "mood": "",
                    "environment": ""
                },
                "device_capabilities": {
                    "screen_size": "medium",
                    "input_method": "mixed",
                    "accessibility": {
                        "vision_impaired": False,
                        "hearing_impaired": False,
                        "motor_impaired": False,
                        "preferred_modality": "visual"
                    }
                },
                "time_context": {
                    "available_time": 0,
                    "time_of_day": "afternoon",
                    "time_zone": "UTC"
                },
                "user_preferences": {
                    "learning_style": "visual",
                    "communication_preferences": {
                        "verbosity": "moderate",
                        "formality": "neutral",
                        "feedback_frequency": "moderate"
                    }
                }
            }
            # Note: The following lines were removed as they caused syntax errors:
            # user_message: The user's message
            # context: Extracted context elements
            # profile_status: Profile completion status

    async def _handle_follow_up_message(
        self,
        user_message: Dict[str, Any],
        awaiting_type: str,
        session_context: Dict[str, Any],
        context: Dict[str, Any],
        profile_status: float
    ) -> Dict[str, Any]:
        """
        Enhanced follow-up message handling with intelligent conversationState management.

        Phase 2 Enhancement: Provides intelligent routing for profile completion scenarios
        with contextual state management and MentorService coordination.

        Args:
            user_message: The user's message
            awaiting_type: Expected response type
            session_context: Session context from conversation state
            context: Extracted context elements
            profile_status: Profile completion status

        Returns:
            dict: Classification result with workflow type, confidence, and state management
        """
        message_text = user_message.get('text', '')

        if awaiting_type == 'profile_info':
            # Enhanced profile completion handling

            # Check if user is providing profile information or requesting wheel generation
            wheel_request_keywords = ["wheel", "activity", "activities", "skip", "later", "enough"]
            is_wheel_request = any(keyword in message_text.lower() for keyword in wheel_request_keywords)

            if is_wheel_request and profile_status >= 0.3:
                # User wants to proceed to wheel generation despite incomplete profile
                await self._send_conversation_state_update({
                    'phase': 'initial',
                    'awaiting_response_type': None,
                    'context': {
                        'profile_completion_interrupted': True,
                        'wheel_request_from_profile_completion': True,
                        'completion_percentage': profile_status
                    }
                })

                return {
                    "workflow_type": "wheel_generation",
                    "confidence": 0.85,
                    "reason": "User requested wheel generation during profile completion - sufficient profile data available",
                    "conversation_state_update": {
                        'phase': 'initial',
                        'awaiting_response_type': None
                    }
                }
            else:
                # Continue with profile completion - user is providing information

                # Check if we should continue profile completion or transition to wheel generation
                if profile_status >= 0.7:
                    # Profile is now sufficient - offer transition to wheel generation
                    await self._send_conversation_state_update({
                        'phase': 'initial',
                        'awaiting_response_type': None,
                        'context': {
                            'profile_completion_achieved': True,
                            'completion_percentage': profile_status,
                            'ready_for_wheel_generation': True
                        }
                    })

                    # Inject instructions to mentor to offer wheel generation
                    if self.mentor_service:
                        try:
                            contextual_instructions = """
                            CONTEXTUAL ENHANCEMENT - Profile Completion Success:
                            The user's profile is now sufficiently complete for quality activity recommendations.

                            APPROACH:
                            - Acknowledge the information they just provided
                            - Celebrate that their profile is now ready
                            - Offer to generate their personalized activity wheel
                            - Be enthusiastic and encouraging about the next step

                            EXAMPLE: "Great! Thanks for sharing that. Your profile is looking fantastic now - I have everything I need to suggest perfect activities for you. Would you like me to generate your personalized activity wheel?"
                            """

                            mentor_agent = await self.mentor_service.get_mentor_agent()
                            if mentor_agent and hasattr(mentor_agent, 'inject_instructions'):
                                mentor_agent.inject_instructions(contextual_instructions)
                                logger.debug("Injected profile completion success instructions to mentor agent")
                        except Exception as e:
                            logger.warning(f"Failed to inject profile completion success instructions: {str(e)}")

                return {
                    "workflow_type": "onboarding",
                    "confidence": 0.95,
                    "reason": "Follow-up to profile information questions - continuing profile completion",
                    "conversation_state_update": {
                        'phase': 'awaiting_profile_info' if profile_status < 0.7 else 'initial',
                        'awaiting_response_type': 'profile_info' if profile_status < 0.7 else None
                    },
                    # CRITICAL FIX: Remove direct_response_only to allow workflow execution
                    # This ensures the profile completion workflow actually runs
                    "launch_workflow": True  # Explicitly indicate workflow should be launched
                }

        elif awaiting_type == 'situation_info':
            # Continue discussion workflow for situation clarification
            return {
                "workflow_type": "discussion",
                "confidence": 0.9,
                "reason": "Follow-up to situation information request"
            }
        elif awaiting_type == 'activity_selection':
            # Route to activity feedback
            return {
                "workflow_type": "post_activity",
                "confidence": 0.9,
                "reason": "Response to activity suggestion"
            }
        elif awaiting_type == 'activity_feedback':
            # Continue post-activity workflow for feedback collection
            return {
                "workflow_type": "post_activity",
                "confidence": 0.9,
                "reason": "Follow-up to activity feedback collection"
            }

        # Fallback to normal classification if awaiting type not recognized
        logger.warning(f"Unknown awaiting response type: {awaiting_type}, falling back to normal classification")
        return await self._classify_message(user_message, context, profile_status)

    async def _send_conversation_state_update(self, state_updates: Dict[str, Any]) -> None:
        """
        Send conversation state updates to frontend via WebSocket.

        Args:
            state_updates: Dictionary of state updates to send
        """
        if self.user_ws_session_name:
            try:
                from channels.layers import get_channel_layer
                channel_layer = get_channel_layer()
                await channel_layer.group_send(
                    self.user_ws_session_name,
                    {
                        'type': 'conversation_state_update',
                        'updates': state_updates
                    }
                )
                logger.debug(f"Sent conversation state update: {state_updates}")
            except Exception as e:
                logger.warning(f"Failed to send conversation state update: {e}")

    async def _send_immediate_user_response(self, message: str) -> None:
        """
        Send immediate response to user via WebSocket for instant feedback.

        This provides immediate user feedback while background processing continues.

        Args:
            message: The message to send to the user immediately
        """
        if self.user_ws_session_name:
            try:
                from channels.layers import get_channel_layer
                channel_layer = get_channel_layer()

                # Send as a chat message from the system (fixed structure)
                await channel_layer.group_send(
                    self.user_ws_session_name,
                    {
                        'type': 'chat_message',
                        'content': message,
                        'is_user': False,
                        'source': 'conversation_dispatcher',
                        'immediate_response': True
                    }
                )
                logger.info(f"Sent immediate user response: {message[:50]}...")
            except Exception as e:
                logger.warning(f"Failed to send immediate user response: {e}")

    async def _send_direct_response(self, response_text: str):
        """Send direct response to user via WebSocket to prevent hanging."""
        try:
            if self.user_ws_session_name:
                from channels.layers import get_channel_layer
                channel_layer = get_channel_layer()
                await channel_layer.group_send(
                    self.user_ws_session_name,
                    {
                        'type': 'chat_message',
                        'content': response_text,
                        'is_user': False,
                        'source': 'conversation_dispatcher',
                        'timestamp': datetime.now().isoformat(),
                        'direct_response': True
                    }
                )
                logger.info(f"Sent direct response to prevent hanging: {response_text[:100]}...")
            else:
                logger.warning("No WebSocket session name available for direct response")
        except Exception as e:
            logger.error(f"Error sending direct response: {str(e)}")

    async def _classify_message(
        self,
        user_message: Dict[str, Any],
        context: Dict[str, Any],
        profile_status: float
    ) -> Dict[str, Any]:
        """
        Classify the user's message to determine the appropriate workflow.

        Args:
            user_message: The user's message
            context: Extracted context elements
            profile_status: Profile completion status

        Returns:
            dict: Classification result with workflow type and confidence
        """
        logger.info(f"🚀 _classify_message CALLED: user_message={user_message}, profile_status={profile_status}")

        message_text = user_message.get("text", "")
        metadata = user_message.get("metadata", {})

        # Check for spin result message (highest priority) - according to DATA_FLOW_AUTHORITATIVE_SPECS.md
        if user_message.get("type") == "spin_result":
            content = user_message.get("content", {})
            return {
                "workflow_type": "post_spin",
                "confidence": 1.0,
                "reason": "Processing spin result",
                "activity_id": content.get("activity_tailored_id"),
                "activity_name": content.get("name")
            }

        # Check for explicit wheel generation requests with energy/time data
        content = user_message.get("content", {})
        if (user_message.get("type") == "chat_message" and
            content.get("metadata", {}).get("requested_workflow") == "wheel_generation"):
            return {
                "workflow_type": "wheel_generation",
                "confidence": 1.0,
                "reason": "Explicit wheel generation request with metadata",
                "energy_level": content.get("energy_level"),
                "time_available": content.get("time_available")
            }

        # Check metadata for explicit workflow requests (high priority)
        if "requested_workflow" in metadata:
            return {
                "workflow_type": metadata["requested_workflow"],
                "confidence": 1.0,
                "reason": "Explicitly requested in metadata"
            }

        # Check for spin result metadata (legacy support)
        if metadata.get("type") == "spin_result":
            return {
                "workflow_type": "post_spin",
                "confidence": 1.0,
                "reason": "Processing spin result (legacy format)",
                "activity_id": metadata.get("activity_id"),
                "activity_name": metadata.get("activity_name")
            }

        # Enhanced profile completion check - consider both percentage AND critical gaps
        logger.info(f"🔍 PROFILE CHECK: profile_status={profile_status:.1%}, message='{message_text[:50]}...'")

        # Get profile gaps for enhanced decision making
        try:
            profile_gaps = await self._analyze_profile_gaps()
            critical_gaps = profile_gaps.get('critical', [])
            has_critical_gaps = len(critical_gaps) > 0
        except Exception as e:
            logger.warning(f"Error getting profile gaps for classification: {e}")
            critical_gaps = []
            has_critical_gaps = False

        # Enhanced routing logic: Route to onboarding if EITHER condition is true:
        # 1. Profile completion < 70% (increased threshold for better quality)
        # 2. Critical gaps exist (regardless of percentage)
        if profile_status < 0.7 or has_critical_gaps:
            if has_critical_gaps:
                logger.info(f"🚨 CRITICAL GAPS DETECTED: {len(critical_gaps)} gaps, profile: {profile_status:.1%}")
            else:
                logger.info(f"🚨 INCOMPLETE PROFILE DETECTED: {profile_status:.1%} < 70%")

            # Check if this is a wheel request to provide appropriate messaging
            is_wheel_request = self._is_explicit_wheel_request(message_text)

            if is_wheel_request and has_critical_gaps:
                # Use specific question from gap analysis for wheel requests
                next_priority = profile_gaps.get('next_priority_field', {})
                specific_question = next_priority.get('question', 'Could you tell me a bit about yourself?')
                context_hint = next_priority.get('context_hint', '')

                logger.info(f"🎯 WHEEL REQUEST with critical gaps - routing to onboarding (profile: {profile_status:.1%}, gaps: {len(critical_gaps)})")

                # Provide specific, targeted response based on gap analysis
                direct_response = f"I'd love to create a personalized activity wheel for you! To make sure I give you the perfect recommendations, I need to know: {specific_question} {context_hint}"
            elif is_wheel_request:
                logger.info(f"🎯 WHEEL REQUEST from incomplete profile - routing to onboarding (profile: {profile_status:.1%})")

                # Provide encouraging response for wheel requests
                direct_response = "I'd love to create a personalized activity wheel for you! To make sure I suggest the best activities that truly fit your situation, could you tell me a bit about yourself first? For example, what kind of activities do you usually enjoy, and how are you feeling today?"
            else:
                logger.info(f"💬 GENERAL MESSAGE from incomplete profile - routing to onboarding (profile: {profile_status:.1%})")

                # Provide general guidance for non-wheel requests
                direct_response = "Hi! I'm here to help you discover great activities. To get started, could you tell me how you're feeling today or what kind of activities you're interested in?"

            # Send direct response immediately to prevent hanging
            await self._send_direct_response(direct_response)

            # Update conversation state
            await self._send_conversation_state_update({
                'phase': 'awaiting_profile_info',
                'awaiting_response_type': 'profile_info',
                'context': {
                    'profile_completion': profile_status,
                    'is_wheel_request': is_wheel_request,
                    'needs_profile_completion': True,
                    'critical_gaps_count': len(critical_gaps)
                }
            })

            # ALWAYS return onboarding workflow for incomplete profiles or critical gaps
            reason = f"Critical gaps detected - profile completion required ({len(critical_gaps)} gaps, {profile_status:.1%})" if has_critical_gaps else f"Incomplete profile detected - profile completion required ({profile_status:.1%})"

            return {
                "workflow_type": "onboarding",
                "confidence": 0.9,
                "reason": reason,
                "direct_response": direct_response,
                "conversation_state_update": {
                    'phase': 'awaiting_profile_info',
                    'awaiting_response_type': 'profile_info'
                }
            }
        else:
            logger.info(f"✅ PROFILE COMPLETE: {profile_status:.1%} >= 70% with no critical gaps, continuing to normal classification")

        # For profiles with sufficient completion, check for explicit activity requests
        message_lower = message_text.lower()
        wheel_keywords = ["activity", "activities", "suggest", "recommendation", "wheel", "what should i do", "overwhelmed", "stress", "help me"]

        if any(keyword in message_lower for keyword in wheel_keywords):
            # Allow wheel generation for explicit activity requests from complete profiles
            logger.info(f"Activity request detected from complete profile - allowing wheel generation (profile: {profile_status:.1%})")
            # Continue to normal classification logic below (don't return here)
        else:
            # For other messages from complete profiles, continue with normal classification
            pass

        # Fetch recent conversation history before attempting LLM classification
        history = []
        try:
            history_result = await execute_tool(
                tool_code="get_conversation_history",
                tool_input={"user_profile_id": self.user_profile_id, "limit": 5}, # Fetch last 5 messages
                user_profile_id=self.user_profile_id, # Pass for event targeting
                session_id=self.user_ws_session_name # Pass for event targeting
            )
            if isinstance(history_result, list):
                history = history_result
            elif isinstance(history_result, dict) and 'history' in history_result and isinstance(history_result['history'], list):
                 history = history_result['history']
            else:
                 logger.warning(f"Unexpected format for conversation history: {type(history_result)}")
        except Exception as e:
            logger.warning(f"Failed to fetch conversation history: {str(e)}")

        # Use LLM for classification if available, now including history
        if self.llm_client:
            try:
                llm_classification = await self._classify_with_llm(message_text, context, history)
                if llm_classification:
                    # Apply business rules to LLM classification too
                    refined_classification = await self._apply_classification_rules(
                        message_text,
                        context,
                        llm_classification.get("workflow_type", "discussion"),
                        llm_classification.get("confidence", 0.7),
                        user_message  # Pass user_message (which is enhanced_message) for trust level access
                    )
                    return refined_classification
            except Exception as e:
                logger.warning(f"LLM classification failed: {str(e)}. Falling back to rules.")

        # Fallback to rule-based classification (or if LLM fails)
        try:
            # Use the classify_message_intent tool (corrected name)
            result = await execute_tool(
                tool_code="classify_message_intent", # Corrected tool code
                tool_input={
                    "message": message_text, # Use 'message' key
                    "context": context, # Use 'context' key
                    "profile_completion": profile_status # Use 'profile_completion' key
                },
                user_profile_id=self.user_profile_id, # Pass for event targeting
                session_id=self.user_ws_session_name # Pass for event targeting
            )

            # The tool returns a dict with a 'classification' key
            classification_result = result.get("classification", {})

            # Apply business rules to refine the classification
            return await self._apply_classification_rules(
                message_text,
                context,
                classification_result.get("workflow_type", "wheel_generation"),
                classification_result.get("confidence", 0.7),
                user_message  # Pass user_message (which is enhanced_message) for trust level access
            )

        except Exception as e:
            logger.warning(f"Error in tool-based workflow classification: {str(e)}")
            # Apply rules to the text directly as fallback
            # Apply business rules to refine the classification
            return await self._apply_classification_rules(
                message_text,
                context,
                "wheel_generation",  # Default to wheel generation
                0,  # Lower confidence for fallback
                user_message  # Pass user_message (which is enhanced_message) for trust level access
            )

    async def _classify_with_llm(self, message: str, context: Dict[str, Any], history: list) -> Optional[Dict[str, Any]]:
        """
        Use LLM to classify the user message into a workflow type, considering history.

        Args:
            message: The user's current message text.
            context: Extracted context from the current message.
            history: List of recent conversation messages (e.g., [{"role": "user", "content": "..."}, ...]).

        Returns:
            Optional[Dict[str, Any]]: Classification results or None if failed/invalid.
        """
        # Create a clear, structured prompt for the LLM
        system_prompt = """You are a workflow classifier for a personal growth application called 'Game of Life'.
Your task is to classify user messages into specific workflow types based on user intent and context. If you're not sure what the user wants, your best bet is to orient to "discussion" workflow.

WORKFLOW TYPES:
1. discussion - User is confused or needs to talk
2. wheel_generation - User wants activity suggestions or recommendations
3. post_activity - User is giving feedback about a completed activity
4. pre_spin_feedback - User is sharing  feelings about a wheel that just got generated
5. onboarding - User is new and needs guidance about the system
6. post_spin - User has selected an activity and needs details (usually handled by metadata)

Classify the message based on:
- Explicit requests (e.g., "I want an activity" -> wheel_generation)
- Implied intent (e.g., "I just finished meditating and..." -> post_activity)
- Context clues (e.g., "I'm feeling creative today" -> wheel_generation)

Respond with a JSON object containing:
{
  "workflow_type": "one of the workflow types listed above",
  "confidence": float between 0.0-1.0,
  "reason": "brief explanation of classification"
}"""

        # Format history for the prompt
        formatted_history = "\n".join([f"{msg.get('role', 'unknown').capitalize()}: {msg.get('content', '')}" for msg in history])

        # User message including context and history
        user_prompt = f"""Conversation History (most recent first):
{formatted_history if formatted_history else "No history available."}

---
Current Message: {message}

Extracted context:
Mood: {context.get('mood', 'Not specified')}
Environment: {context.get('environment', 'Not specified')}
Time availability: {context.get('time_availability', 'Not specified')}
Focus: {context.get('focus', 'Not specified')}

Classify this message into the most appropriate workflow type."""

        # Call the LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        try:
            llm_response = await self.llm_client.chat_completion(
                messages=messages,
                temperature=0.2  # Low temperature for more deterministic results
            )

            # Parse the response to extract the JSON
            if llm_response.is_text:
                content = llm_response.content
                # Try to extract JSON from the response
                try:
                    # First try direct JSON parsing
                    classification = json.loads(content)
                except json.JSONDecodeError:
                    # Try to extract from markdown code blocks
                    import re
                    json_match = re.search(r"```(?:json)?\s*([\s\S]*?)\s*```", content)
                    if json_match:
                        try:
                            classification = json.loads(json_match.group(1))
                        except json.JSONDecodeError:
                            logger.warning("Failed to parse JSON from code block")
                            return None
                    else:
                        # Try to find JSON-like structure with regex
                        json_like = re.search(r'(\{.*"workflow_type".*\})', content, re.DOTALL)
                        if json_like:
                            try:
                                classification = json.loads(json_like.group(1))
                            except json.JSONDecodeError:
                                logger.warning("Failed to parse JSON-like structure")
                                return None
                        else:
                            logger.warning("No JSON found in LLM response")
                            return None

                # Validate the classification
                if isinstance(classification, dict) and "workflow_type" in classification:
                    # Ensure workflow_type is valid
                    # Added "discussion" based on LLM prompt and workflow_analysis.md
                    # Removed "progress_review" as it's not implemented
                    valid_types = [
                        "discussion", "wheel_generation", "post_activity",
                        "pre_spin_feedback", "onboarding",
                        "post_spin"
                    ]

                    if classification["workflow_type"] in valid_types:
                        # Ensure confidence is valid
                        if "confidence" not in classification:
                            classification["confidence"] = 0.8
                        else:
                            # --- Proposed Robust Handling ---
                            try:
                                classification["confidence"] = min(1.0, max(0.0, float(classification["confidence"])))
                            except (ValueError, TypeError):
                                logger.warning(f"Invalid confidence value received: {classification['confidence']}. Defaulting to 0.8.")
                                classification["confidence"] = 0.8
                            # --- End Proposed Handling ---

                        # Ensure reason is present
                        if "reason" not in classification:
                            classification["reason"] = "Classified by LLM"

                        return classification

            logger.warning(f"Invalid LLM classification response: {content}")
            return None

        except Exception as e:
            logger.error(f"Error in LLM classification: {str(e)}", exc_info=True)
            return None

    async def _apply_classification_rules(
        self,
        message: str,
        context: Dict[str, Any],
        workflow_type: str,
        confidence: float,
        enhanced_message: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Apply business rules to refine the classification.

        Args:
            message: The user's message text
            context: Extracted context
            workflow_type: Initial workflow type
            confidence: Initial confidence

        Returns:
            dict: Refined classification
        """
        # Convert message to lowercase for case-insensitive matching
        message_lower = message.lower()

        # Rule 1: Strong keywords override with higher confidence
        activity_keywords = ["wheel", "activity", "activities", "suggest", "suggestion", "suggestions", "recommend", "what should i do"]
        feedback_keywords = ["feedback", "completed", "finished", "done with", "i did"]
        pre_spin_keywords = ["before spinning", "about to spin", "before i spin"]
        # Removed progress_keywords as progress_review workflow is not implemented

        # Rule 1: Strong keywords override (Order matters!)
        # Check feedback first, as it's more specific than general activity requests
        if any(kw in message_lower for kw in feedback_keywords):
            return {
                'workflow_type': 'post_activity',
                'confidence': max(0.8, confidence),
                'reason': 'Feedback-related keywords detected'
            }
        # ADHD/First-time user support: Check for activity requests and apply trust-based routing
        elif any(kw in message_lower for kw in activity_keywords):
            # Get trust level from database (preferred) or enhanced_message/context fallbacks
            trust_level = 50  # Default fallback

            # CRITICAL FIX: Query database for actual trust level using async database access
            try:
                from apps.user.models import TrustLevel

                @database_sync_to_async
                def get_trust_level_sync():
                    return TrustLevel.objects.filter(user_profile_id=self.user_profile_id).first()

                trust_record = await get_trust_level_sync()

                if trust_record:
                    trust_level = trust_record.value  # TrustLevel.value is already 0-100
                    logger.info(f"Retrieved trust level from database: {trust_level} for user {self.user_profile_id}")
                else:
                    logger.debug(f"No trust level record found for user {self.user_profile_id}, using fallback")
            except Exception as e:
                logger.warning(f"Error retrieving trust level from database: {e}")
                # Fall back to enhanced_message/context extraction
                if enhanced_message and 'mentor_context' in enhanced_message:
                    trust_level = enhanced_message['mentor_context'].get('trust_level', 0.5) * 100  # Convert 0.0-1.0 to 0-100
                elif 'mentor_context' in context:
                    trust_level = context['mentor_context'].get('trust_level', 0.5) * 100  # Convert 0.0-1.0 to 0-100
                else:
                    trust_level = context.get('user_state', {}).get('trust_level', 50)

            is_wellness_request = any(wellness_kw in message_lower for wellness_kw in ['wellness', 'productivity', 'improve', 'better', 'help'])
            is_direct_activity_request = any(direct_kw in message_lower for direct_kw in ['activities', 'suggestions', 'ready', 'need some', 'ready for', 'want activities', 'give me activities'])

            # Enhanced ADHD-friendly routing logic
            # For users with low trust (< 80), require mood assessment before wheel generation
            if trust_level < 80:
                if is_wellness_request:
                    return {
                        'workflow_type': 'discussion',
                        'confidence': 0.85,
                        'reason': 'Low trust user wellness request - needs mood assessment first'
                    }
                elif is_direct_activity_request:
                    return {
                        'workflow_type': 'discussion',
                        'confidence': 0.80,
                        'reason': 'Low trust user activity request - needs mood assessment before activities'
                    }

            # High trust users (>= 80) can proceed directly to wheel generation
            return {
                'workflow_type': 'wheel_generation',
                'confidence': max(0.8, confidence),
                'reason': f'Activity request from high trust user (trust: {trust_level:.0f})'
            }
        # Then check for pre-spin context gathering
        elif any(kw in message_lower for kw in pre_spin_keywords):
            return {
                'workflow_type': 'pre_spin_feedback',
                'confidence': max(0.8, confidence),
                'reason': 'Pre-spin related keywords detected'
            }

        # Removed progress_review check as workflow is not implemented

        # Rule 2: If confidence is low (e.g., < 0.65), default to discussion first
        # This handles ambiguous cases before potentially misinterpreting brief mood messages.
        if confidence < 0.65:
             return {
                'workflow_type': 'discussion',
                'confidence': 0.6, # Keep confidence relatively low for default
                'reason': 'Low confidence classification, defaulting to discussion'
            }

        # Rule 3: If the message contains significant mood information but little else (and confidence wasn't low)
        # This might indicate pre-spin feedback if not explicitly asking for activities.
        if context.get('mood') and len(message.split()) < 10:
            return {
                'workflow_type': 'pre_spin_feedback', # Changed from discussion, might need review
                'confidence': 0.7,
                'reason': 'Brief message with mood information'
            }

        # Default: If no specific rules matched and confidence is reasonable, return the original classification.
        return {
            'workflow_type': workflow_type,
            'confidence': confidence,
            'reason': 'Based on initial classification (LLM or tool)'
        }

    def _check_if_action_required(self, workflow_type: str, context: Dict[str, Any], user_message: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        Check if additional information is needed before proceeding.

        Args:
            workflow_type: The determined workflow type
            context: Extracted context
            user_message: The original user message (to check metadata)

        Returns:
            dict or None: Action required information or None
        """
        # FIXED: Disable restrictive wheel generation blocking to allow basic user stories to complete
        # The system should proceed with wheel generation when the user clearly wants activities
        if workflow_type == 'wheel_generation':
            # Always proceed with wheel generation - the workflow can handle missing context gracefully
            # This fixes the core user story where "I feel like doing exercise" should generate a wheel
            return None

        # For post activity feedback, ensure we know which activity
        if workflow_type == 'post_activity':
            # Check if activity_id is in context or in user message metadata
            has_activity_id = (
                'activity_id' in context or
                (user_message and user_message.get('metadata', {}).get('activity_id'))
            )

            if not has_activity_id:
                return {
                    'type': 'gather_information',
                    'missing_field': 'activity_id',
                    'prompt': "I'd love to hear your feedback. Which activity are you providing feedback on?"
                }

        # No additional action required
        return None

    def _get_fallback_context(self) -> Dict[str, Any]:
        """
        Get fallback context when context extraction fails.

        Returns:
            dict: Minimal context with default values
        """
        return {
            "mood": "",
            "environment": "unknown",
            "time_availability": "",
            "focus": "",
            "extraction_confidence": 0.0,
            "user_state": {
                "trust_level": 50,
                "mood": "",
                "environment": "unknown"
            },
            "device_capabilities": {
                "screen_size": "medium",
                "input_method": "mixed",
                "accessibility": {
                    "vision_impaired": False,
                    "hearing_impaired": False,
                    "motor_impaired": False,
                    "preferred_modality": "visual"
                }
            },
            "time_context": {
                "available_time": 0,
                "time_of_day": "afternoon",
                "time_zone": "UTC"
            },
            "user_preferences": {
                "learning_style": "visual",
                "communication_preferences": {
                    "verbosity": "moderate",
                    "formality": "neutral",
                    "feedback_frequency": "moderate"
                }
            }
        }

    def _get_fallback_context_packet(self, context_extraction: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get fallback context packet when context packet building fails.

        Args:
            context_extraction: The context extraction data (may be partial)

        Returns:
            dict: Minimal context packet with essential fields
        """
        return {
            "user_id": self.user_profile_id,
            "session_timestamp": datetime.now().isoformat(),
            "reported_mood": context_extraction.get("mood", ""),
            "reported_environment": context_extraction.get("environment", "unknown"),
            "reported_time_availability": context_extraction.get("time_availability", ""),
            "reported_focus": context_extraction.get("focus", ""),
            "extraction_confidence": 0.0,
            "user_ws_session_name": self.user_ws_session_name,
            "fallback_mode": True,
            "error_context": "Context packet building failed, using minimal fallback",
            "system_metadata": {
                "workflow_origin": "frontend"  # Frontend-initiated workflows are always "real"
            }
        }

    def _build_context_packet(
        self,
        context_extraction: Dict[str, Any],
        enhanced_message: Dict[str, Any] = None,
        workflow_classification: Dict[str, Any] = None,
        mentor_context_available: bool = False
    ) -> Dict[str, Any]:
        """
        Build a standardized context packet for downstream processing.

        Args:
            context_extraction: Extracted context elements

        Returns:
            dict: Standardized context packet with enhanced context fields (schema v2.0.0)
        """
        # Build the base context packet with standard fields
        context_packet = {
            'user_id': self.user_profile_id,
            'user_profile_id': self.user_profile_id,  # Include for backwards compatibility
            'session_timestamp': datetime.now().isoformat(),
            'reported_mood': context_extraction.get('mood', ''),
            'reported_environment': context_extraction.get('environment', ''),
            'reported_time_availability': context_extraction.get('time_availability', ''),
            'reported_focus': context_extraction.get('focus', ''),
            'reported_satisfaction': context_extraction.get('satisfaction', ''),
            'extraction_confidence': context_extraction.get('extraction_confidence', 0.5),
            'entities': context_extraction.get('extracted_entities', []),
            'user_ws_session_name': self.user_ws_session_name  # Add WebSocket session for result routing
        }

        # Add enhanced context fields (schema v2.0.0)
        if 'user_state' in context_extraction:
            context_packet['user_state'] = context_extraction.get('user_state')

        if 'device_capabilities' in context_extraction:
            context_packet['device_capabilities'] = context_extraction.get('device_capabilities')

        if 'time_context' in context_extraction:
            context_packet['time_context'] = context_extraction.get('time_context')

        if 'user_preferences' in context_extraction:
            context_packet['user_preferences'] = context_extraction.get('user_preferences')

        # Add user input context (energy level and time available from frontend)
        if enhanced_message and enhanced_message.get('content'):
            content = enhanced_message['content']
            if 'energy_level' in content or 'time_available' in content:
                context_packet['user_input_context'] = {
                    'energy_level': content.get('energy_level'),
                    'time_available': content.get('time_available'),
                    'forced_wheel_item_count': 5,  # Force 5 wheel items for high-level debugging
                    'direct_input': True
                }
                # Also add to reported fields for backward compatibility
                if content.get('energy_level') is not None:
                    context_packet['reported_energy_level'] = content.get('energy_level')
                if content.get('time_available') is not None:
                    context_packet['reported_time_availability'] = f"{content.get('time_available')} minutes"

        # Add MentorService context if available (enhanced architecture integration)
        if mentor_context_available and enhanced_message:
            mentor_context = enhanced_message.get('mentor_context', {})
            context_packet['mentor_context'] = {
                'trust_level': mentor_context.get('trust_level', 0.5),
                'communication_preferences': mentor_context.get('communication_preferences', {}),
                'conversation_context': mentor_context.get('conversation_context', {}),
                'mentor_assessment': mentor_context.get('mentor_assessment', {}),
                'service_id': enhanced_message.get('mentor_service_id')
            }

        # Add workflow classification metadata (enhanced monitoring)
        if workflow_classification:
            context_packet['workflow_metadata'] = {
                'intended_workflow': workflow_classification.get('workflow_type'),
                'classification_confidence': workflow_classification.get('confidence', 0.0),
                'classification_reason': workflow_classification.get('reason', 'unknown'),
                'llm_classification_used': self.llm_client is not None
            }

        # Add original message content if available (for spin_result and other workflows)
        if enhanced_message and 'original_content' in enhanced_message:
            context_packet['original_content'] = enhanced_message['original_content']

        # Add system metadata for enhanced monitoring and debugging
        context_packet['system_metadata'] = {
            'dispatcher_version': '2.0.0',  # Enhanced version with MentorService integration
            'mentor_service_available': mentor_context_available,
            'llm_client_available': self.llm_client is not None,
            'llm_initialization_error': self._llm_initialization_error,
            'processing_timestamp': datetime.now().isoformat(),
            'enhanced_architecture': True,  # Flag for downstream components
            'workflow_origin': 'frontend'  # Frontend-initiated workflows are always "real"
        }

        return context_packet

    def _estimate_completion_time(self, workflow_type: str) -> int:
        """
        Estimate workflow completion time in seconds.

        Args:
            workflow_type: The workflow type

        Returns:
            int: Estimated completion time in seconds
        """
        # Provide realistic estimates to set user expectations
        estimates = {
            'wheel_generation': 15,  # 15 seconds
            'post_activity': 10,     # 10 seconds
            'pre_spin_feedback': 5,   # 5 seconds
            'user_onboarding': 20,    # 20 seconds
            # 'progress_review': 12, # Removed - not implemented
            'post_spin': 8,           # 8 seconds
            'discussion': 10          # Added estimate for discussion
        }
        return estimates.get(workflow_type, 10)  # Default 10 seconds

    def _launch_workflow(self, workflow_type: str, context_packet: Dict[str, Any], workflow_id: str) -> None:
        """
        Launch the workflow asynchronously with proper execution mode configuration.

        Args:
            workflow_type: Type of workflow to start
            context_packet: Context information for the workflow
            workflow_id: Unique identifier for the workflow
        """
        # Import here to avoid circular imports
        from apps.main.tasks.agent_tasks import execute_graph_workflow

        # FIXED: Configure execution mode for user-initiated workflows
        # Use real database access to enable proper agent configuration loading
        # This fixes the issue where orchestrator agent definition couldn't be found
        workflow_input = {
            "user_profile_id": self.user_profile_id,
            "context_packet": context_packet,
            "workflow_id": workflow_id,
            # Enable real database access for user-initiated workflows
            "use_real_llm": False,  # Keep LLM mocked for cost control
            "use_real_tools": False,  # Keep tools mocked for safety
            "use_real_db": True,  # Enable real database access for agent definitions
            "mock_tools": None
        }

        # Prepare initial input for the workflow (legacy interface support)
        initial_input = {
            "task_type": workflow_type,
            "context_packet": context_packet,
            "workflow_type": workflow_type,  # Explicitly include for result handling
            "user_ws_session_name": self.user_ws_session_name,  # Include for result routing
            "llm_config_id": self.llm_config_id,  # Include LLM config for debug mode
            "workflow_input": workflow_input  # Add benchmarking interface parameters
        }

        # Launch the appropriate Celery task based on workflow type
        try:
            if workflow_type == 'wheel_generation':
                # Use the specialized wheel generation task with progress tracking
                from apps.main.tasks.wheel_generation_tasks import execute_wheel_generation_workflow
                execute_wheel_generation_workflow.delay(
                    user_profile_id=self.user_profile_id,
                    context_packet=context_packet,
                    workflow_id=workflow_id
                )
                logger.info(f"Launched wheel generation task with progress tracking: {workflow_type} ({workflow_id})")
            else:
                # Use the generic graph workflow task for other workflow types
                execute_graph_workflow.delay(
                    workflow_id=workflow_id,
                    user_profile_id=self.user_profile_id,
                    initial_input=initial_input,
                    workflow_type=workflow_type # Pass workflow_type explicitly
                )
                logger.info(f"Launched generic workflow task: {workflow_type} ({workflow_id}) with real DB access")
        except Exception as e:
            logger.error(f"Error launching workflow task: {str(e)}", exc_info=True)
            # This doesn't raise an exception to allow the response to be returned

    @database_sync_to_async
    def _record_workflow_initiation_sync(self, workflow_type: str, workflow_id: str, context: Dict[str, Any]):
        """Synchronous helper to record workflow initiation."""
        from apps.main.models import HistoryEvent
        from django.contrib.contenttypes.models import ContentType
        from apps.user.models import UserProfile

        try:
            # Get user profile content type
            user_profile_content_type = ContentType.objects.get_for_model(UserProfile)

            # Create history event
            HistoryEvent.objects.create(
                event_type='workflow_initiated',
                content_type=user_profile_content_type,
                object_id=self.user_profile_id,
                user_profile_id=self.user_profile_id,
                details={
                    'workflow_type': workflow_type,
                    'workflow_id': workflow_id,
                    'initial_context': context
                }
            )
            logger.debug(f"Recorded workflow initiation event: {workflow_type} ({workflow_id})")
        except Exception as e:
            # Log the error but don't let it crash the main flow
            logger.error(f"Error recording workflow initiation in sync helper: {str(e)}", exc_info=True)


    async def _record_workflow_initiation(self, workflow_type: str, workflow_id: str,
                                        context: Dict[str, Any]) -> None:
        """
        Record workflow initiation in history (async wrapper).

        Args:
            workflow_type: Type of workflow initiated
            workflow_id: Unique identifier for the workflow
            context: Context information
        """
        try:
            # Call the synchronous helper using database_sync_to_async
            await self._record_workflow_initiation_sync(workflow_type, workflow_id, context)
        except Exception as e:
            # Log the warning if the async call itself fails (less likely)
            logger.warning(f"Failed to record workflow initiation (async wrapper): {str(e)}")
            # Non-critical error, don't re-raise


# Note: This function seems misplaced here and might be better in tasks or utils.
# It also has a potential issue mixing sync/async logic for testing.
# Keeping it as is for now, but flagging for potential refactor.
def execute_graph_workflow(workflow_id, user_profile_id, initial_input, workflow_type=None): # Added workflow_type default
    """
    Execute a complete LangGraph workflow.

    This function is a convenience wrapper for tests and direct calls
    that delegates to the Celery task.

    Args:
        workflow_id: ID for the workflow run
        user_profile_id: The user profile ID
        initial_input: Initial input for the workflow

    Returns:
        dict: Workflow result
    """
    import os

    # For testing environments, run synchronously
    if 'TESTING' in os.environ:
        # Import asyncio to run async function directly
        import asyncio
        from apps.main.graphs.wheel_generation_graph import run_wheel_generation_workflow

        # If this is a wheel generation workflow, run it directly
        if initial_input.get('task_type') == 'wheel_generation':
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(
                run_wheel_generation_workflow(
                    user_profile_id=user_profile_id,
                    context_packet=initial_input,
                    workflow_id=workflow_id
                )
            )

        # Generic fallback
        return {'workflow_id': workflow_id, 'result': {'output_data': {}}}

    # For production, use appropriate Celery task based on workflow type
    if workflow_type == 'wheel_generation':
        # Use the specialized wheel generation task with progress tracking
        from apps.main.tasks.wheel_generation_tasks import execute_wheel_generation_workflow
        result = execute_wheel_generation_workflow.delay(user_profile_id, initial_input, workflow_id)
    else:
        # Use the generic graph workflow task for other workflow types
        from apps.main.tasks.agent_tasks import execute_graph_workflow
        result = execute_graph_workflow.delay(workflow_type, user_profile_id, initial_input, workflow_id)

    return result.get()
