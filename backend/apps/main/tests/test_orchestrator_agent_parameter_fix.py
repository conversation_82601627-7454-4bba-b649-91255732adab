"""
Test suite for OrchestratorAgent parameter handling fix.

This test verifies that the '_actual_execution_mode' parameter error is fixed
and that workflow benchmarks can run without parameter-related errors.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from asgiref.sync import sync_to_async

from apps.main.services.async_workflow_manager import WorkflowBenchmarker
from apps.main.models import BenchmarkScenario
from apps.main.graphs.wheel_generation_graph import _configure_agent_for_execution_mode, WheelGenerationState


class TestOrchestratorAgentParameterFix:
    """Test suite for the OrchestratorAgent parameter handling fix."""

    @pytest.mark.asyncio
    async def test_configure_agent_for_execution_mode_no_invalid_params(self):
        """Test that _configure_agent_for_execution_mode doesn't add invalid parameters."""
        # Create a mock state
        state = WheelGenerationState(
            user_profile_id="test_user",
            use_real_llm=False,
            use_real_tools=False,
            use_real_db=False
        )
        
        # Configure agent parameters
        agent_kwargs = await _configure_agent_for_execution_mode(state, "Orchestrator", "test_user")
        
        # Verify that _actual_execution_mode is NOT in the agent kwargs
        assert '_actual_execution_mode' not in agent_kwargs, \
            "Agent kwargs should not contain '_actual_execution_mode' parameter"
        
        # Verify that valid parameters are present
        assert 'user_profile_id' in agent_kwargs
        assert agent_kwargs['user_profile_id'] == "test_user"

    @pytest.mark.asyncio
    async def test_orchestrator_agent_creation_with_configured_params(self):
        """Test that OrchestratorAgent can be created with configured parameters."""
        from apps.main.agents.orchestrator_agent import OrchestratorAgent
        from apps.main.testing.mock_database_service import MockDatabaseService
        from apps.main.llm.service import MockLLMClient

        # Create a mock state
        state = WheelGenerationState(
            user_profile_id="test_user",
            use_real_llm=False,
            use_real_tools=False,
            use_real_db=False
        )

        # Configure agent parameters
        agent_kwargs = await _configure_agent_for_execution_mode(state, "Orchestrator", "test_user")

        # Add mock services to avoid LLM configuration issues
        agent_kwargs['db_service'] = MockDatabaseService()
        agent_kwargs['llm_client'] = MockLLMClient()

        # This should not raise any "unexpected keyword argument" errors
        try:
            agent = OrchestratorAgent(**agent_kwargs)
            assert agent is not None
            assert agent.user_profile_id == "test_user"
        except TypeError as e:
            if "_actual_execution_mode" in str(e):
                pytest.fail(f"OrchestratorAgent still receiving invalid '_actual_execution_mode' parameter: {e}")
            else:
                # Re-raise other TypeError exceptions
                raise

    @pytest.mark.asyncio
    @pytest.mark.django_db
    async def test_workflow_benchmark_parameter_handling(self):
        """Test that workflow benchmarks can run without parameter errors."""
        # Get a wheel generation scenario
        get_scenario = sync_to_async(
            lambda: BenchmarkScenario.objects.filter(
                metadata__workflow_type='wheel_generation'
            ).first(),
            thread_sensitive=True
        )
        scenario = await get_scenario()
        
        if not scenario:
            pytest.skip("No wheel_generation scenario found for testing")
        
        benchmarker = WorkflowBenchmarker()
        
        # This should not raise "_actual_execution_mode" parameter errors
        try:
            result = await benchmarker.run_benchmark(
                scenario=scenario,
                benchmark_params={
                    'runs': 1,
                    'warmup_runs': 0,
                    'semantic_evaluation': False,
                    'use_real_llm': False,
                    'use_real_tools': False,
                    'use_real_db': False
                }
            )
            
            # Verify that we got a result (even if it has other errors)
            assert result is not None
            assert hasattr(result, 'id')
            
        except TypeError as e:
            if "_actual_execution_mode" in str(e):
                pytest.fail(f"Workflow benchmark still has '_actual_execution_mode' parameter error: {e}")
            else:
                # Re-raise other TypeError exceptions
                raise
        except NotImplementedError:
            # This is expected for mock workflow implementation
            pass
        except Exception as e:
            # Other exceptions are acceptable as long as they're not the parameter error
            if "_actual_execution_mode" in str(e):
                pytest.fail(f"Workflow benchmark still has '_actual_execution_mode' parameter error: {e}")

    def test_state_execution_mode_tracking(self):
        """Test that execution mode tracking is handled through state, not agent parameters."""
        state = WheelGenerationState(
            user_profile_id="test_user",
            use_real_llm=False,
            use_real_tools=False,
            use_real_db=False
        )
        
        # Simulate the execution mode tracking that should happen in the state
        state._actual_execution_modes = {
            'Orchestrator': {
                'real_llm': False,
                'real_db': False,
                'real_tools': False
            }
        }
        
        # Verify that execution mode tracking is properly stored in state
        assert hasattr(state, '_actual_execution_modes')
        assert 'Orchestrator' in state._actual_execution_modes
        assert state._actual_execution_modes['Orchestrator']['real_llm'] is False
        assert state._actual_execution_modes['Orchestrator']['real_db'] is False
        assert state._actual_execution_modes['Orchestrator']['real_tools'] is False
