"""
LLM Interaction Interceptor

This module provides functionality to intercept and log LLM interactions during
workflow execution for Phase 1 Data Model Enhancement. It captures full prompts,
responses, token usage, and performance metrics.
"""

import time
import logging
from typing import Dict, Any, List, Optional, Callable, Awaitable
from functools import wraps
from apps.main.llm.response import LLMResponse
from apps.main.services.agent_communication_tracker import AgentCommunicationTracker

logger = logging.getLogger(__name__)


class LLMInteractionInterceptor:
    """
    Intercepts LLM interactions to capture detailed debugging information.
    
    This class provides decorators and context managers to automatically
    capture LLM interactions during agent execution.
    """
    
    def __init__(self, tracker: Optional[AgentCommunicationTracker] = None):
        """
        Initialize the interceptor.
        
        Args:
            tracker: AgentCommunicationTracker instance to record interactions
        """
        self.tracker = tracker
        self.enabled = tracker is not None and tracker.enabled
        
    def intercept_llm_call(self, agent_name: str, model_name: str = "unknown"):
        """
        Decorator to intercept LLM calls and capture interaction data.
        
        Args:
            agent_name: Name of the agent making the LLM call
            model_name: Name of the LLM model being used
            
        Returns:
            Decorator function
        """
        def decorator(func: Callable[..., Awaitable[LLMResponse]]):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                if not self.enabled:
                    return await func(*args, **kwargs)
                
                # Extract prompt information from arguments
                prompt = self._extract_prompt_from_args(args, kwargs)
                
                # Record start time
                start_time = time.perf_counter()
                
                try:
                    # Call the original LLM function
                    response = await func(*args, **kwargs)
                    
                    # Calculate duration
                    duration_ms = (time.perf_counter() - start_time) * 1000
                    
                    # Extract response information
                    response_text = response.content or ""
                    token_usage = {
                        'input': response.input_tokens or 0,
                        'output': response.output_tokens or 0
                    }
                    
                    # Extract temperature from kwargs or use default
                    temperature = kwargs.get('temperature', 0.7)
                    
                    # Record the interaction
                    if self.tracker:
                        self.tracker.record_llm_interaction(
                            agent=agent_name,
                            model=model_name,
                            prompt=prompt,
                            response=response_text,
                            token_usage=token_usage,
                            temperature=temperature,
                            duration_ms=duration_ms,
                            success=True
                        )
                    
                    return response
                    
                except Exception as e:
                    # Calculate duration even for failed calls
                    duration_ms = (time.perf_counter() - start_time) * 1000
                    
                    # Record the failed interaction
                    if self.tracker:
                        self.tracker.record_llm_interaction(
                            agent=agent_name,
                            model=model_name,
                            prompt=prompt,
                            response="",
                            token_usage={'input': 0, 'output': 0},
                            temperature=kwargs.get('temperature', 0.7),
                            duration_ms=duration_ms,
                            success=False,
                            error=str(e)
                        )
                    
                    # Re-raise the exception
                    raise
                    
            return wrapper
        return decorator
    
    def _extract_prompt_from_args(self, args: tuple, kwargs: Dict[str, Any]) -> str:
        """
        Extract prompt text from function arguments.
        
        Args:
            args: Positional arguments
            kwargs: Keyword arguments
            
        Returns:
            Extracted prompt text
        """
        # Try to find messages in kwargs
        messages = kwargs.get('messages', [])
        if messages and isinstance(messages, list):
            # Combine all message content
            prompt_parts = []
            for message in messages:
                if isinstance(message, dict) and 'content' in message:
                    role = message.get('role', 'unknown')
                    content = message.get('content', '')
                    prompt_parts.append(f"[{role}]: {content}")
            return "\n".join(prompt_parts)
        
        # Try to find prompt in args
        for arg in args:
            if isinstance(arg, str) and len(arg) > 10:  # Likely a prompt
                return arg
            elif isinstance(arg, list):  # Likely messages
                return self._extract_prompt_from_messages(arg)
        
        return "Unable to extract prompt"
    
    def _extract_prompt_from_messages(self, messages: List[Dict[str, Any]]) -> str:
        """Extract prompt from messages list."""
        prompt_parts = []
        for message in messages:
            if isinstance(message, dict) and 'content' in message:
                role = message.get('role', 'unknown')
                content = message.get('content', '')
                prompt_parts.append(f"[{role}]: {content}")
        return "\n".join(prompt_parts)


def create_interceptor_for_tracker(tracker: AgentCommunicationTracker) -> LLMInteractionInterceptor:
    """
    Create an LLM interaction interceptor for the given tracker.
    
    Args:
        tracker: AgentCommunicationTracker instance
        
    Returns:
        LLMInteractionInterceptor instance
    """
    return LLMInteractionInterceptor(tracker)


def intercept_llm_service_calls(llm_service, agent_name: str, tracker: AgentCommunicationTracker):
    """
    Monkey-patch an LLM service to intercept its calls.
    
    Args:
        llm_service: LLM service instance to intercept
        agent_name: Name of the agent using the service
        tracker: Tracker to record interactions
    """
    if not tracker or not tracker.enabled:
        return
    
    # Store original method
    original_chat_completion = llm_service.chat_completion
    
    async def intercepted_chat_completion(*args, **kwargs):
        """Intercepted chat completion method."""
        # Extract model name from service
        model_name = getattr(llm_service, 'model', 'unknown')
        if hasattr(llm_service, 'llm_config') and llm_service.llm_config:
            model_name = llm_service.llm_config.model_name

        # Extract temperature
        temperature = 0.7  # default
        if hasattr(llm_service, 'llm_config') and llm_service.llm_config:
            temperature = float(llm_service.llm_config.temperature)

        # Record the LLM interaction manually for better control
        start_time = time.monotonic()

        try:
            # Call original method
            result = await original_chat_completion(*args, **kwargs)
            duration_ms = (time.monotonic() - start_time) * 1000

            # Extract prompt and response
            prompt = str(args[0]) if args else str(kwargs.get('messages', ''))
            response = str(result.get('content', '')) if isinstance(result, dict) else str(result)

            # Calculate token usage
            token_usage = {'input': 0, 'output': 0}
            if isinstance(result, dict):
                if 'usage' in result:
                    usage = result['usage']
                    token_usage['input'] = usage.get('prompt_tokens', 0)
                    token_usage['output'] = usage.get('completion_tokens', 0)
                else:
                    # Estimate tokens if not provided
                    try:
                        from apps.main.utils.token_counter import count_tokens
                        token_usage['input'] = count_tokens(prompt, model_name)
                        token_usage['output'] = count_tokens(response, model_name)
                    except ImportError:
                        # Fallback to simple estimation
                        token_usage['input'] = len(prompt.split()) * 1.3  # Rough estimation
                        token_usage['output'] = len(response.split()) * 1.3
            elif hasattr(result, 'input_tokens') and hasattr(result, 'output_tokens'):
                # Handle LLMResponse objects
                token_usage['input'] = result.input_tokens
                token_usage['output'] = result.output_tokens
            else:
                # Fallback estimation
                try:
                    from apps.main.utils.token_counter import count_tokens
                    token_usage['input'] = count_tokens(prompt, model_name)
                    token_usage['output'] = count_tokens(response, model_name)
                except ImportError:
                    token_usage['input'] = len(prompt.split()) * 1.3
                    token_usage['output'] = len(response.split()) * 1.3

            # Record successful LLM interaction
            tracker.record_llm_interaction(
                agent=agent_name,
                model=model_name,
                prompt=prompt,
                response=response,
                token_usage=token_usage,
                temperature=temperature,
                duration_ms=duration_ms,
                success=True
            )

            return result

        except Exception as e:
            duration_ms = (time.monotonic() - start_time) * 1000

            # Record failed LLM interaction
            tracker.record_llm_interaction(
                agent=agent_name,
                model=model_name,
                prompt=str(args[0]) if args else str(kwargs.get('messages', '')),
                response='',
                token_usage={'input': 0, 'output': 0},
                temperature=temperature,
                duration_ms=duration_ms,
                success=False,
                error=str(e)
            )

            raise
    
    # Replace the method
    llm_service.chat_completion = intercepted_chat_completion
    
    logger.debug(f"Intercepted LLM service calls for agent {agent_name}")


def intercept_agent_llm_calls(agent, tracker: AgentCommunicationTracker):
    """
    Intercept LLM calls for a specific agent.
    
    Args:
        agent: Agent instance to intercept
        tracker: Tracker to record interactions
    """
    if not tracker or not tracker.enabled:
        return
    
    # Check if agent has an LLM client/service
    if hasattr(agent, 'llm_client'):
        intercept_llm_service_calls(agent.llm_client, agent.agent_role, tracker)
    elif hasattr(agent, 'llm_service'):
        intercept_llm_service_calls(agent.llm_service, agent.agent_role, tracker)
    
    logger.debug(f"Set up LLM call interception for agent {agent.agent_role}")
