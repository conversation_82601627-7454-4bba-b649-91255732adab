# apps/main/views/activity_colors.py

from django.http import JsonResponse
from django.conf import settings
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json


@require_http_methods(["GET"])
def get_activity_color_config(request):
    """
    API endpoint to get activity color configuration for frontend.
    
    Returns the base colors and modulation parameters that the frontend
    can use to calculate final colors based on energy level, challenge level, and mood.
    """
    try:
        # Get base domain colors
        domain_colors = getattr(settings, 'ACTIVITY_DOMAIN_COLORS', {
            'wellness': '#2ECC71',
            'personal_growth': '#27AE60',
            'creativity': '#E67E22',
            'learning': '#3498DB',
            'physical': '#E74C3C',
            'social': '#F39C12',
            'general': '#95A5A6',
        })
        
        # Get modulation parameters
        color_modulation = getattr(settings, 'ACTIVITY_COLOR_MODULATION', {
            'energy_brightness': {
                'low': 0.6,
                'medium': 0.8,
                'high': 1.0,
            },
            'challenge_saturation': {
                'easy': 0.6,
                'medium': 0.8,
                'hard': 1.0,
            },
            'mood_adjustments': {
                'stressed': {'brightness': 0.7, 'saturation': 0.6},
                'anxious': {'brightness': 0.7, 'saturation': 0.6},
                'excited': {'brightness': 1.0, 'saturation': 1.0},
                'tired': {'brightness': 0.6, 'saturation': 0.7},
                'focused': {'brightness': 0.9, 'saturation': 0.9},
                'creative': {'brightness': 0.95, 'saturation': 0.95},
            }
        })
        
        return JsonResponse({
            'success': True,
            'data': {
                'domain_colors': domain_colors,
                'modulation_parameters': color_modulation,
                'usage_instructions': {
                    'description': 'Use domain_colors as base colors, then apply modulation_parameters based on activity context',
                    'example': 'For a high-energy creative activity: base_color = domain_colors.creativity, apply energy_brightness.high',
                    'frontend_responsibility': 'Calculate final colors using HSL/HSV color space modulation'
                }
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@require_http_methods(["GET"])
def get_activity_color_by_domain(request, domain):
    """
    Get the base color for a specific activity domain.
    
    Args:
        domain: The activity domain (e.g., 'wellness', 'creativity', etc.)
    """
    try:
        from apps.main.agents.tools.activity_tools import _get_activity_color
        
        color = _get_activity_color(domain)
        
        return JsonResponse({
            'success': True,
            'data': {
                'domain': domain,
                'color': color
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
