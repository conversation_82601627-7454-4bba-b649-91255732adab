from apps.main.services.database_service import DatabaseService
from apps.main.llm.service import LLMService # Import base LLMService for type hinting

class MockDatabaseService(DatabaseService):
    """Mock implementation for DatabaseService for testing purposes."""

    def __init__(self):
        self.agent_definitions = {}
        self.tools = {}
        self.runs = {}
        self.memories = {}
        self.run_id_counter = 0

    def load_agent_definition(self, agent_role):
        """Mock load agent definition."""
        return self.agent_definitions.get(agent_role)

    async def load_tools(self, agent_definition):
        """Mock load tools."""
        return self.tools.get(agent_definition.role, [])

    async def start_run(self, agent_definition, user_profile_id, input_data, state):
        """Mock start run."""
        self.run_id_counter += 1
        run_id = f"mock_run_{self.run_id_counter}"
        self.runs[run_id] = {
            "agent_definition": agent_definition,
            "user_profile_id": user_profile_id,
            "input_data": input_data,
            "initial_state": state,
            "status": "running",
            "output_data": None,
            "final_state": None,
            "memory_updates": None,
            "error_message": None,
        }
        return type('MockAgentRun', (object,), {'id': run_id, 'agent': agent_definition, 'user_profile_id': user_profile_id})()

    async def complete_run(self, run_id, output_data, state, memory_updates=None, status='completed', error_message=None):
        """Mock complete run."""
        if run_id in self.runs:
            self.runs[run_id].update({
                "output_data": output_data,
                "final_state": state,
                "memory_updates": memory_updates,
                "status": status,
                "error_message": error_message,
            })
            # Simulate memory updates
            if memory_updates:
                for key, content in memory_updates.items():
                    self.memories[(self.runs[run_id]["agent_definition"].role, self.runs[run_id]["user_profile_id"], key)] = content
            return {"run_id": run_id, "status": status}
        return None

    async def get_memory(self, agent_role, user_profile_id, memory_key):
        """Mock get memory."""
        return self.memories.get((agent_role, user_profile_id, memory_key))

    async def set_memory(self, agent_role, user_profile_id, memory_key, content, confidence=1.0, expires_at=None):
        """Mock set memory."""
        self.memories[(agent_role, user_profile_id, memory_key)] = content
        return True

# Note: MockLLMClient is already defined in apps.main.llm.service.py
# If it were not, it would be defined here as well.
