# Generated migration to add detailed tool call tracking

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='benchmarkrun',
            name='tool_call_details',
            field=models.JSONField(
                default=dict,
                help_text="Detailed tool call information including mock vs real tool usage, call counts, and execution details"
            ),
        ),
    ]
