/**
 * App Shell Component
 * Main application container that orchestrates all other components
 */

import { LitElement, html, css } from 'lit';
import { customElement, state } from 'lit/decorators.js';

// Import components
import './game-wheel/game-wheel.js';
import './chat/chat-interface.js';
import './debug/debug-panel.js';
import './auth/login-form.ts';
import './modals/contract-disclaimer-modal.js';
import './progress/real-time-progress-bar.js';
import { ContractDisclaimerModal } from './modals/contract-disclaimer-modal.js';

// Import services
import { StateManager } from '../services/state-manager.js';
import { MessageHandler } from '../services/message-handler.js';
import { WebSocketManager } from '../services/websocket-manager.js';
import { ConfigService } from '../services/config-service.js';
import { AuthService } from '../services/auth-service.js';
import { DebugMessageFilter } from '../utils/debug-message-filter.js';

// Import types
import type { ChatMessage } from './chat/message-bubble.js';
import type { WheelData } from './game-wheel/wheel-types.js';

@customElement('app-shell')
export class AppShell extends LitElement {
  static styles = css`
    :host {
      display: block;
      width: 100%;
      min-height: 100vh;
      background: var(--gradient-primary);
      position: relative;
      overflow-x: hidden;
    }

    .app-container {
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      max-width: 100vw;
      margin: 0 auto;
      position: relative;
    }

    .header {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      padding: var(--spacing-4) var(--spacing-6);
      position: sticky;
      top: 0;
      z-index: var(--z-sticky);
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      max-width: 1200px;
      margin: 0 auto;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      color: white;
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
    }

    .logo-icon {
      width: 32px;
      height: 32px;
      border-radius: var(--radius-full);
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .status-bar {
      display: flex;
      align-items: center;
      gap: var(--spacing-4);
      color: white;
      font-size: var(--font-size-sm);
    }

    .connection-status {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: var(--radius-full);
      background: var(--color-error);
      transition: background var(--transition-fast);
    }

    .status-indicator.connected {
      background: var(--color-success);
      box-shadow: 0 0 8px rgba(76, 175, 80, 0.4);
    }

    .status-indicator.connecting {
      background: #ff9800;
      box-shadow: 0 0 8px rgba(255, 152, 0, 0.4);
      animation: pulse 1.5s ease-in-out infinite;
    }

    .status-indicator.disconnected {
      background: var(--color-error);
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .user-name {
      font-weight: 500;
    }

    .staff-badge {
      background: rgba(255, 193, 7, 0.2);
      color: #ffc107;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      font-weight: 600;
      text-transform: uppercase;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .user-icon-btn, .logout-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 14px;
    }

    .user-icon-btn:hover, .logout-btn:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.3);
    }

    .logout-btn:hover {
      background: rgba(255, 107, 107, 0.3);
      border-color: rgba(255, 107, 107, 0.5);
    }

    .debug-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 4px 8px;
      font-size: 10px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .debug-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: var(--spacing-6);
      gap: var(--spacing-6);
      max-width: 1200px;
      margin: 0 auto;
      width: 100%;
      position: relative;
      z-index: 1;
    }

    .wheel-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-6);
      min-height: 60vh;
    }

    .wheel-controls {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-4);
      width: 100%;
      max-width: 500px;
    }

    .button-bar {
      display: flex;
      gap: var(--spacing-4);
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: var(--spacing-4);
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .potentiometer-control {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-2);
      flex: 1;
    }

    .potentiometer-label {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
      font-weight: 500;
      text-align: center;
    }

    .potentiometer-slider {
      width: 100%;
      max-width: 120px;
      height: 6px;
      border-radius: 3px;
      background: linear-gradient(to right, #4CAF50, #FFC107, #FF5722);
      outline: none;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .potentiometer-slider::-webkit-slider-thumb {
      appearance: none;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #fff;
      border: 2px solid #007bff;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      transition: all 0.2s ease;
    }

    .potentiometer-slider::-webkit-slider-thumb:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .potentiometer-slider::-moz-range-thumb {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #fff;
      border: 2px solid #007bff;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .potentiometer-value {
      font-size: var(--font-size-xs);
      color: var(--color-text-muted);
      font-weight: 600;
    }

    .action-button-container {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 0 0 auto;
    }

    .action-button {
      padding: var(--spacing-3) var(--spacing-6);
      border: none;
      border-radius: 8px;
      font-size: var(--font-size-base);
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 100px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .generate-button {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    }

    .generate-button:hover:not(:disabled) {
      background: linear-gradient(135deg, #45a049, #3d8b40);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
    }

    .generate-button:disabled {
      background: #666;
      cursor: not-allowed;
      opacity: 0.6;
      transform: none;
      box-shadow: none;
    }

    .spin-button {
      background: linear-gradient(135deg, #FF6B6B, #e55a5a);
      color: white;
      box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
    }

    .spin-button:hover:not(:disabled) {
      background: linear-gradient(135deg, #e55a5a, #d94545);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
    }

    .spin-button:disabled {
      background: #666;
      cursor: not-allowed;
      opacity: 0.6;
      transform: none;
      box-shadow: none;
    }

    .activity-list {
      width: 100%;
      max-width: 500px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      overflow: hidden;
    }

    .activity-list-header {
      padding: var(--spacing-4);
      background: rgba(255, 255, 255, 0.1);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      font-weight: 600;
      color: var(--color-text-primary);
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .add-activity-btn {
      background: var(--color-success);
      color: white;
      border: none;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 18px;
      font-weight: bold;
    }

    .add-activity-btn:hover {
      background: var(--color-success-dark);
      transform: scale(1.1);
    }

    .remove-activity-btn {
      background: none;
      border: none;
      color: var(--color-error);
      cursor: pointer;
      padding: var(--spacing-1);
      border-radius: 4px;
      transition: all 0.2s ease;
      font-size: 16px;
      opacity: 0.7;
    }

    .remove-activity-btn:hover {
      background: rgba(255, 107, 107, 0.1);
      opacity: 1;
      transform: scale(1.1);
    }

    .activity-item {
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .activity-item:last-child {
      border-bottom: none;
    }

    .activity-item-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-3) var(--spacing-4);
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .activity-item-header:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    .activity-item-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      flex: 1;
    }

    .activity-color-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      flex-shrink: 0;
    }

    .activity-name {
      font-weight: 500;
      color: var(--color-text-primary);
    }

    .activity-expand-icon {
      transition: transform 0.2s ease;
      color: var(--color-text-secondary);
    }

    .activity-item.expanded .activity-expand-icon {
      transform: rotate(180deg);
    }

    .activity-item-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
    }

    .activity-item.expanded .activity-item-content {
      max-height: 200px;
    }

    .activity-item-details {
      padding: 0 var(--spacing-4) var(--spacing-3) var(--spacing-4);
      border-top: 1px solid rgba(255, 255, 255, 0.05);
    }

    .activity-description {
      color: var(--color-text-secondary);
      font-size: var(--font-size-sm);
      line-height: 1.5;
      margin-bottom: var(--spacing-3);
    }

    .activity-meta {
      display: flex;
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-3);
      font-size: var(--font-size-xs);
    }

    .activity-meta-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-1);
      color: var(--color-text-muted);
    }

    .activity-actions {
      display: flex;
      justify-content: flex-end;
    }

    .activity-change-btn {
      display: flex;
      align-items: center;
      gap: var(--spacing-1);
      padding: var(--spacing-2) var(--spacing-3);
      background: var(--color-primary);
      color: white;
      border: none;
      border-radius: 6px;
      font-size: var(--font-size-xs);
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .activity-change-btn:hover {
      background: var(--color-primary-dark);
      transform: translateY(-1px);
    }

    /* Modal styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      backdrop-filter: blur(4px);
    }

    .modal-overlay::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.4);
      z-index: -1;
    }

    .profile-modal {
      max-width: 90vw;
      max-height: 90vh;
      overflow-y: auto;
    }

    /* Static Profile Sections */
    .profile-static-section {
      margin-bottom: var(--spacing-6);
      padding: var(--spacing-4);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: var(--radius-lg);
      background: rgba(255, 255, 255, 0.05);
    }

    .profile-static-section h4 {
      color: var(--color-primary);
      margin: 0 0 var(--spacing-4) 0;
      font-size: 1.1rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .profile-static-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-3);
    }

    /* Compact Profile Section */
    .profile-compact-section {
      margin-bottom: var(--spacing-4);
      padding: var(--spacing-3);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: var(--radius-lg);
      background: rgba(255, 255, 255, 0.05);
    }

    .profile-compact-header h4 {
      color: var(--color-primary);
      margin: 0 0 var(--spacing-3) 0;
      font-size: 1rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .profile-compact-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: var(--spacing-2);
    }

    .profile-field-compact {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);
    }

    .profile-field-compact.profile-field-full-width {
      grid-column: 1 / -1;
    }

    .profile-field-compact label {
      font-size: 0.8rem;
      font-weight: 500;
      color: var(--color-text-secondary);
      margin: 0;
    }

    .profile-value-compact {
      background: rgba(255, 255, 255, 0.03);
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--radius-sm);
      border: 1px solid rgba(255, 255, 255, 0.05);
      color: var(--color-text);
      font-size: 0.9rem;
      min-height: 1.5rem;
      display: flex;
      align-items: center;
    }

    /* Goal display styles */
    .goal-description {
      margin-bottom: var(--spacing-2);
      font-style: italic;
      color: var(--color-text-secondary);
    }

    .goal-meta {
      display: flex;
      gap: var(--spacing-3);
      font-size: 0.8rem;
      color: var(--color-text-muted);
    }

    .goal-importance, .goal-strength {
      padding: var(--spacing-1) var(--spacing-2);
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-sm);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* Environment details styles */
    .environment-details {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);
    }

    .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-1) var(--spacing-2);
      background: rgba(255, 255, 255, 0.02);
      border-radius: var(--radius-sm);
      font-size: 0.85rem;
    }

    .detail-key {
      font-weight: 500;
      color: var(--color-text-secondary);
      flex: 1;
    }

    .detail-value {
      color: var(--color-text);
      text-align: right;
      max-width: 60%;
      word-break: break-word;
    }

    /* Enhanced Profile Modal with Accordion */
    .profile-section {
      margin-bottom: var(--spacing-4);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: var(--radius-lg);
      overflow: hidden;
      background: rgba(255, 255, 255, 0.02);
    }

    .profile-section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-4);
      cursor: pointer;
      background: rgba(255, 255, 255, 0.05);
      transition: background-color 0.2s ease;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .profile-section-header:hover {
      background: rgba(255, 255, 255, 0.08);
    }

    .profile-section-header h4 {
      color: var(--color-primary);
      margin: 0;
      font-size: 1.1rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .profile-section-icon {
      font-size: 1.2em;
    }

    .profile-section-toggle {
      transition: transform 0.2s ease;
      color: var(--color-text-secondary);
      font-size: 1.2em;
    }

    .profile-section.expanded .profile-section-toggle {
      transform: rotate(180deg);
    }

    .profile-section-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
    }

    .profile-section.expanded .profile-section-content {
      max-height: 500px;
    }

    .profile-section-body {
      padding: var(--spacing-4);
    }

    .profile-field {
      margin-bottom: var(--spacing-3);
    }

    .profile-field label {
      display: block;
      font-weight: 500;
      color: var(--color-text-secondary);
      margin-bottom: var(--spacing-1);
      font-size: 0.9rem;
    }

    .profile-value {
      background: rgba(255, 255, 255, 0.05);
      padding: var(--spacing-2) var(--spacing-3);
      border-radius: var(--radius-md);
      border: 1px solid rgba(255, 255, 255, 0.1);
      color: var(--color-text);
    }

    .profile-value.editable {
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .profile-value.editable:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: var(--color-primary);
    }

    .profile-edit-input {
      width: 100%;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid var(--color-primary);
      color: var(--color-text);
      padding: var(--spacing-2) var(--spacing-3);
      border-radius: var(--radius-md);
      font-size: 0.9rem;
    }

    .profile-edit-input:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.3);
    }

    .profile-edit-actions {
      display: flex;
      gap: var(--spacing-2);
      margin-top: var(--spacing-2);
    }

    .profile-edit-btn {
      padding: var(--spacing-1) var(--spacing-3);
      border: none;
      border-radius: var(--radius-sm);
      font-size: 0.8rem;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .profile-edit-btn.save {
      background: var(--color-success);
      color: white;
    }

    .profile-edit-btn.cancel {
      background: var(--color-error);
      color: white;
    }

    .loading-state {
      text-align: center;
      padding: var(--spacing-8);
    }

    .loading-state .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.1);
      border-top: 3px solid var(--color-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto var(--spacing-4);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .modal {
      background: var(--color-background);
      border-radius: 12px;
      width: 90%;
      max-width: 600px;
      max-height: 85vh;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      flex-direction: column;
    }

    /* Bootstrap-compatible modal classes */
    .modal-dialog {
      background: var(--color-background);
      border-radius: 12px;
      width: 90%;
      max-width: 600px;
      max-height: 85vh;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      flex-direction: column;
    }

    .modal-content {
      display: flex;
      flex-direction: column;
      height: 100%;
      background: transparent;
      border: none;
    }

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-4);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      background: rgba(255, 255, 255, 0.05);
    }

    .modal-title {
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--color-text-primary);
    }

    .modal-close {
      background: none;
      border: none;
      font-size: var(--font-size-xl);
      color: var(--color-text-secondary);
      cursor: pointer;
      padding: var(--spacing-1);
      border-radius: 4px;
      transition: all 0.2s ease;
    }

    .modal-close:hover {
      background: rgba(255, 255, 255, 0.1);
      color: var(--color-text-primary);
    }

    .modal-body {
      padding: var(--spacing-4);
      flex: 1;
      overflow-y: auto;
      min-height: 0; /* Important for flex scrolling */
    }

    /* Enhanced scrolling for activity modal */
    .activity-modal .modal-body {
      max-height: calc(85vh - 120px); /* Account for header and footer */
      overflow-y: auto;
    }

    .search-input {
      width: 100%;
      padding: var(--spacing-3);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.05);
      color: var(--color-text-primary);
      font-size: var(--font-size-base);
      margin-bottom: var(--spacing-4);
    }

    .search-input:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
    }

    .search-input::placeholder {
      color: var(--color-text-muted);
    }

    .activity-catalog {
      display: grid;
      gap: var(--spacing-3);
      max-height: calc(60vh - 120px); /* Ensure scrolling within modal */
      overflow-y: auto;
      padding-right: var(--spacing-2); /* Space for scrollbar */
    }

    .catalog-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      padding: var(--spacing-3);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      background: rgba(255, 255, 255, 0.02);
    }

    .catalog-item:hover {
      background: rgba(255, 255, 255, 0.05);
      border-color: var(--color-primary);
      transform: translateY(-1px);
    }

    .catalog-item.tailored {
      border-color: rgba(255, 193, 7, 0.3);
      background: rgba(255, 193, 7, 0.1);
    }

    .catalog-item.tailored:hover {
      border-color: rgba(255, 193, 7, 0.5);
      background: rgba(255, 193, 7, 0.15);
    }

    .catalog-item-info {
      flex: 1;
    }

    .catalog-item-name {
      font-weight: 500;
      color: var(--color-text-primary);
      margin-bottom: var(--spacing-1);
    }

    .catalog-item-description {
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
      line-height: 1.4;
    }

    .catalog-item-meta {
      display: flex;
      gap: var(--spacing-2);
      margin-top: var(--spacing-2);
      font-size: var(--font-size-xs);
      color: var(--color-text-muted);
    }

    .catalog-item-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      margin-bottom: var(--spacing-2);
    }

    .activity-type-icon {
      font-size: 1.2em;
    }

    .activity-type-badge {
      font-size: 0.75em;
      padding: 2px 6px;
      border-radius: 4px;
      font-weight: 600;
      text-transform: uppercase;
      margin-left: auto;
    }

    .activity-type-badge.tailored {
      background: rgba(255, 193, 7, 0.2);
      color: #ffc107;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .activity-type-badge.tailored::before {
      content: '✨ ';
      font-size: 0.9em;
    }

    .activity-type-badge.generic {
      background: rgba(108, 117, 125, 0.2);
      color: #6c757d;
      border: 1px solid rgba(108, 117, 125, 0.3);
    }

    .activity-type-badge.generic::before {
      content: '📋 ';
      font-size: 0.9em;
    }

    /* Enhanced visual differentiation for activity type icons */
    .activity-type-icon.tailored {
      color: #ffc107;
      text-shadow: 0 0 8px rgba(255, 193, 7, 0.5);
      font-size: 1.3em;
    }

    .activity-type-icon.generic {
      color: #6c757d;
      opacity: 0.8;
    }

    /* Enhanced catalog item styling for better differentiation */
    .catalog-item.tailored {
      border-left: 3px solid #ffc107;
      background: linear-gradient(135deg, rgba(255, 193, 7, 0.05), rgba(255, 193, 7, 0.02));
    }

    .catalog-item.generic {
      border-left: 3px solid rgba(108, 117, 125, 0.3);
      background: rgba(108, 117, 125, 0.02);
    }

    .catalog-item.tailored:hover {
      background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
      border-left-color: #ffcd39;
    }

    /* Winning Modal Styles */
    .winning-modal-overlay {
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(5px);
    }

    .winning-modal-content {
      max-width: 500px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: var(--radius-xl);
      color: white;
      text-align: center;
      animation: winningModalAppear 0.5s ease-out;
    }

    @keyframes winningModalAppear {
      0% {
        transform: scale(0.8) translateY(-20px);
        opacity: 0;
      }
      100% {
        transform: scale(1) translateY(0);
        opacity: 1;
      }
    }

    .winning-header {
      padding: var(--spacing-6) var(--spacing-4) var(--spacing-4);
      position: relative;
    }

    .winning-icon {
      font-size: 3rem;
      margin-bottom: var(--spacing-2);
      animation: bounce 1s infinite;
    }

    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
      }
      40% {
        transform: translateY(-10px);
      }
      60% {
        transform: translateY(-5px);
      }
    }

    .winning-header h2 {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 700;
    }

    .winning-activity {
      padding: var(--spacing-4);
    }

    .activity-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      margin-bottom: var(--spacing-4);
    }

    .activity-icon {
      font-size: 2.5rem;
      text-align: center;
      min-width: 50px;
    }

    .activity-title {
      flex: 1;
    }

    .activity-name {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: var(--spacing-2);
      color: #fff;
    }

    .activity-description {
      font-size: 1rem;
      opacity: 0.9;
      margin-bottom: var(--spacing-4);
      line-height: 1.5;
      background: rgba(255, 255, 255, 0.1);
      padding: var(--spacing-3);
      border-radius: var(--radius-md);
      border-left: 4px solid rgba(255, 255, 255, 0.5);
    }

    .activity-instructions {
      background: rgba(255, 255, 255, 0.15);
      border-radius: var(--radius-md);
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-4);
      text-align: left;
    }

    .activity-instructions h4 {
      margin: 0 0 var(--spacing-2) 0;
      color: rgba(255, 255, 255, 0.95);
      font-size: 1rem;
      font-weight: 600;
    }

    .activity-instructions p {
      margin: 0;
      color: rgba(255, 255, 255, 0.85);
      line-height: 1.5;
      font-size: 0.95rem;
    }

    .tailored-info {
      background: rgba(255, 193, 7, 0.2);
      border: 1px solid rgba(255, 193, 7, 0.4);
      border-radius: var(--radius-md);
      padding: var(--spacing-3);
      margin-bottom: var(--spacing-4);
    }

    .tailored-info h4 {
      color: #ffc107;
      margin: 0 0 var(--spacing-2) 0;
      font-size: 1rem;
    }

    .tailored-info p {
      margin: 0;
      color: #fff;
      line-height: 1.5;
      opacity: 0.9;
    }

    .activity-details {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-2);
      background: rgba(255, 255, 255, 0.1);
      padding: var(--spacing-3);
      border-radius: var(--radius-lg);
      margin-bottom: var(--spacing-4);
    }

    .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-2);
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-sm);
    }

    .detail-label {
      font-weight: 500;
      opacity: 0.8;
      font-size: 0.9rem;
    }

    .detail-value {
      font-weight: 600;
      font-size: 0.9rem;
    }

    .activity-requirements {
      margin-bottom: var(--spacing-4);
    }

    .requirement-section {
      margin-bottom: var(--spacing-3);
      background: rgba(255, 255, 255, 0.05);
      padding: var(--spacing-3);
      border-radius: var(--radius-md);
      border-left: 3px solid rgba(255, 255, 255, 0.3);
    }

    .requirement-section h4 {
      margin: 0 0 var(--spacing-2) 0;
      color: rgba(255, 255, 255, 0.9);
      font-size: 1rem;
    }

    .requirement-section p {
      margin: 0;
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.5;
    }

    .winning-actions {
      text-align: center;
      margin-top: var(--spacing-4);
    }

    .winning-actions .primary-button {
      background: rgba(255, 255, 255, 0.9);
      color: #667eea;
      border: none;
      padding: var(--spacing-3) var(--spacing-6);
      border-radius: var(--radius-lg);
      font-size: 1.1rem;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .winning-actions .primary-button:hover {
      background: #fff;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    .winning-modal-content .modal-footer {
      padding: var(--spacing-4);
      display: flex;
      gap: var(--spacing-3);
      justify-content: center;
    }

    .winning-modal-content .btn {
      padding: var(--spacing-3) var(--spacing-6);
      border-radius: var(--radius-lg);
      font-weight: 600;
      transition: all 0.2s ease;
    }

    .winning-modal-content .btn-primary {
      background: #fff;
      color: #667eea;
      border: none;
    }

    .winning-modal-content .btn-primary:hover {
      background: #f8f9fa;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .winning-modal-content .btn-secondary {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .winning-modal-content .btn-secondary:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }

    /* Contract Confirmation Styles in Winning Modal */
    .contract-confirmation {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-4);
      backdrop-filter: blur(10px);
    }

    .contract-confirmation-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      margin-bottom: var(--spacing-3);
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
    }

    .contract-icon {
      font-size: 1.2rem;
    }

    .contract-message {
      font-size: 0.95rem;
    }

    .signature-display {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-2);
    }

    .signature-image {
      max-width: 200px;
      max-height: 60px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.9);
      padding: var(--spacing-1);
    }

    .signature-timestamp {
      font-size: 0.8rem;
      color: rgba(255, 255, 255, 0.7);
      font-style: italic;
    }

    .no-results {
      text-align: center;
      padding: var(--spacing-6);
      color: var(--color-text-muted);
    }

    /* Create Activity Modal Styles */
    .create-activity-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-2);
      width: 100%;
      padding: var(--spacing-3);
      background: var(--color-primary);
      color: white;
      border: none;
      border-radius: 8px;
      font-size: var(--font-size-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-bottom: var(--spacing-4);
    }

    .create-activity-btn:hover {
      background: var(--color-primary-dark);
      transform: translateY(-1px);
    }

    .form-group {
      margin-bottom: var(--spacing-4);
    }

    .form-label {
      display: block;
      margin-bottom: var(--spacing-2);
      font-weight: 500;
      color: var(--color-text-primary);
      font-size: var(--font-size-sm);
    }

    .form-input,
    .form-textarea,
    .form-select {
      width: 100%;
      padding: var(--spacing-3);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.05);
      color: var(--color-text-primary);
      font-size: var(--font-size-sm);
      transition: all 0.2s ease;
    }

    .form-input:focus,
    .form-textarea:focus,
    .form-select:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
    }

    .form-textarea {
      resize: vertical;
      min-height: 80px;
    }

    .form-range {
      width: 100%;
      margin: var(--spacing-2) 0;
    }

    .range-value {
      text-align: center;
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
      margin-top: var(--spacing-1);
    }

    .modal-actions {
      display: flex;
      gap: var(--spacing-3);
      justify-content: flex-end;
      margin-top: var(--spacing-4);
      padding-top: var(--spacing-4);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .btn-secondary {
      padding: var(--spacing-2) var(--spacing-4);
      background: transparent;
      color: var(--color-text-secondary);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .btn-secondary:hover {
      background: rgba(255, 255, 255, 0.05);
      color: var(--color-text-primary);
    }

    .btn-primary {
      padding: var(--spacing-2) var(--spacing-4);
      background: var(--color-primary);
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .btn-primary:hover {
      background: var(--color-primary-dark);
    }

    .btn-primary:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .wheel-container {
      position: relative;
      width: 100%;
      max-width: 500px;
      aspect-ratio: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .wheel-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.3;
      filter: grayscale(100%) brightness(0.7);
      z-index: 0;
    }

    .wheel-foreground {
      position: relative;
      z-index: 1;
      width: 100%;
      height: 100%;
    }

    .wheel-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: rgba(255, 255, 255, 0.7);
      text-align: center;
      padding: var(--spacing-8);
    }

    .wheel-placeholder-icon {
      width: 64px;
      height: 64px;
      margin-bottom: var(--spacing-4);
      opacity: 0.5;
    }

    .chat-section {
      background: rgba(255, 255, 255, 0.95);
      border-radius: var(--radius-2xl);
      padding: var(--spacing-6);
      box-shadow: var(--shadow-xl);
      backdrop-filter: blur(10px);
    }

    .error-banner {
      background: var(--color-error);
      color: white;
      padding: var(--spacing-4);
      border-radius: var(--radius-lg);
      margin-bottom: var(--spacing-4);
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
    }

    .error-banner button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: var(--spacing-2) var(--spacing-3);
      font-size: var(--font-size-sm);
    }

    .progress-bar-container {
      margin-bottom: var(--spacing-4, 16px);
      padding: 0 var(--spacing-2, 8px);
      animation: slideDown 0.3s ease-out;
    }

    /* Progress Modal Overlay */
    .progress-modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.4);
      backdrop-filter: blur(4px);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      animation: fadeIn 0.3s ease-out;
    }

    .progress-modal {
      background: white;
      border-radius: 16px;
      padding: 24px;
      max-width: 500px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      animation: slideUp 0.3s ease-out;
    }

    .progress-modal.user-friendly {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .progress-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e5e7eb;
    }

    .progress-modal-title {
      font-size: 20px;
      font-weight: 600;
      color: #111827;
      margin: 0;
    }

    .progress-modal-close {
      background: none;
      border: none;
      font-size: 24px;
      color: #6b7280;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s ease;
    }

    .progress-modal-close:hover {
      background: #f3f4f6;
      color: #374151;
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .connection-error-banner {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background: #dc3545; /* Red background for connection errors */
      color: white;
      padding: var(--spacing-3);
      text-align: center;
      z-index: 2000;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      font-weight: 500;
    }

    /* Enjoy Overlay Styles */
    .enjoy-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.4);
      backdrop-filter: blur(10px);
      z-index: 3000;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--color-text-primary);
      text-align: center;
      animation: enjoyOverlayFadeIn 0.5s ease-out;
    }

    @keyframes enjoyOverlayFadeIn {
      0% {
        opacity: 0;
        transform: scale(0.95);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }

    .enjoy-overlay-content {
      max-width: 600px;
      padding: var(--spacing-8);
      background: rgba(255, 255, 255, 0.9);
      border-radius: var(--radius-2xl);
      border: 1px solid rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(20px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .enjoy-title {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: var(--spacing-6);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
    }

    .enjoy-activity-info {
      margin-bottom: var(--spacing-6);
    }

    .enjoy-activity-name {
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: var(--spacing-3);
      color: var(--color-text-primary);
    }

    .enjoy-activity-description {
      font-size: 1.2rem;
      line-height: 1.6;
      color: var(--color-text-secondary);
      margin-bottom: var(--spacing-4);
    }

    .enjoy-activity-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: var(--spacing-3);
      margin-bottom: var(--spacing-6);
    }

    .enjoy-detail-item {
      background: rgba(102, 126, 234, 0.1);
      padding: var(--spacing-3);
      border-radius: var(--radius-lg);
      border: 1px solid rgba(102, 126, 234, 0.2);
    }

    .enjoy-detail-label {
      font-size: 0.9rem;
      color: var(--color-text-secondary);
      margin-bottom: var(--spacing-1);
    }

    .enjoy-detail-value {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--color-text-primary);
    }

    .enjoy-hourglass {
      font-size: 3rem;
      margin: var(--spacing-6) 0;
      animation: rotateHourglass 2s linear infinite;
    }

    @keyframes rotateHourglass {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .enjoy-timer {
      font-size: 1.2rem;
      color: var(--color-text-secondary);
      margin-bottom: var(--spacing-4);
    }

    .enjoy-close-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: var(--spacing-3) var(--spacing-6);
      border-radius: var(--radius-lg);
      font-size: 1.1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .enjoy-close-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    /* Post-Activity Feedback Modal Styles */
    .post-activity-feedback-modal {
      max-width: 600px;
      width: 90%;
      max-height: 90vh;
      overflow-y: auto;
    }

    /* Update modal overlay to have white background */
    .modal-overlay {
      background: rgba(255, 255, 255, 0.4);
      backdrop-filter: blur(10px);
    }

    .activity-summary {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
      border-radius: var(--radius-lg);
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-6);
      border: 1px solid rgba(102, 126, 234, 0.2);
    }

    .activity-info .activity-name {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: var(--spacing-2);
      color: var(--color-text-primary);
    }

    .activity-info .activity-description {
      font-size: 1rem;
      line-height: 1.5;
      color: var(--color-text-secondary);
    }

    .feedback-message {
      font-size: 1.1rem;
      line-height: 1.6;
      color: var(--color-text-secondary);
      margin-bottom: var(--spacing-6);
      text-align: center;
      font-style: italic;
    }

    .feedback-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-6);
    }

    .rating-group {
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-lg);
      padding: var(--spacing-4);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .rating-group .form-label {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: var(--spacing-3);
      color: var(--color-text-primary);
    }

    .rating-options {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-2);
      justify-content: space-between;
    }

    .rating-option {
      flex: 1;
      min-width: 100px;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: var(--spacing-3);
      border-radius: var(--radius-md);
      border: 2px solid rgba(255, 255, 255, 0.1);
      background: rgba(255, 255, 255, 0.05);
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
    }

    .rating-option:hover {
      border-color: rgba(102, 126, 234, 0.5);
      background: rgba(102, 126, 234, 0.1);
      transform: translateY(-2px);
    }

    .rating-option input[type="radio"] {
      display: none;
    }

    .rating-option input[type="radio"]:checked + .rating-text {
      color: #667eea;
      font-weight: 600;
    }

    .rating-option:has(input[type="radio"]:checked) {
      border-color: #667eea;
      background: rgba(102, 126, 234, 0.2);
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .rating-text {
      font-size: 0.9rem;
      color: var(--color-text-secondary);
      transition: all 0.3s ease;
      margin-top: var(--spacing-1);
    }

    @media (max-width: 768px) {
      .rating-options {
        flex-direction: column;
      }

      .rating-option {
        min-width: auto;
        flex-direction: row;
        justify-content: flex-start;
        gap: var(--spacing-2);
      }

      .rating-text {
        margin-top: 0;
      }
    }

    .loading-banner {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: var(--spacing-3) var(--spacing-4);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-3);
      z-index: var(--z-sticky);
      font-size: var(--font-size-sm);
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transform: translateY(-100%);
      animation: slideDown 0.3s ease-out forwards;
    }

    .loading-banner .dots-animation {
      display: flex;
      gap: 4px;
      align-items: center;
    }

    .loading-banner .dots-animation span {
      width: 6px;
      height: 6px;
      background: white;
      border-radius: 50%;
      animation: dots-bounce 1.4s infinite ease-in-out;
    }

    .loading-banner .dots-animation span:nth-child(1) {
      animation-delay: -0.32s;
    }

    .loading-banner .dots-animation span:nth-child(2) {
      animation-delay: -0.16s;
    }

    .loading-banner .dots-animation span:nth-child(3) {
      animation-delay: 0s;
    }

    @keyframes slideDown {
      to {
        transform: translateY(0);
      }
    }

    @keyframes dots-bounce {
      0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.7;
      }
      40% {
        transform: scale(1);
        opacity: 1;
      }
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
      .main-content {
        padding: var(--spacing-4);
        gap: var(--spacing-4);
      }

      .header {
        padding: var(--spacing-3) var(--spacing-4);
      }

      .logo {
        font-size: var(--font-size-lg);
      }

      .chat-section {
        padding: var(--spacing-4);
      }
    }
  `;

  @state() private messages: ChatMessage[] = [];
  @state() private wheelData: WheelData | null = null;
  @state() private wsConnected = false;
  @state() private isLoading = false;
  @state() private error: string | null = null;
  @state() private isAuthenticated = false;
  @state() private showDebugPanel = false;
  @state() private currentMode: 'debug' | 'production' = 'debug';
  @state() private debugUserId = '';
  @state() private debugLLMConfigId = '';
  @state() private timeAvailable = 50; // 0-100 scale
  @state() private showProgressBar = false;
  @state() private currentProgressTrackerId = '';
  @state() private progressTitle = 'Processing...';
  @state() private progressModalMode: 'debug' | 'user-friendly' = 'user-friendly';
  @state() private energyLevel = 50; // 0-100 scale
  @state() private currentUser: { id: string; name: string; isStaff?: boolean } | null = null;
  @state() private connectionState: 'disconnected' | 'connecting' | 'connected' = 'disconnected';
  @state() private expandedActivities = new Set<string>(); // Track which activities are expanded
  @state() private showActivityModal = false;
  @state() private selectedActivityId: string | null = null;
  @state() private searchQuery = '';
  @state() private activityCatalog: Array<{
    id: string;
    name: string;
    description: string;
    domain: string;
    base_challenge_rating: number;
    type: 'generic' | 'tailored';
    icon: string;
  }> = [];

  // Activity catalog caching
  private activityCatalogCache: {
    data: any[] | null;
    timestamp: number;
    userId: string | null;
  } = {
    data: null,
    timestamp: 0,
    userId: null
  };

  private readonly ACTIVITY_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  // New activity creation modal state
  @state() private showCreateActivityModal = false;
  @state() private newActivityForm = {
    name: '',
    description: '',
    domain: 'general',
    base_challenge_rating: 50
  };
  @state() private showProfileModal = false;
  @state() private currentUserProfile: any = null;
  @state() private showWinningModal = false;
  @state() private winningActivity: any = null;
  @state() private expandedProfileSections = new Set<string>(['basic']); // Default to basic section expanded
  @state() private editingProfileFields = new Set<string>(); // Track which fields are being edited
  @state() private showFeedbackModal = false;
  @state() private feedbackModalConfig = {
    title: '',
    message: '',
    feedback_type: '',
    content_type: '',
    object_id: '',
    primaryButtonLabel: 'Send Feedback',
    secondaryButtonLabel: 'Cancel'
  };
  @state() private showAddActivityModal = false;
  @state() private showContractModal = false;
  @state() private showEnjoyOverlay = false;
  @state() private enjoyOverlayActivity: any = null;
  @state() private enjoyOverlayStartTime: number | null = null;
  @state() private showPostActivityFeedbackModal = false;
  @state() private postActivityFeedbackConfig = {
    title: '',
    message: '',
    feedback_type: '',
    content_type: '',
    object_id: '',
    activity: null as any
  };

  private stateManager = StateManager.getInstance();
  private messageHandler = MessageHandler.getInstance();
  private websocketManager = WebSocketManager.getInstance();
  private configService = ConfigService.getInstance();
  private authService = AuthService.getInstance();
  private debugFilter = DebugMessageFilter.getInstance();

  connectedCallback() {
    super.connectedCallback();
    this.loadDebugSelections();
    this.loadCurrentUser();
    this.checkForActiveEnjoyOverlay();
    this.initializeApp();
    this.setupEventListeners();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    this.websocketManager.disconnect();
    this.removeEventListeners();
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    window.addEventListener('auth-changed', this.handleAuthChange);
    window.addEventListener('config-changed', this.handleConfigChange);
    window.addEventListener('keydown', this.handleKeydown);
    window.addEventListener('load-mocked-wheel', this.handleLoadMockedWheel);
    window.addEventListener('progress-update', this.handleProgressUpdate);
  }

  /**
   * Remove event listeners
   */
  private removeEventListeners(): void {
    window.removeEventListener('auth-changed', this.handleAuthChange);
    window.removeEventListener('config-changed', this.handleConfigChange);
    window.removeEventListener('keydown', this.handleKeydown);
    window.removeEventListener('load-mocked-wheel', this.handleLoadMockedWheel);
    window.removeEventListener('progress-update', this.handleProgressUpdate);
  }

  /**
   * Handle authentication changes
   */
  private handleAuthChange = (event: any) => {
    this.isAuthenticated = event.detail.isAuthenticated;
    if (this.isAuthenticated) {
      // Update current user from auth service
      const authUser = this.authService.getCurrentUser();
      if (authUser) {
        this.currentUser = {
          id: authUser.id,
          name: authUser.name || authUser.username || 'User',
          isStaff: authUser.is_staff || false
        };
      }
      this.initializeApp();
    } else {
      // Clear user data on logout
      this.currentUser = null;
    }
    // Force re-render to update status bar
    this.requestUpdate();
  };

  /**
   * Handle configuration changes
   */
  private handleConfigChange = (event: any) => {
    // Reinitialize with new config if needed
    console.log('Configuration changed:', event.detail.config);
  };

  /**
   * Handle keyboard shortcuts
   */
  private handleKeydown = (event: KeyboardEvent) => {
    // Toggle debug panel with Ctrl+Shift+D
    if (event.ctrlKey && event.shiftKey && event.key === 'D' && this.configService.isDebugMode()) {
      event.preventDefault();
      this.showDebugPanel = !this.showDebugPanel;
    }
  };

  /**
   * Handle load mocked wheel request from debug panel
   */
  private handleLoadMockedWheel = (event: any) => {
    console.log('🎡 Loading mocked wheel items from debug panel...');

    // Create mock wheel data with diverse activities
    const mockWheelData: WheelData = {
      segments: [
        {
          id: 'mock-1',
          text: '🏃‍♂️ Morning Run',
          percentage: 15,
          color: '#FF6B6B',
          description: 'Start your day with an energizing 20-minute jog around the neighborhood'
        },
        {
          id: 'mock-2',
          text: '📚 Study Session',
          percentage: 20,
          color: '#4ECDC4',
          description: 'Focus on your most challenging subject for 45 minutes with breaks'
        },
        {
          id: 'mock-3',
          text: '🎨 Creative Drawing',
          percentage: 12,
          color: '#45B7D1',
          description: 'Express yourself through art - sketch, paint, or digital drawing'
        },
        {
          id: 'mock-4',
          text: '🧘‍♀️ Meditation',
          percentage: 10,
          color: '#96CEB4',
          description: 'Find inner peace with a 15-minute mindfulness session'
        },
        {
          id: 'mock-5',
          text: '🍳 Cooking',
          percentage: 18,
          color: '#FFEAA7',
          description: 'Try a new healthy recipe and enjoy the process of cooking'
        },
        {
          id: 'mock-6',
          text: '🎵 Music Practice',
          percentage: 13,
          color: '#DDA0DD',
          description: 'Practice your instrument or learn a new song for 30 minutes'
        },
        {
          id: 'mock-7',
          text: '📞 Call Friend',
          percentage: 8,
          color: '#98FB98',
          description: 'Reconnect with a friend you haven\'t spoken to in a while'
        },
        {
          id: 'mock-8',
          text: '🌱 Garden Care',
          percentage: 4,
          color: '#F0E68C',
          description: 'Tend to your plants or start a small herb garden'
        }
      ]
    };

    // Set the wheel data
    this.wheelData = mockWheelData;

    // Add a message to show the wheel was loaded
    const message = {
      id: `mock-loaded-${Date.now()}`,
      type: 'ai' as const,
      content: '🎡 Perfect! I\'ve loaded a diverse set of activities for you to try. Give the wheel a spin to see what adventure awaits!',
      timestamp: new Date()
    };
    this.messages = [...this.messages, message];

    console.log('✅ Mock wheel data loaded successfully');
  };

  /**
   * Initializes the application
   */
  private async initializeApp(): Promise<void> {
    try {
      this.isLoading = true;
      this.connectionState = 'connecting';
      this.currentMode = this.configService.getMode();

      // Check authentication status (but respect manual logout state)
      if (!this.isAuthenticated) {
        // If we're manually logged out, don't override with auth service
        this.isAuthenticated = this.authService.isAuthenticated();
      }

      if (!this.isAuthenticated) {
        this.isLoading = false;
        return; // Show login form
      }

      // Try to initialize WebSocket connection with robust error handling and race condition protection
      const config = this.configService.getConfig();
      let backendAvailable = false;
      let wsError: Error | null = null;

      try {
        console.log('🔌 Attempting WebSocket connection to:', config.websocket.url);

        // Use a longer timeout for initial connection to avoid race conditions
        const wsInitPromise = this.websocketManager.initialize(config.websocket);
        const extendedTimeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Extended WebSocket connection timeout')), 45000)
        );

        await Promise.race([wsInitPromise, extendedTimeoutPromise]);

        this.wsConnected = true;
        this.connectionState = 'connected';
        backendAvailable = true;
        this.setupMessageHandlers();
        console.log('✅ WebSocket connection established successfully');

        // Set up late connection handler in case WebSocket connects after timeout
        this.setupLateConnectionHandler();

        // Verify backend API availability with timeout (non-blocking)
        try {
          const apiTestPromise = this.testBackendAPI();
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('API test timeout')), 5000)
          );

          await Promise.race([apiTestPromise, timeoutPromise]);
          console.log('✅ Backend API endpoints verified');
        } catch (apiError) {
          const errorMessage = apiError instanceof Error ? apiError.message : 'Unknown error';
          console.warn('⚠️ WebSocket connected but API endpoints unavailable:', errorMessage);
          console.warn('⚠️ Continuing with WebSocket-only mode - chat functionality will work');
          // WebSocket works but API doesn't - still consider backend available for chat
        }

      } catch (error) {
        wsError = error as Error;
        this.wsConnected = false;
        this.connectionState = 'disconnected';
        backendAvailable = false;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('❌ WebSocket connection failed:', errorMessage);

        // Set up late connection handler in case WebSocket connects later
        this.setupLateConnectionHandler();
      }

      // Initialize appropriate mode based on backend availability
      if (backendAvailable) {
        // Backend is available - initialize normally
        if (this.configService.isProductionMode()) {
          // Production mode with backend - minimal demo data
          this.initializeProductionMode();
        } else {
          // Debug mode with backend - clean slate for development
          this.initializeDebugMode();
        }
      } else {
        // Backend unavailable - fallback to demo mode but allow recovery
        console.warn('⚠️ Backend server not available, initializing demo mode');
        console.warn('WebSocket error details:', wsError?.message || 'Unknown error');
        this.initializeDemoMode();
      }

    } catch (error) {
      console.error('Failed to initialize app:', error);
      this.error = 'Failed to initialize application';
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Set up handler for late WebSocket connections (race condition recovery)
   */
  private setupLateConnectionHandler(): void {
    // Listen for WebSocket connection events that might happen after initial timeout
    const handleLateConnection = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && !this.wsConnected) {
        console.log('🔄 Late WebSocket connection detected - recovering from demo mode');
        this.wsConnected = true;
        this.connectionState = 'connected';

        // If we're in demo mode due to connection failure, switch to proper mode
        if (this.configService.isDebugMode()) {
          this.initializeDebugMode();
        } else {
          this.initializeProductionMode();
        }

        // Force update of chat interface connection status
        this.updateChatConnectionStatus('connected');

        // Remove this handler since we've recovered
        window.removeEventListener('websocket-connected', handleLateConnection);
      }
    };

    window.addEventListener('websocket-connected', handleLateConnection);

    // Clean up after 30 seconds if no late connection
    setTimeout(() => {
      window.removeEventListener('websocket-connected', handleLateConnection);
    }, 30000);
  }

  /**
   * Test backend API availability
   */
  private async testBackendAPI(): Promise<void> {
    const baseUrl = this.getBackendBaseUrl();

    // Test the health endpoint first (most reliable)
    try {
      const healthResponse = await fetch(`${baseUrl}/api/health/`, {
        method: 'GET',
        signal: AbortSignal.timeout(3000)
      });

      if (healthResponse.ok) {
        console.log('✅ Health endpoint check passed');
        return;
      }
    } catch (healthError) {
      const errorMessage = healthError instanceof Error ? healthError.message : 'Unknown error';
      console.warn('⚠️ Health endpoint check failed, trying fallback:', errorMessage);
    }

    // Fallback to debug users endpoint
    const response = await fetch(`${baseUrl}/api/debug/users/`, {
      method: 'HEAD',
      signal: AbortSignal.timeout(3000)
    });

    if (!response.ok) {
      throw new Error(`API test failed: ${response.status} ${response.statusText}`);
    }
  }

  /**
   * Get backend base URL from WebSocket URL
   */
  private getBackendBaseUrl(): string {
    const config = this.configService.getConfig();
    let baseUrl = config.websocket.url;

    // Convert WebSocket URL to HTTP URL
    if (baseUrl.startsWith('ws://')) {
      baseUrl = baseUrl.replace('ws://', 'http://');
    } else if (baseUrl.startsWith('wss://')) {
      baseUrl = baseUrl.replace('wss://', 'https://');
    }

    // Remove WebSocket path
    baseUrl = baseUrl.replace('/ws/game/', '');

    return baseUrl;
  }

  /**
   * Initialize production mode with backend connected
   */
  private initializeProductionMode(): void {
    console.log('🚀 Initializing production mode with backend connection');

    this.messages = [
      {
        id: 'prod-welcome',
        type: 'system',
        content: '🎯 Welcome to Goali! I\'m your personal life coach. Tell me what\'s on your mind and I\'ll help you find the perfect activity.',
        timestamp: new Date()
      }
    ];

    this.wheelData = null;
    this.error = null;

    // Update chat interface connection status
    this.updateChatConnectionStatus('connected');

    console.log('✅ Production mode initialized');
  }

  /**
   * Initialize debug mode with backend connected
   */
  private initializeDebugMode(): void {
    console.log('🐛 Initializing debug mode with backend connection');
    const config = this.configService.getConfig();

    this.messages = [
      {
        id: 'debug-welcome',
        type: 'system',
        content: '🐛 Debug mode active. Backend connected. Use the debug panel (Ctrl+Shift+D) to configure settings, or start chatting to generate a wheel.',
        timestamp: new Date()
      }
    ];

    // Check if mock data is enabled in debug mode
    if (config.debug.mockDataEnabled) {
      console.log('🎭 Mock data enabled in debug mode - creating demo wheel');
      this.wheelData = {
        segments: [
          { id: '1', text: 'Go for a 20-minute walk', percentage: 15, color: '#FF6B6B' },
          { id: '2', text: 'Call a friend or family member', percentage: 12, color: '#4ECDC4' },
          { id: '3', text: 'Read 10 pages of a book', percentage: 18, color: '#45B7D1' },
          { id: '4', text: 'Do 5 minutes of meditation', percentage: 10, color: '#96CEB4' },
          { id: '5', text: 'Write in a journal', percentage: 8, color: '#FFEAA7' },
          { id: '6', text: 'Learn something new online', percentage: 15, color: '#DDA0DD' },
          { id: '7', text: 'Organize one small area', percentage: 12, color: '#98D8C8' },
          { id: '8', text: 'Practice a hobby or skill', percentage: 10, color: '#F7DC6F' }
        ],
        wheelId: 'debug-demo-wheel-001',
        createdAt: new Date().toISOString()
      };

      // Add demo message about wheel
      this.messages.push({
        id: 'debug-demo-wheel',
        type: 'ai',
        content: '🎡 Demo wheel loaded! This is a sample wheel with various activities. In debug mode with mock data enabled, you can test the wheel functionality.',
        timestamp: new Date()
      });
    } else {
      this.wheelData = null;
    }

    this.error = null;

    // Update chat interface connection status
    this.updateChatConnectionStatus('connected');

    console.log('✅ Debug mode initialized');
  }

  /**
   * Update chat interface connection status
   */
  private updateChatConnectionStatus(status: 'connected' | 'disconnected' | 'connecting'): void {
    const chatInterface = this.shadowRoot?.querySelector('chat-interface') as any;
    if (chatInterface) {
      chatInterface.connectionStatus = status;
      console.log(`🔄 Chat interface connection status updated to: ${status}`);
    } else {
      console.warn('⚠️ Chat interface not found for status update');
    }
  }

  /**
   * Initializes demo mode with fake data (fallback when backend unavailable)
   */
  private initializeDemoMode(): void {
    console.log('🎭 Initializing demo mode (backend unavailable)');

    // Note: Don't override wsConnected here - it might be set correctly by WebSocket manager
    // Only set to false if we're certain the backend is unavailable
    const config = this.configService.getConfig();

    // Only create demo wheel data if in production mode or if mock data is explicitly enabled
    if (this.configService.isProductionMode() || config.debug.mockDataEnabled) {
      this.wheelData = {
        segments: [
          { id: '1', text: 'Go for a 20-minute walk', percentage: 15, color: '#FF6B6B' },
          { id: '2', text: 'Call a friend or family member', percentage: 12, color: '#4ECDC4' },
          { id: '3', text: 'Read 10 pages of a book', percentage: 18, color: '#45B7D1' },
          { id: '4', text: 'Do 5 minutes of meditation', percentage: 10, color: '#96CEB4' },
          { id: '5', text: 'Write in a journal', percentage: 8, color: '#FFEAA7' },
          { id: '6', text: 'Learn something new online', percentage: 15, color: '#DDA0DD' },
          { id: '7', text: 'Organize one small area', percentage: 12, color: '#98D8C8' },
          { id: '8', text: 'Practice a hobby or skill', percentage: 10, color: '#F7DC6F' }
        ],
        wheelId: 'demo-wheel-001',
        createdAt: new Date().toISOString()
      };

      // Add demo messages
      this.messages = [
        {
          id: 'demo-1',
          type: 'system',
          content: '🎯 Welcome to Goali! This is demo mode - your personalized activity wheel is ready.',
          timestamp: new Date()
        },
        {
          id: 'demo-2',
          type: 'ai',
          content: 'Hi! I\'ve created a sample wheel with various activities to help you get started. Give it a spin to choose your next adventure! In the full version, I would create personalized activities based on our conversation.',
          timestamp: new Date()
        }
      ];
    } else {
      // Debug mode without mock data - start with clean slate
      this.wheelData = null;
      this.messages = [
        {
          id: 'debug-welcome',
          type: 'system',
          content: '🐛 Debug mode active. Use the debug panel (Ctrl+Shift+D) to configure settings, or start chatting to generate a wheel.',
          timestamp: new Date()
        }
      ];
    }

    this.error = null;
    console.log(`✅ Demo mode initialized (${this.configService.getMode()} mode, wheel: ${this.wheelData ? 'enabled' : 'disabled'})`);
  }

  /**
   * Sets up WebSocket message handlers
   */
  private setupMessageHandlers(): void {
    // Initialize debug filter
    this.debugFilter.setDebugMode(this.configService.isDebugMode());

    this.websocketManager.onMessage('wheel_data', (data) => {
      this.handleWheelGenerated(data);
    });

    this.websocketManager.onMessage('chat_message', (data) => {
      this.handleAIResponse(data);
    });

    this.websocketManager.onMessage('system_message', (data) => {
      this.handleSystemMessage(data);
    });

    this.websocketManager.onMessage('error', (data) => {
      this.handleError(data);
    });

    // Filter debug messages - only show in debug mode and don't add to chat
    this.websocketManager.onMessage('debug_info', (data) => {
      if (this.debugFilter.shouldShowDebugMessage(data)) {
        // FIXED: Properly extract debug message content based on actual backend structure
        let debugMessage = 'Unknown debug message';

        if (data.content && typeof data.content === 'object') {
          // Priority order: message > details.message > source+level > stringify
          if (data.content.message) {
            debugMessage = data.content.message;
          } else if (data.content.details?.message) {
            debugMessage = data.content.details.message;
          } else if (data.content.source && data.content.level) {
            // FIXED: Better formatting for source+level debug messages
            debugMessage = `[${data.content.source}] ${data.content.message || data.content.level}`;
          } else {
            // FIXED: Better fallback for complex debug objects
            debugMessage = JSON.stringify(data.content, null, 2);
          }
        } else if (data.content && typeof data.content === 'string') {
          debugMessage = data.content;
        } else if (data.message) {
          debugMessage = data.message;
        } else {
          // FIXED: Better fallback for unknown debug message structure
          debugMessage = JSON.stringify(data, null, 2);
        }

        // FIXED: Better handling of undefined values
        if (!debugMessage || debugMessage === 'undefined' || debugMessage.includes('undefined')) {
          const source = data.content?.source || 'unknown';
          const level = data.content?.level || 'info';
          const details = data.content?.details ? JSON.stringify(data.content.details) : 'no details';
          debugMessage = `[${source}:${level}] ${details}`;
        }

        console.log('🔧 Debug:', debugMessage);
      }
      // Don't add debug messages to chat to prevent spam
    });

    // Handle workflow status without adding to chat
    this.websocketManager.onMessage('workflow_status', (data) => {
      if (this.configService.isDebugMode()) {
        console.log('🔄 Workflow status:', data);
      }
      // Don't add workflow status to chat
    });

    // Handle processing status
    this.websocketManager.onMessage('processing_status', (data) => {
      this.isLoading = data.status === 'processing';
      if (this.configService.isDebugMode()) {
        console.log('⚙️ Processing status:', data.status);
      }
      // Don't add processing status to chat
    });

    // Handle progress updates directly via WebSocket (backup handler)
    this.websocketManager.onMessage('progress_update', (data) => {
      if (this.configService.isDebugMode()) {
        console.log('📊 WebSocket progress update received:', data);
      }

      // Show progress bar if not already visible
      if (!this.showProgressBar && data.data) {
        this.showProgressBar = true;
        this.currentProgressTrackerId = data.data.tracker_id;
        this.progressTitle = data.data.workflow_type === 'wheel_generation' ? 'Generating Wheel...' : 'Processing...';

        // Set progress modal mode based on user type
        this.progressModalMode = this.currentUser?.isStaff ? 'debug' : 'user-friendly';
      }

      // Forward to progress bar component
      const progressBar = this.shadowRoot?.querySelector('real-time-progress-bar');
      if (progressBar) {
        progressBar.dispatchEvent(new CustomEvent('progress-update', { detail: data }));
      }

      // Hide progress bar when completed and trigger wheel refresh
      if (data.data?.stage === 'completed' && data.data?.progress_percent >= 100) {
        // Wait for wheel data to be populated before dismissing progress bar
        this.waitForWheelPopulationAndDismiss();
      }
    });

    // Handle performance metrics
    this.websocketManager.onMessage('performance_metrics', (data) => {
      this.handlePerformanceMetrics(data);
    });

    // Handle workflow progress
    this.websocketManager.onMessage('workflow_progress', (data) => {
      this.handleWorkflowProgress(data);
    });

    this.websocketManager.onConnectionChange((connected) => {
      this.wsConnected = connected;
      this.connectionState = connected ? 'connected' : 'disconnected';
      console.log(`🔄 Connection state updated: ${this.connectionState}`);
    });
  }

  /**
   * Handles wheel generation from backend
   */
  private handleWheelGenerated(data: any): void {
    try {
      console.log('🎡 ===== WHEEL DATA RECEIVED DEBUG =====');
      console.log('🎡 Full wheel data:', JSON.stringify(data, null, 2));

      if (data.wheel && data.wheel.items && Array.isArray(data.wheel.items)) {
        console.log('🎡 Wheel items array:');
        data.wheel.items.forEach((item: any, index: number) => {
          console.log(`   Item ${index + 1}:`);
          console.log(`     - item.id: ${item.id}`);
          console.log(`     - item.activity_tailored_id: ${item.activity_tailored_id}`);
          console.log(`     - item.name: ${item.name}`);
          console.log(`     - item.title: ${item.title}`);
          console.log(`     - Full item:`, JSON.stringify(item, null, 2));
        });
        console.log('🎡 ===== END WHEEL DATA DEBUG =====');
        // Convert backend wheel format to frontend format
        this.wheelData = {
          segments: data.wheel.items.map((item: any) => ({
            id: item.id, // This is the wheel item ID (e.g., "item_123")
            // FIXED: Prioritize item.title over item.name for proper activity names
            text: item.title || item.name || 'Activity',
            name: item.name || item.title || 'Activity', // For compatibility
            percentage: item.percentage,
            color: item.color,
            // FIXED: Add description from backend data
            description: item.description || '',
            // Store the activity tailored ID separately for reference
            activity_tailored_id: item.activity_tailored_id,
            // Store additional metadata
            domain: item.domain,
            base_challenge_rating: item.base_challenge_rating,
            // Ensure we have the wheel item ID for removal operations
            wheel_item_id: item.id
          })),
          wheelId: `wheel-${Date.now()}`,
          createdAt: new Date().toISOString()
        };

        console.log('✅ Wheel data processed successfully:', this.wheelData);

        // Add system message about wheel generation
        const message: ChatMessage = {
          id: `msg-${Date.now()}`,
          type: 'system',
          content: 'Your personalized activity wheel has been generated! Give it a spin to choose your next adventure.',
          timestamp: new Date()
        };
        this.messages = [...this.messages, message];

        // Force re-render to ensure wheel component updates
        this.requestUpdate();
      } else {
        console.error('❌ Invalid wheel data structure:', data);
        this.handleError({ content: 'Invalid wheel data received from server' });
      }
    } catch (error) {
      console.error('❌ Error processing wheel data:', error);
      this.handleError({ content: 'Failed to process wheel data' });
    }
  }

  /**
   * Handles system messages from backend
   */
  private handleSystemMessage(data: any): void {
    const message: ChatMessage = {
      id: `msg-${Date.now()}`,
      type: 'system',
      content: data.content || 'System message',
      timestamp: new Date()
    };
    this.messages = [...this.messages, message];

    // Force re-render to ensure chat interface updates
    this.requestUpdate();
  }

  /**
   * Handles AI response messages
   */
  private handleAIResponse(data: any): void {
    // Only handle AI messages (not user echoes)
    if (data.is_user) {
      return; // Skip user message echoes
    }

    const message: ChatMessage = {
      id: `msg-${Date.now()}`,
      type: 'ai',
      content: data.content || 'No content',
      timestamp: new Date(),
      metadata: {
        processingTime: data.processingTime
      }
    };

    // CRITICAL FIX: Create new array to trigger Lit reactivity
    this.messages = [...this.messages, message];

    // Force re-render to ensure chat interface updates
    this.requestUpdate();
  }

  /**
   * Handles error messages
   */
  private handleError(data: any): void {
    this.error = data.content || data.message || 'An error occurred';

    const message: ChatMessage = {
      id: `msg-${Date.now()}`,
      type: 'error',
      content: data.content || data.message || 'An error occurred',
      timestamp: new Date(),
      metadata: {
        error: {
          code: data.code || 'UNKNOWN',
          details: data.details || ''
        }
      }
    };
    this.messages = [...this.messages, message];
  }

  /**
   * Close progress modal
   */
  private closeProgressModal = () => {
    this.showProgressBar = false;
    this.currentProgressTrackerId = '';
    // Clear any fallback timers
    if (this.progressFallbackTimer) {
      clearTimeout(this.progressFallbackTimer);
      this.progressFallbackTimer = null;
    }
  };

  private progressFallbackTimer: NodeJS.Timeout | null = null;
  private fallbackProgress = 0;

  /**
   * Start fallback progress simulation if backend doesn't send real-time updates
   */
  private startProgressFallback = (trackerId: string) => {
    this.fallbackProgress = 0;
    let currentStageIndex = 0;

    // Define realistic wheel generation stages with detailed messages
    const wheelGenerationStages = [
      {
        stage_id: 'initialization',
        stage_name: 'Initializing Workflow',
        message: 'Setting up your personalized wheel generation...',
        targetProgress: 15
      },
      {
        stage_id: 'context_analysis',
        stage_name: 'Analyzing Your Profile',
        message: 'Understanding your preferences, goals, and current context...',
        targetProgress: 30
      },
      {
        stage_id: 'resource_assessment',
        stage_name: 'Evaluating Resources',
        message: 'Assessing your available time, energy, and environment...',
        targetProgress: 45
      },
      {
        stage_id: 'activity_generation',
        stage_name: 'Generating Activities',
        message: 'Creating personalized activities tailored to your needs...',
        targetProgress: 70
      },
      {
        stage_id: 'activity_tailoring',
        stage_name: 'Personalizing Activities',
        message: 'Customizing activities based on your unique profile...',
        targetProgress: 85
      },
      {
        stage_id: 'wheel_assembly',
        stage_name: 'Assembling Your Wheel',
        message: 'Finalizing your personalized activity wheel...',
        targetProgress: 95
      }
    ];

    const updateFallbackProgress = () => {
      if (!this.showProgressBar || this.currentProgressTrackerId !== trackerId) {
        return; // Stop if progress bar was closed or tracker changed
      }

      const currentStage = wheelGenerationStages[currentStageIndex];
      if (!currentStage) return;

      // Gradually progress towards the current stage target
      const increment = Math.random() * 8 + 2; // 2-10% increments
      this.fallbackProgress = Math.min(this.fallbackProgress + increment, currentStage.targetProgress);

      const progressBar = this.shadowRoot?.querySelector('real-time-progress-bar');
      if (progressBar) {
        progressBar.dispatchEvent(new CustomEvent('progress-update', {
          detail: {
            data: {
              tracker_id: trackerId,
              progress_percent: this.fallbackProgress,
              stage: 'processing',
              stage_id: currentStage.stage_id,
              stage_name: currentStage.stage_name,
              message: currentStage.message,
              workflow_type: 'wheel_generation',
              timestamp: new Date().toISOString(),
              priority: 'normal'
            }
          }
        }));
      }

      // Move to next stage when current target is reached
      if (this.fallbackProgress >= currentStage.targetProgress && currentStageIndex < wheelGenerationStages.length - 1) {
        currentStageIndex++;
      }

      // Continue updating if not at final stage
      if (currentStageIndex < wheelGenerationStages.length - 1 || this.fallbackProgress < 95) {
        this.progressFallbackTimer = setTimeout(updateFallbackProgress, 1500 + Math.random() * 2500); // 1.5-4 second intervals
      }
    };

    // Start the fallback after 3 seconds if no real updates received
    this.progressFallbackTimer = setTimeout(updateFallbackProgress, 3000);
  };

  /**
   * Wait for wheel data to be populated before dismissing progress bar
   */
  private waitForWheelPopulationAndDismiss = () => {
    const checkWheelPopulation = () => {
      // Check if wheel data is available and has items
      const hasWheelData = this.wheelData && this.wheelData.segments && this.wheelData.segments.length > 0;

      if (hasWheelData) {
        // Wheel is populated, start progressive dismissal
        this.progressivelyDismissProgressBar();
      } else {
        // Keep checking every 500ms for up to 10 seconds
        const elapsed = Date.now() - this.progressDismissalStartTime;
        if (elapsed < 10000) {
          setTimeout(checkWheelPopulation, 500);
        } else {
          // Timeout reached, dismiss anyway
          console.warn('⚠️ Wheel population timeout reached, dismissing progress bar');
          this.progressivelyDismissProgressBar();
        }
      }
    };

    this.progressDismissalStartTime = Date.now();
    checkWheelPopulation();
  };

  private progressDismissalStartTime = 0;

  /**
   * Progressively dismiss the progress bar with fade effect
   */
  private progressivelyDismissProgressBar = () => {
    const progressModal = this.shadowRoot?.querySelector('.progress-modal') as HTMLElement;

    if (progressModal) {
      // Add fade-out class for smooth transition
      progressModal.style.transition = 'opacity 1s ease-out';
      progressModal.style.opacity = '0';

      // Wait for fade animation to complete before hiding
      setTimeout(() => {
        this.showProgressBar = false;
        this.currentProgressTrackerId = '';

        // Clear any fallback timers
        if (this.progressFallbackTimer) {
          clearTimeout(this.progressFallbackTimer);
          this.progressFallbackTimer = null;
        }

        // Force wheel refresh after progress completion
        this.requestUpdate();

        // Also trigger a wheel component update if it exists
        const wheelComponent = this.shadowRoot?.querySelector('game-wheel') as any;
        if (wheelComponent && typeof wheelComponent.requestUpdate === 'function') {
          wheelComponent.requestUpdate();
        }
      }, 1000); // Wait for fade animation
    } else {
      // Fallback if modal not found
      this.showProgressBar = false;
      this.currentProgressTrackerId = '';
      this.requestUpdate();
    }
  };

  /**
   * Handles progress update messages
   */
  private handleProgressUpdate = (event: Event): void => {
    const customEvent = event as CustomEvent;
    const data = customEvent.detail;

    if (this.configService.isDebugMode()) {
      console.log('📊 Progress update:', data);
    }

    // Show progress bar if not already visible
    if (!this.showProgressBar && data.data) {
      this.showProgressBar = true;
      this.currentProgressTrackerId = data.data.tracker_id;
      this.progressTitle = data.data.workflow_type === 'wheel_generation' ? 'Generating Wheel...' : 'Processing...';

      // Set progress modal mode based on user type
      this.progressModalMode = this.currentUser?.isStaff ? 'debug' : 'user-friendly';
    }

    // Forward to progress bar component
    const progressBar = this.shadowRoot?.querySelector('real-time-progress-bar');
    if (progressBar) {
      progressBar.dispatchEvent(new CustomEvent('progress-update', { detail: data }));
    }

    // Hide progress bar when completed and ensure wheel refresh
    if (data.data?.stage === 'completed' && data.data?.progress_percent >= 100) {
      setTimeout(() => {
        this.showProgressBar = false;
        this.currentProgressTrackerId = '';

        // Force a complete re-render to ensure wheel data is displayed
        this.requestUpdate();

        // Also trigger a wheel component update if it exists
        const wheelComponent = this.shadowRoot?.querySelector('game-wheel') as any;
        if (wheelComponent && typeof wheelComponent.requestUpdate === 'function') {
          wheelComponent.requestUpdate();
        }
      }, 2000); // Keep visible for 2 seconds after completion
    }
  }

  /**
   * Handles performance metrics messages
   */
  private handlePerformanceMetrics(data: any): void {
    if (this.configService.isDebugMode()) {
      console.log('⚡ Performance metrics:', data);
    }

    // Forward to progress bar component
    const progressBar = this.shadowRoot?.querySelector('real-time-progress-bar');
    if (progressBar) {
      progressBar.dispatchEvent(new CustomEvent('performance-metrics', { detail: data }));
    }
  }

  /**
   * Handles workflow progress messages
   */
  private handleWorkflowProgress(data: any): void {
    if (this.configService.isDebugMode()) {
      console.log('🔄 Workflow progress:', data);
    }

    // Show progress bar if not already visible
    if (!this.showProgressBar && data.data) {
      this.showProgressBar = true;
      this.progressTitle = `${data.data.workflow_type?.replace('_', ' ').toUpperCase() || 'Processing'}...`;
    }

    // Forward to progress bar component
    const progressBar = this.shadowRoot?.querySelector('real-time-progress-bar');
    if (progressBar) {
      progressBar.dispatchEvent(new CustomEvent('workflow-progress', { detail: data }));
    }

    // Hide progress bar when workflow is complete
    if (data.data?.overall_progress >= 100) {
      setTimeout(() => {
        this.showProgressBar = false;
      }, 2000);
    }
  }

  /**
   * Load debug selections from localStorage
   */
  private loadDebugSelections(): void {
    if (this.configService.isDebugMode()) {
      try {
        this.debugUserId = localStorage.getItem('debug_selected_user_id') || '';
        this.debugLLMConfigId = localStorage.getItem('debug_selected_llm_config_id') || '';
      } catch (error) {
        console.warn('Failed to load debug selections:', error);
      }
    }
  }

  /**
   * Check for active enjoy overlay from previous session
   */
  private checkForActiveEnjoyOverlay(): void {
    try {
      const lastSpinData = localStorage.getItem('goali_last_wheel_spin');
      if (lastSpinData) {
        const spinData = JSON.parse(lastSpinData);
        const now = Date.now();
        const timeSinceSpinMs = now - spinData.timestamp;
        const tenMinutesMs = 10 * 60 * 1000; // 10 minutes in milliseconds

        // If less than 10 minutes have passed, show enjoy overlay
        if (timeSinceSpinMs < tenMinutesMs) {
          console.log('🎯 Restoring enjoy overlay from previous session');
          this.enjoyOverlayActivity = spinData.activity;
          this.enjoyOverlayStartTime = spinData.timestamp;
          this.showEnjoyOverlay = true;

          // Set up timer to transition to feedback modal after remaining time
          const remainingTime = tenMinutesMs - timeSinceSpinMs;
          setTimeout(() => {
            this.transitionToFeedbackModal();
          }, remainingTime);
        } else {
          // More than 10 minutes have passed, show feedback modal directly
          console.log('🎯 Showing post-activity feedback modal from previous session');
          this.showPostActivityFeedbackWithActivity(spinData.activity);
        }
      }
    } catch (error) {
      console.warn('Failed to check for active enjoy overlay:', error);
      localStorage.removeItem('goali_last_wheel_spin');
    }
  }

  /**
   * Show enjoy overlay with activity details
   */
  private showEnjoyOverlayWithActivity(activity: any): void {
    const timestamp = Date.now();

    // Store in localStorage for persistence across page reloads
    const spinData = {
      timestamp,
      activity: {
        id: activity.id,
        name: activity.name,
        description: activity.description,
        domain: activity.domain,
        type: activity.type,
        icon: activity.icon,
        base_challenge_rating: activity.base_challenge_rating,
        duration_range: activity.duration_range,
        instructions: activity.instructions,
        requirements: activity.requirements,
        materials: activity.materials
      }
    };

    try {
      localStorage.setItem('goali_last_wheel_spin', JSON.stringify(spinData));
    } catch (error) {
      console.warn('Failed to store wheel spin data:', error);
    }

    // Set component state
    this.enjoyOverlayActivity = activity;
    this.enjoyOverlayStartTime = timestamp;
    this.showEnjoyOverlay = true;

    // Auto-transition to feedback modal after 10 minutes
    setTimeout(() => {
      this.transitionToFeedbackModal();
    }, 10 * 60 * 1000);
  }

  /**
   * Transition from enjoy overlay to feedback modal
   */
  private transitionToFeedbackModal(): void {
    if (this.showEnjoyOverlay && this.enjoyOverlayActivity) {
      this.showEnjoyOverlay = false;
      this.showPostActivityFeedbackWithActivity(this.enjoyOverlayActivity);
    }
  }

  /**
   * Show post-activity feedback modal with activity details
   */
  private showPostActivityFeedbackWithActivity(activity: any): void {
    const timestamp = Date.now();

    // Store in localStorage for persistence across page reloads
    const spinData = {
      timestamp,
      activity: {
        id: activity.id,
        name: activity.name,
        description: activity.description,
        domain: activity.domain,
        type: activity.type,
        icon: activity.icon,
        base_challenge_rating: activity.base_challenge_rating,
        duration_range: activity.duration_range,
        instructions: activity.instructions,
        requirements: activity.requirements,
        materials: activity.materials
      }
    };

    try {
      localStorage.setItem('goali_last_wheel_spin', JSON.stringify(spinData));
    } catch (error) {
      console.warn('Failed to store wheel spin data:', error);
    }

    // Configure post-activity feedback modal
    this.postActivityFeedbackConfig = {
      title: "How was your experience?",
      message: `Tell us about your experience with "${activity.name}". Your feedback helps us improve the system and provide better activity recommendations.`,
      feedback_type: "post_activity_feedback",
      content_type: "WheelItem",
      object_id: activity.id,
      activity: activity
    };

    this.showPostActivityFeedbackModal = true;
  }

  /**
   * Close post-activity feedback modal
   */
  private closePostActivityFeedbackModal = () => {
    this.showPostActivityFeedbackModal = false;

    // Clean up localStorage
    try {
      localStorage.removeItem('goali_last_wheel_spin');
    } catch (error) {
      console.warn('Failed to clean up wheel spin data:', error);
    }
  };

  /**
   * Submit post-activity feedback
   */
  private submitPostActivityFeedback = async () => {
    try {
      const activity = this.postActivityFeedbackConfig.activity;
      const feedbackTextarea = document.getElementById('post-activity-feedback-comment') as HTMLTextAreaElement;
      const experienceRating = document.querySelector('input[name="experience-rating"]:checked') as HTMLInputElement;
      const difficultyRating = document.querySelector('input[name="difficulty-rating"]:checked') as HTMLInputElement;
      const enjoymentRating = document.querySelector('input[name="enjoyment-rating"]:checked') as HTMLInputElement;
      const recommendationRating = document.querySelector('input[name="recommendation-rating"]:checked') as HTMLInputElement;

      // Collect business metrics
      const contextData = {
        activity_id: activity.id,
        activity_name: activity.name,
        activity_domain: activity.domain,
        activity_type: activity.type,
        activity_challenge_rating: activity.base_challenge_rating,
        activity_duration_range: activity.duration_range,
        experience_rating: experienceRating ? parseInt(experienceRating.value) : null,
        difficulty_rating: difficultyRating ? parseInt(difficultyRating.value) : null,
        enjoyment_rating: enjoymentRating ? parseInt(enjoymentRating.value) : null,
        recommendation_rating: recommendationRating ? parseInt(recommendationRating.value) : null,
        feedback_timestamp: new Date().toISOString(),
        session_duration_minutes: Math.round((Date.now() - JSON.parse(localStorage.getItem('goali_last_wheel_spin') || '{}').timestamp) / (1000 * 60))
      };

      const response = await fetch(`${this.getBackendBaseUrl()}/api/feedback/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authService.getToken()}`
        },
        body: JSON.stringify({
          feedback_type: this.postActivityFeedbackConfig.feedback_type,
          content_type: this.postActivityFeedbackConfig.content_type,
          object_id: this.postActivityFeedbackConfig.object_id,
          user_comment: feedbackTextarea?.value || '',
          criticality: 1,
          context_data: contextData,
          // Link to the specific wheel item that was won
          wheel_item_id: activity.wheel_item_id || activity.id
        })
      });

      if (response.ok) {
        console.log('✅ Post-activity feedback submitted successfully');
        this.closePostActivityFeedbackModal();

        // Reset app state for new session
        this.resetAppAfterFeedback();
      } else {
        console.error('❌ Failed to submit post-activity feedback');
        this.error = 'Failed to submit feedback. Please try again.';
      }
    } catch (error) {
      console.error('❌ Error submitting post-activity feedback:', error);
      this.error = 'Failed to submit feedback. Please try again.';
    }
  };

  /**
   * Hide enjoy overlay and clean up
   */
  private hideEnjoyOverlay(): void {
    this.showEnjoyOverlay = false;
    this.enjoyOverlayActivity = null;
    this.enjoyOverlayStartTime = null;

    // Clean up localStorage
    try {
      localStorage.removeItem('goali_last_wheel_spin');
    } catch (error) {
      console.warn('Failed to clean up wheel spin data:', error);
    }
  }

  /**
   * Reset app entirely when overlay is closed manually
   */
  private resetAppFromEnjoyOverlay(): void {
    this.hideEnjoyOverlay();

    // Reset all app state
    this.wheelData = null;
    this.messages = [];
    this.error = null;
    this.isLoading = false;
    this.showActivityModal = false;
    this.showCreateActivityModal = false;
    this.showProfileModal = false;
    this.showWinningModal = false;
    this.showFeedbackModal = false;
    this.showAddActivityModal = false;
    this.showContractModal = false;
    this.showPostActivityFeedbackModal = false;
    this.winningActivity = null;

    // Force re-render
    this.requestUpdate();

    console.log('🔄 App reset from enjoy overlay');
  }

  /**
   * Reset app state after feedback submission
   */
  private resetAppAfterFeedback(): void {
    // Reset all app state
    this.wheelData = null;
    this.messages = [];
    this.error = null;
    this.isLoading = false;
    this.showActivityModal = false;
    this.showCreateActivityModal = false;
    this.showProfileModal = false;
    this.showWinningModal = false;
    this.showFeedbackModal = false;
    this.showAddActivityModal = false;
    this.showContractModal = false;
    this.showEnjoyOverlay = false;
    this.showPostActivityFeedbackModal = false;
    this.winningActivity = null;
    this.enjoyOverlayActivity = null;
    this.enjoyOverlayStartTime = null;

    // Force re-render
    this.requestUpdate();

    console.log('🔄 App reset after post-activity feedback');
  }

  /**
   * Load current user information
   */
  private loadCurrentUser(): void {
    const previousUserId = this.currentUser?.id;

    if (this.configService.isDebugMode() && this.debugUserId) {
      // In debug mode, use debug user selection
      this.currentUser = {
        id: this.debugUserId,
        name: `Debug User ${this.debugUserId}`,
        isStaff: true
      };
    } else {
      // Use authenticated user
      const authUser = this.authService.getCurrentUser();
      if (authUser) {
        this.currentUser = {
          id: authUser.id,
          name: authUser.name || authUser.username || `User ${authUser.id}`,
          isStaff: authUser.is_staff || false
        };
      } else {
        // No authenticated user
        this.currentUser = null;
      }
    }

    // Invalidate activity cache if user changed
    if (previousUserId !== this.currentUser?.id) {
      this.invalidateActivityCache();
    }
  }

  /**
   * Get button text based on connection state
   */
  private getButtonText(): string {
    if (this.isLoading) {
      return 'Generating...';
    }

    switch (this.connectionState) {
      case 'disconnected':
        return 'Connecting...';
      case 'connecting':
        return 'Connecting...';
      case 'connected':
        return 'Generate';
      default:
        return 'Generate';
    }
  }

  /**
   * Get connection status text
   */
  private getConnectionStatusText(): string {
    switch (this.connectionState) {
      case 'disconnected':
        return 'Disconnected';
      case 'connecting':
        return 'Connecting...';
      case 'connected':
        return 'Connected';
      default:
        return 'Unknown';
    }
  }

  /**
   * Open user profile management
   */
  private openUserProfile(): void {
    // Show mobile-friendly profile modal instead of opening admin page
    this.showProfileModal = true;
    this.loadCurrentUserProfile();
  }

  /**
   * Load current user profile data
   */
  private async loadCurrentUserProfile(): Promise<void> {
    try {
      console.log('🔄 Loading user profile data from API...');

      const config = this.configService.getConfig();
      const baseUrl = config.websocket.url.replace('ws://', 'http://').replace('wss://', 'https://').replace('/ws/game/', '');

      // SECURITY FIX: Use authenticated user's ID, not hardcoded fallback
      const currentUser = this.authService.getCurrentUser();
      if (!currentUser?.id) {
        console.error('❌ No authenticated user found - cannot load profile');
        throw new Error('User not authenticated');
      }

      const userId = this.debugUserId || currentUser.id;
      const apiUrl = `${baseUrl}/api/user/profile/${userId}/`;

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(this.authService.getToken() ? {
            'Authorization': `Bearer ${this.authService.getToken()}`
          } : {})
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.profile) {
          this.currentUserProfile = {
            id: data.profile.id,
            name: data.profile.name,
            description: data.profile.description,
            demographics: data.profile.demographics,
            environment: data.profile.environment,
            environments: data.profile.environments,
            preferences: data.profile.preferences,
            goals: data.profile.goals,
            is_real: data.profile.is_real
          };
          console.log('✅ User profile loaded successfully:', this.currentUserProfile);
        } else {
          throw new Error(data.error || 'Failed to load profile data');
        }
      } else {
        console.warn('⚠️ Profile API failed, using fallback data');
        // SECURITY FIX: Use authenticated user's data in fallback, not hardcoded PhiPhi
        const currentUser = this.authService.getCurrentUser();
        this.currentUserProfile = {
          id: userId,
          name: currentUser?.name || 'Unknown User',
          description: 'Profile data not available',
          demographics: {
            age: 'Not set',
            location: 'Not set',
            occupation: 'Not set'
          },
          environment: {
            living_situation: 'Not set',
            available_resources: 'Not set',
            constraints: 'Not set'
          },
          goals: {
            short_term: 'Not set',
            long_term: 'Not set',
            motivation: 'Not set'
          }
        };
      }
    } catch (error) {
      console.error('❌ Failed to load user profile:', error);
      // SECURITY FIX: Use authenticated user's data in error fallback, not hardcoded values
      const currentUser = this.authService.getCurrentUser();
      if (!currentUser?.id) {
        // If no authenticated user, throw error instead of using fallback data
        throw new Error('Cannot load profile: User not authenticated');
      }

      this.currentUserProfile = {
        id: this.debugUserId || currentUser.id,
        name: currentUser.name || 'Unknown User',
        description: 'Profile data not available',
        demographics: {
          age: 'Not set',
          location: 'Not set',
          occupation: 'Not set'
        },
        environment: {
          living_situation: 'Not set',
          available_resources: 'Not set',
          constraints: 'Not set'
        },
        goals: {
          short_term: 'Not set',
          long_term: 'Not set',
          motivation: 'Not set'
        }
      };
    }
  }

  /**
   * Handles message sending from chat interface
   */
  private async handleMessageSend(event: CustomEvent): Promise<void> {
    const { message } = event.detail;

    // Add user message to chat
    this.messages = [...this.messages, message];

    if (this.wsConnected) {
      // SECURITY FIX: Get current user ID without hardcoded fallback
      let userId: string;
      if (this.configService.isDebugMode() && this.debugUserId) {
        userId = this.debugUserId;
      } else {
        const currentUser = this.authService.getCurrentUser();
        if (!currentUser?.id) {
          console.error('❌ No authenticated user found - cannot send message');
          return;
        }
        userId = currentUser.id;
      }

      console.log('Sending message with debug selections:', { userId, llmConfigId: this.debugLLMConfigId });

      // CRITICAL FIX: Use message handler's sendChatMessage method to include conversation state
      try {
        await this.messageHandler.sendChatMessage(
          message.content,
          userId,
          undefined, // workflowType
          this.configService.isDebugMode() ? this.debugLLMConfigId : undefined // llmConfigId
        );
      } catch (error) {
        console.error('Failed to send message:', error);
        // Fallback to direct WebSocket send if message handler fails
        this.websocketManager.sendMessage('chat_message', {
          message: message.content,
          user_profile_id: userId,
          timestamp: message.timestamp.toISOString(),
          metadata: this.configService.isDebugMode() && this.debugLLMConfigId ? {
            llm_config_id: this.debugLLMConfigId
          } : {}
        });
      }
    } else {
      // Demo mode: simulate AI response
      this.simulateAIResponse(message.content);
    }
  }

  /**
   * Simulates AI response in demo mode
   */
  private simulateAIResponse(userMessage: string): void {
    setTimeout(() => {
      const responses = [
        "That's interesting! In the full version, I would analyze your message and provide personalized guidance. For now, try spinning the wheel to explore the sample activities!",
        "I understand! While this is demo mode, you can still experience the core wheel functionality. Each activity is designed to be quick and rewarding.",
        "Great question! In the complete version, I would create custom activities based on your interests, goals, and current situation. The wheel you see contains some popular starter activities.",
        "Thanks for sharing! The wheel spinning experience you're about to try is the same as in the full version - realistic physics and engaging interactions await!"
      ];

      const randomResponse = responses[Math.floor(Math.random() * responses.length)];

      const aiMessage = {
        id: `demo-ai-${Date.now()}`,
        type: 'ai' as const,
        content: randomResponse,
        timestamp: new Date(),
        metadata: {
          processingTime: 800 + Math.random() * 400 // Simulate realistic processing time
        }
      };

      this.messages = [...this.messages, aiMessage];
    }, 1000 + Math.random() * 1000); // Simulate realistic response delay
  }

  /**
   * Handles wheel spin completion
   */
  private handleWheelSpinComplete(event: CustomEvent): void {
    console.log('🎯 [APP-SHELL] handleWheelSpinComplete called!', event.detail);
    const { winningSegment, finalAngle, duration } = event.detail;

    if (winningSegment) {
      console.log('🎯 [APP-SHELL] Processing winning segment:', winningSegment);

      // ARCHITECTURAL FIX: Robust data merging for complete activity information
      const originalActivity = this.wheelData?.segments?.find(segment =>
        segment.id === winningSegment.activityId ||
        segment.id === winningSegment.id ||
        segment.activity_tailored_id === winningSegment.activityId ||
        segment.activity_tailored_id === winningSegment.id
      );

      console.log('🎯 [APP-SHELL] Found original activity:', originalActivity);

      // Create comprehensive winning activity data
      this.winningActivity = {
        // Base data from winning segment
        id: winningSegment.id || winningSegment.activityId,
        name: winningSegment.text || winningSegment.name,
        color: winningSegment.color,
        percentage: winningSegment.percentage,

        // Enhanced data from original activity (using any type for flexible property access)
        description: (originalActivity as any)?.description || (originalActivity as any)?.tailored_description || 'No description available',
        instructions: (originalActivity as any)?.instructions || (originalActivity as any)?.tailored_instructions || '',
        domain: originalActivity?.domain || 'general',
        base_challenge_rating: originalActivity?.base_challenge_rating || (originalActivity as any)?.challenge_rating || 50,
        duration_range: (originalActivity as any)?.duration_range || (originalActivity as any)?.estimated_duration || 'Flexible',

        // Activity type and metadata
        type: (originalActivity as any)?.type || (originalActivity?.activity_tailored_id ? 'tailored' : 'generic'),
        activity_tailored_id: originalActivity?.activity_tailored_id || winningSegment.activityId,

        // Wheel item ID for feedback linking
        wheel_item_id: winningSegment.id || winningSegment.activityId,

        // Spin metadata
        spinDuration: duration,
        finalAngle: finalAngle,

        // Additional fields that might be available
        requirements: (originalActivity as any)?.requirements || (originalActivity as any)?.social_requirements,
        materials: (originalActivity as any)?.materials,
        icon: (originalActivity as any)?.icon || ((originalActivity as any)?.type === 'tailored' ? '⭐' : '🎯')
      };

      console.log('🎯 [APP-SHELL] Complete winning activity data:', this.winningActivity);

      // Show winning modal with 2-second delay for better UX
      setTimeout(() => {
        this.showWinningModal = true;
        this.requestUpdate();
      }, 2000);

      // Add wheel result message
      const message: ChatMessage = {
        id: `msg-${Date.now()}`,
        type: 'wheel-result',
        content: `🎯 You spun: ${winningSegment.text}`,
        timestamp: new Date(),
        metadata: {
          wheelResult: {
            segmentId: winningSegment.id,
            segmentText: winningSegment.text,
            spinDuration: duration
          }
        }
      };
      this.messages = [...this.messages, message];

      // Send result to backend (if connected)
      if (this.wsConnected) {
        // Find the original activity data from wheelData to get proper description
        const originalActivity = this.wheelData?.segments?.find(segment =>
          segment.id === winningSegment.activityId
        );

        console.log('🎯 [APP-SHELL] Sending spin_result to backend:', {
          activity_tailored_id: winningSegment.activityId || winningSegment.id,
          name: winningSegment.text,
          description: originalActivity?.description || winningSegment.text,
          user_profile_id: '2'
        });

        // SECURITY FIX: Use authenticated user's ID, not hardcoded value
        const currentUser = this.authService.getCurrentUser();
        if (currentUser?.id) {
          this.websocketManager.sendMessage('spin_result', {
            activity_tailored_id: winningSegment.activityId || winningSegment.id,
            name: winningSegment.text,
            description: originalActivity?.description || winningSegment.text,
            user_profile_id: currentUser.id
          });
        } else {
          console.error('❌ Cannot send spin result: No authenticated user');
        }
      } else {
        // Demo mode: simulate follow-up message
        setTimeout(() => {
          const followUpMessage = {
            id: `demo-followup-${Date.now()}`,
            type: 'ai' as const,
            content: `Great choice! "${winningSegment.text}" is a wonderful activity. In the full version, I would provide personalized tips and track your progress. For now, enjoy exploring this activity!`,
            timestamp: new Date()
          };
          this.messages = [...this.messages, followUpMessage];
        }, 2000);
      }
    }
  }

  /**
   * Handle login success
   */
  private handleLoginSuccess(): void {
    this.isAuthenticated = true;
    this.initializeApp();
  }

  /**
   * Handle demo mode request
   */
  private handleDemoModeRequest(): void {
    // Switch to demo mode temporarily
    this.isAuthenticated = true;
    this.initializeDemoMode();
  }

  /**
   * Handle logout
   */
  private async handleLogout(): Promise<void> {
    try {
      // Immediately clear authentication state to prevent any brief login modal flash
      this.isAuthenticated = false;
      this.currentUser = null;

      // Clear all application state
      this.wsConnected = false;
      this.connectionState = 'disconnected';
      this.wheelData = null;
      this.messages = [];
      this.error = null;
      this.showDebugPanel = false;
      this.showActivityModal = false;
      this.showCreateActivityModal = false;
      this.showProfileModal = false;
      this.showWinningModal = false;

      // Disconnect WebSocket
      this.websocketManager.disconnect();

      // Force immediate re-render to show logged out state
      this.requestUpdate();

      // Now perform the actual logout
      await this.authService.logout();

      // Force a complete page reload to ensure clean state
      // This prevents any debug mode authentication bypass from interfering
      setTimeout(() => {
        window.location.reload();
      }, 50); // Reduced timeout to minimize flash

      console.log('✅ User logged out successfully');
    } catch (error) {
      console.error('❌ Logout failed:', error);
      // Force logout even if server request fails
      this.isAuthenticated = false;
      this.currentUser = null;

      // Force page reload on error too
      setTimeout(() => {
        window.location.reload();
      }, 50);
    }
  }

  /**
   * Clears error state
   */
  private handleClearError(): void {
    this.error = null;
  }

  private handleTimeAvailableChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    this.timeAvailable = parseInt(target.value);
    // TODO: Send update to backend
  };

  /**
   * Convert time available percentage to minutes (10min to 4h range)
   */
  private getTimeInMinutes(): number {
    return Math.round(10 + (this.timeAvailable / 100) * (240 - 10)); // 10min to 240min (4h)
  }

  /**
   * Format time in minutes to human readable format
   */
  private formatTimeAvailable(): string {
    const minutes = this.getTimeInMinutes();
    if (minutes < 60) {
      return `${minutes}min`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      if (remainingMinutes === 0) {
        return `${hours}h`;
      } else {
        return `${hours}h ${remainingMinutes}min`;
      }
    }
  }

  private handleEnergyLevelChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    this.energyLevel = parseInt(target.value);
    // TODO: Send update to backend
  };

  /**
   * Handle generate wheel button click
   */
  private handleGenerateWheel = async () => {
    if (!this.wsConnected || this.isLoading) {
      return;
    }

    try {
      this.isLoading = true;
      this.error = null;

      // Show progress bar immediately when generate button is clicked
      this.showProgressBar = true;
      this.progressTitle = 'Generating Wheel...';
      this.progressModalMode = this.currentUser?.isStaff ? 'debug' : 'user-friendly';

      // Generate a unique tracker ID for this session
      const trackerId = `wheel_gen_${Date.now()}`;
      this.currentProgressTrackerId = trackerId;

      // Initialize progress bar with 0% progress
      const progressBar = this.shadowRoot?.querySelector('real-time-progress-bar');
      if (progressBar) {
        progressBar.dispatchEvent(new CustomEvent('progress-update', {
          detail: {
            data: {
              tracker_id: trackerId,
              progress_percent: 0,
              stage: 'initiating',
              stage_id: 'init',
              stage_name: 'Initializing wheel generation...',
              message: 'Starting wheel generation process',
              workflow_type: 'wheel_generation'
            }
          }
        }));
      }

      // Force a re-render to ensure progress modal is displayed
      this.requestUpdate();

      // Start a fallback progress simulation in case backend doesn't send real-time updates
      this.startProgressFallback(trackerId);

      // SECURITY FIX: Use authenticated user's ID, not hardcoded fallback
      const currentUser = this.authService.getCurrentUser();
      if (!currentUser?.id && !this.debugUserId) {
        console.error('❌ No authenticated user found - cannot generate wheel');
        this.error = 'User not authenticated. Please log in to generate a wheel.';
        this.isLoading = false;
        return;
      }
      const userId = this.debugUserId || currentUser!.id;

      // Convert time available from percentage to minutes (10min to 4h range)
      const timeInMinutes = Math.round(10 + (this.timeAvailable / 100) * (240 - 10)); // 10min to 240min (4h)

      const message = `Generate a personalized activity wheel (Energy: ${this.energyLevel}%, Time: ${timeInMinutes} minutes)`;

      console.log('🎯 Sending wheel generation request with energy level:', this.energyLevel, 'and time available:', timeInMinutes, 'minutes');

      // Use the existing sendChatMessage method but with wheel generation workflow and forced flag
      await this.messageHandler.sendChatMessage(
        message,
        userId,
        'wheel_generation', // workflowType
        this.configService.isDebugMode() ? this.debugLLMConfigId : undefined, // llmConfigId
        {
          forced_wheel_generation: true, // Force wheel generation regardless of profile completion
          energy_level: this.energyLevel,
          time_available_minutes: timeInMinutes
        }
      );

      // TODO: Modify MessageHandler to support energy_level and time_available parameters
      // For now, the energy level and time available are included in the message text

    } catch (error) {
      console.error('❌ Failed to generate wheel:', error);
      this.error = 'Failed to generate wheel. Please try again.';
      this.isLoading = false;
    }
  };

  /**
   * Handle spin wheel button click - now shows contract modal first
   */
  private handleSpinWheel = () => {
    if (!this.wheelData || this.isLoading) {
      console.log('[APP] Cannot spin: wheelData missing or loading in progress');
      return;
    }

    console.log('[APP] Spin wheel button clicked - showing contract modal...');
    this.showContractModal = true;
  };

  /**
   * Handle contract acceptance - proceed with actual wheel spin
   */
  private handleContractAccepted = (event: CustomEvent) => {
    console.log('[APP] Contract accepted, proceeding with wheel spin...', event.detail);
    this.showContractModal = false;

    // Store signature data for later use in winning modal
    const signature = event.detail.signature;
    if (signature) {
      console.log('📝 Contract signature captured for winning modal');
    }

    // Now proceed with the actual wheel spin
    this.performWheelSpin();
  };

  /**
   * Handle contract cancellation
   */
  private handleContractCancelled = () => {
    console.log('[APP] Contract cancelled by user');
    this.showContractModal = false;
  };

  /**
   * Perform the actual wheel spin (extracted from original handleSpinWheel)
   */
  private performWheelSpin() {
    console.log('[APP] Performing wheel spin...');

    // Find the interactive wheel component (not the background one)
    const wheelComponent = this.shadowRoot?.querySelector('.wheel-foreground game-wheel') as any;
    if (wheelComponent && typeof wheelComponent.spin === 'function') {
      console.log('[APP] Found interactive wheel component, checking state...');

      // Get wheel state for debugging
      const wheelState = wheelComponent.getWheelState();
      console.log('[APP] Wheel state:', wheelState);

      // Check if wheel is properly initialized
      if (!wheelState.hasPhysicsEngine || !wheelState.hasRenderer) {
        console.log('[APP] Wheel not fully initialized, waiting and retrying...');
        setTimeout(() => {
          console.log('[APP] Retrying spin after initialization delay...');
          this.performWheelSpin();
        }, 500);
        return;
      }

      // Check if wheel is already spinning
      if (wheelState.isSpinning) {
        console.log('[APP] Wheel is already spinning, ignoring spin request');
        return;
      }

      console.log('[APP] Triggering wheel spin...');
      wheelComponent.spin();
    } else {
      console.error('[APP] Interactive wheel component not found or spin method not available');
      console.log('[APP] Available wheel components:', this.shadowRoot?.querySelectorAll('game-wheel'));

      // Try to find any game-wheel component as fallback
      const anyWheelComponent = this.shadowRoot?.querySelector('game-wheel') as any;
      if (anyWheelComponent) {
        console.log('[APP] Found fallback wheel component, attempting spin...');
        anyWheelComponent.spin();
      }
    }
  }

  private toggleActivityExpanded = (activityId: string) => {
    const newExpanded = new Set(this.expandedActivities);
    if (newExpanded.has(activityId)) {
      newExpanded.delete(activityId);
    } else {
      newExpanded.add(activityId);
    }
    this.expandedActivities = newExpanded;
  };

  private openActivityModal = (activityId: string) => {
    this.selectedActivityId = activityId;
    this.showActivityModal = true;
    // Force reload of activity catalog to ensure we have the latest data
    this.invalidateActivityCache();
    this.loadActivityCatalog();
  };

  private closeActivityModal = () => {
    this.showActivityModal = false;
    this.selectedActivityId = null;
    this.searchQuery = '';
  };

  private handleSearchChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    this.searchQuery = target.value;

    // Debounce search to avoid too many API calls
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
      this.performSearch();
    }, 300);
  };

  private searchTimeout: ReturnType<typeof setTimeout> | undefined;

  private async performSearch() {
    if (!this.searchQuery.trim()) {
      // If search is empty, load full catalog
      await this.loadActivityCatalog();
      return;
    }

    try {
      console.log(`🔍 Searching activities for: "${this.searchQuery}"`);
      this.isLoading = true;

      const response = await fetch(`${this.getBackendBaseUrl()}/api/activities/catalog/?search=${encodeURIComponent(this.searchQuery)}&limit=50`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authService.getToken()}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.activities) {
          this.activityCatalog = data.activities;
          console.log(`✅ Search found ${data.total_count} activities`);
        } else {
          console.warn('⚠️ Invalid search response');
        }
      } else {
        console.warn('⚠️ Search request failed');
      }
    } catch (error) {
      console.error('❌ Error performing search:', error);
    } finally {
      this.isLoading = false;
    }
  }

  private selectCatalogActivity = async (catalogActivity: any) => {
    if (!this.selectedActivityId || !this.wheelData) {
      console.error('No activity selected or no wheel data available');
      return;
    }

    console.log('Changing activity from', this.selectedActivityId, 'to:', catalogActivity);

    // If it's a generic activity, auto-tailor it first
    let finalActivity = catalogActivity;
    if (catalogActivity.type === 'generic') {
      try {
        // Show loading state
        this.isLoading = true;
        this.requestUpdate();

        console.log('🔄 Auto-tailoring generic activity:', catalogActivity.name);

        // Call the tailor API
        const config = this.configService.getConfig();
        const baseUrl = config.websocket.url.replace('ws://', 'http://').replace('wss://', 'https://').replace('/ws/game/', '');

        const response = await fetch(`${baseUrl}/api/activities/tailor/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...(this.authService.getToken() ? {
              'Authorization': `Bearer ${this.authService.getToken()}`
            } : {})
          },
          body: JSON.stringify({
            generic_activity_id: catalogActivity.id
          })
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.activity) {
            finalActivity = data.activity;
            console.log('✅ Activity auto-tailored successfully:', finalActivity.name);

            // Add to catalog cache for future use
            this.activityCatalog = [...this.activityCatalog, finalActivity];
            this.invalidateActivityCache(); // Refresh cache
          } else {
            console.warn('⚠️ Failed to tailor activity, using generic version');
          }
        } else {
          console.warn('⚠️ Tailor API failed, using generic version');
        }
      } catch (error) {
        console.error('❌ Error auto-tailoring activity:', error);
        console.log('Using generic version as fallback');
      } finally {
        this.isLoading = false;
        this.requestUpdate();
      }
    }

    // Find the activity to replace in the wheel data
    const activityIndex = this.wheelData.segments.findIndex(segment =>
      segment.id === this.selectedActivityId ||
      segment.activityId === this.selectedActivityId ||
      segment.activity_tailored_id === this.selectedActivityId
    );

    if (activityIndex === -1) {
      console.error('Activity not found in wheel data:', this.selectedActivityId);
      return;
    }

    // Create new wheel data with the replaced activity
    const updatedSegments = [...this.wheelData.segments];
    const originalSegment = updatedSegments[activityIndex];

    // Replace the activity while keeping the same percentage and color
    updatedSegments[activityIndex] = {
      ...originalSegment,
      id: finalActivity.id,
      name: finalActivity.name,
      text: finalActivity.name,
      description: finalActivity.description,
      domain: finalActivity.domain,
      base_challenge_rating: finalActivity.base_challenge_rating,
      activityId: finalActivity.id,
      activity_tailored_id: finalActivity.id
    };

    // Update the wheel data
    this.wheelData = {
      ...this.wheelData,
      segments: updatedSegments
    };

    console.log('✅ Activity replaced successfully');
    this.closeActivityModal();

    // TODO: Optionally send the change to the backend to persist it
    // this.sendActivityChangeToBackend(this.selectedActivityId, finalActivity.id);
  };

  // New activity creation modal methods
  private openCreateActivityModal = () => {
    this.showCreateActivityModal = true;
    // Reset form
    this.newActivityForm = {
      name: '',
      description: '',
      domain: 'general',
      base_challenge_rating: 50
    };
  };

  private closeCreateActivityModal = () => {
    this.showCreateActivityModal = false;
  };

  private closeProfileModal = () => {
    this.showProfileModal = false;
    // Reset editing state when closing modal
    this.editingProfileFields.clear();
  };

  private closeWinningModal = () => {
    // Store the winning activity before closing the modal
    const activityToEnjoy = this.winningActivity;

    this.showWinningModal = false;
    this.winningActivity = null;

    // Clean up stored signature after wheel completion
    ContractDisclaimerModal.clearStoredSignature();

    // Show enjoy overlay with the activity details
    if (activityToEnjoy) {
      this.showEnjoyOverlayWithActivity(activityToEnjoy);
    }
  };

  // Profile modal accordion methods
  private toggleProfileSection = (sectionId: string) => {
    const newExpanded = new Set(this.expandedProfileSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    this.expandedProfileSections = newExpanded;
  };

  private startEditingField = (fieldId: string) => {
    const newEditing = new Set(this.editingProfileFields);
    newEditing.add(fieldId);
    this.editingProfileFields = newEditing;
  };

  private stopEditingField = (fieldId: string) => {
    const newEditing = new Set(this.editingProfileFields);
    newEditing.delete(fieldId);
    this.editingProfileFields = newEditing;
  };

  // Wheel item management methods
  private handleRemoveActivity = (e: Event, item: any) => {
    e.stopPropagation(); // Prevent accordion toggle

    // COMPREHENSIVE DEBUG LOGGING to identify the exact issue
    console.log('🗑️ ===== WHEEL ITEM REMOVAL DEBUG =====');
    console.log('🗑️ Remove activity clicked for item:', JSON.stringify(item, null, 2));
    console.log('🗑️ Item ID (should be wheel item ID):', item.id);
    console.log('🗑️ Item wheel_item_id:', item.wheel_item_id);
    console.log('🗑️ Item activity_tailored_id:', item.activity_tailored_id);
    console.log('🗑️ Item name:', item.name);

    // Validate that we have a proper wheel item ID
    const wheelItemId = item.id; // Should be the wheel item ID from backend

    if (!wheelItemId) {
      console.error('🚨 ERROR: No wheel item ID found!');
      return;
    }

    if (wheelItemId.startsWith('llm_tailored_') || wheelItemId.startsWith('generic-')) {
      console.error('🚨 ERROR: Received activity tailored ID instead of wheel item ID!');
      console.error('🚨 This indicates a data mapping issue in the frontend');
      console.error('🚨 Expected wheel item ID format: wheel-item-* or item_*');
      console.error('🚨 Received:', wheelItemId);
      return;
    }

    if (wheelItemId.startsWith('wheel-item-') || wheelItemId.startsWith('item_')) {
      console.log('✅ ARCHITECTURAL FIX WORKING: Correct wheel item ID format detected');
      console.log('✅ Wheel item ID:', wheelItemId);
    }

    console.log('✅ Using wheel item ID for removal:', wheelItemId);
    console.log('🗑️ ===== END DEBUG =====');

    // Show feedback modal
    this.feedbackModalConfig = {
      title: "Don't like this one ?",
      message: "Please tell us more about the reason why you don't want this in your wheel",
      feedback_type: "wheel_item_refusal",
      content_type: "WheelItem",
      object_id: wheelItemId,
      primaryButtonLabel: "Remove Activity",
      secondaryButtonLabel: "Back"
    };
    this.showFeedbackModal = true;
  };

  private openAddActivityModal = () => {
    this.showAddActivityModal = true;
    // Always load fresh activity catalog to ensure we have all activities
    this.searchQuery = ''; // Clear any previous search
    this.loadActivityCatalog();
  };

  private closeAddActivityModal = () => {
    this.showAddActivityModal = false;
    this.searchQuery = '';
  };

  private closeFeedbackModal = () => {
    this.showFeedbackModal = false;
  };

  private submitFeedback = async () => {
    try {
      const response = await fetch(`${this.getBackendBaseUrl()}/api/feedback/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authService.getToken()}`
        },
        body: JSON.stringify({
          feedback_type: this.feedbackModalConfig.feedback_type,
          content_type: this.feedbackModalConfig.content_type,
          object_id: this.feedbackModalConfig.object_id,
          user_comment: (document.getElementById('feedback-comment') as HTMLTextAreaElement)?.value || '',
          criticality: 1,
          context_data: {}
        })
      });

      if (response.ok) {
        // Remove the wheel item after feedback is submitted
        await this.removeWheelItem(this.feedbackModalConfig.object_id);
        this.closeFeedbackModal();
      } else {
        console.error('Failed to submit feedback');
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
    }
  };

  private removeWheelItem = async (wheelItemId: string) => {
    try {
      const response = await fetch(`${this.getBackendBaseUrl()}/api/wheel-items/${wheelItemId}/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.authService.getToken()}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.wheel_data) {
          // Update wheel data with new segments
          this.wheelData = {
            segments: data.wheel_data.segments.map((segment: any) => ({
              id: segment.id,
              text: segment.name,
              name: segment.name,
              description: segment.description,
              percentage: segment.percentage,
              color: segment.color,
              activity_tailored_id: segment.activity_tailored_id
            }))
          };
        }
      } else {
        console.error('Failed to remove wheel item');
      }
    } catch (error) {
      console.error('Error removing wheel item:', error);
    }
  };

  private addActivityToWheel = async (activity: any) => {
    try {
      const response = await fetch(`${this.getBackendBaseUrl()}/api/wheel-items/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authService.getToken()}`
        },
        body: JSON.stringify({
          activity_id: activity.id,
          activity_type: activity.type
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.wheel_data) {
          // Update wheel data with new segments
          this.wheelData = {
            segments: data.wheel_data.segments.map((segment: any) => ({
              id: segment.id,
              text: segment.name,
              name: segment.name,
              description: segment.description,
              percentage: segment.percentage,
              color: segment.color,
              activity_tailored_id: segment.activity_tailored_id
            }))
          };
          this.closeAddActivityModal();
        }
      } else {
        const errorData = await response.json();
        console.error('Failed to add activity to wheel:', errorData.error);
        // Show error to user
        alert(errorData.error || 'Failed to add activity to wheel');
      }
    } catch (error) {
      console.error('Error adding activity to wheel:', error);
    }
  };

  private saveProfileField = async (fieldId: string, value: string) => {
    // TODO: Implement API call to save profile field
    console.log(`Saving profile field ${fieldId}:`, value);

    // For now, just update local state
    if (this.currentUserProfile) {
      // Update the field in the current profile
      const fieldPath = fieldId.split('.');
      let target = this.currentUserProfile;
      for (let i = 0; i < fieldPath.length - 1; i++) {
        if (!target[fieldPath[i]]) target[fieldPath[i]] = {};
        target = target[fieldPath[i]];
      }
      target[fieldPath[fieldPath.length - 1]] = value;

      // Force re-render
      this.requestUpdate();
    }

    this.stopEditingField(fieldId);
  };

  private renderEditableField(fieldId: string, label: string, value: string) {
    const isEditing = this.editingProfileFields.has(fieldId);

    if (isEditing) {
      return html`
        <div class="profile-field">
          <label>${label}</label>
          <input
            type="text"
            class="profile-edit-input"
            .value=${value}
            @keydown=${(e: KeyboardEvent) => {
              if (e.key === 'Enter') {
                this.saveProfileField(fieldId, (e.target as HTMLInputElement).value);
              } else if (e.key === 'Escape') {
                this.stopEditingField(fieldId);
              }
            }}
            @blur=${(e: Event) => {
              this.saveProfileField(fieldId, (e.target as HTMLInputElement).value);
            }}
            autofocus
          />
          <div class="profile-edit-actions">
            <button
              class="profile-edit-btn save"
              @click=${(e: Event) => {
                const input = (e.target as HTMLElement).parentElement?.previousElementSibling as HTMLInputElement;
                this.saveProfileField(fieldId, input.value);
              }}
            >
              Save
            </button>
            <button
              class="profile-edit-btn cancel"
              @click=${() => this.stopEditingField(fieldId)}
            >
              Cancel
            </button>
          </div>
        </div>
      `;
    }

    return html`
      <div class="profile-field">
        <label>${label}</label>
        <div
          class="profile-value editable"
          @click=${() => this.startEditingField(fieldId)}
          title="Click to edit"
        >
          ${value}
        </div>
      </div>
    `;
  }

  private handleNewActivityFormChange = (field: string, value: string | number) => {
    this.newActivityForm = {
      ...this.newActivityForm,
      [field]: value
    };
  };

  private createNewActivity = async () => {
    // Validate form
    if (!this.newActivityForm.name.trim()) {
      alert('Please enter an activity name');
      return;
    }

    if (!this.newActivityForm.description.trim()) {
      alert('Please enter an activity description');
      return;
    }

    try {
      console.log('Creating new activity:', this.newActivityForm);

      // Show loading state
      this.isLoading = true;
      this.requestUpdate();

      // Send to backend API to create new activity
      const config = this.configService.getConfig();
      const baseUrl = config.websocket.url.replace('ws://', 'http://').replace('wss://', 'https://').replace('/ws/game/', '');

      const response = await fetch(`${baseUrl}/api/activities/create/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.authService.getToken() ? {
            'Authorization': `Bearer ${this.authService.getToken()}`
          } : {})
        },
        body: JSON.stringify({
          name: this.newActivityForm.name,
          description: this.newActivityForm.description,
          domain: this.newActivityForm.domain,
          base_challenge_rating: this.newActivityForm.base_challenge_rating,
          duration_range: '15-30 minutes',
          instructions: this.newActivityForm.description
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.activity) {
          const newActivity = data.activity;

          // Add to catalog
          this.activityCatalog = [...this.activityCatalog, newActivity];

          // Invalidate cache since we added a new activity
          this.invalidateActivityCache();

          // Close create modal
          this.closeCreateActivityModal();

          // Show success message
          console.log('✅ New activity created successfully:', newActivity.name);

          // Auto-select the new activity if we're in activity selection mode
          if (this.selectedActivityId) {
            await this.selectCatalogActivity(newActivity);
          }
        } else {
          throw new Error(data.error || 'Failed to create activity');
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create activity');
      }
    } catch (error) {
      console.error('Failed to create new activity:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      alert(`Failed to create new activity: ${errorMessage}`);
    } finally {
      this.isLoading = false;
      this.requestUpdate();
    }
  };

  private async loadActivityCatalog() {
    const currentUserId = this.currentUser?.id || null;
    const now = Date.now();

    // Check cache validity
    if (this.activityCatalogCache.data &&
        this.activityCatalogCache.userId === currentUserId &&
        (now - this.activityCatalogCache.timestamp) < this.ACTIVITY_CACHE_TTL) {
      console.log('✅ Using cached activity catalog');
      this.activityCatalog = this.activityCatalogCache.data;
      return;
    }

    try {
      console.log('🔄 Loading fresh activity catalog from API...');
      const config = this.configService.getConfig();
      const baseUrl = config.websocket.url.replace('ws://', 'http://').replace('wss://', 'https://').replace('/ws/game/', '');

      const response = await fetch(`${baseUrl}/api/activities/catalog/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // Include authentication if available
          ...(this.authService.getToken() ? {
            'Authorization': `Bearer ${this.authService.getToken()}`
          } : {})
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.activities) {
          this.activityCatalog = data.activities;

          // Update cache
          this.activityCatalogCache = {
            data: data.activities,
            timestamp: now,
            userId: currentUserId
          };

          console.log(`✅ Loaded ${data.total_count} activities (${data.tailored_count} tailored, ${data.generic_count} generic)`);
        } else {
          console.warn('⚠️ Invalid response from activity catalog API');
          this.loadFallbackActivityCatalog();
        }
      } else {
        console.warn('⚠️ Failed to load activity catalog from API, using fallback');
        this.loadFallbackActivityCatalog();
      }
    } catch (error) {
      console.error('❌ Error loading activity catalog:', error);
      this.loadFallbackActivityCatalog();
    }
  }

  /**
   * Invalidate activity catalog cache (e.g., when user changes or new activity is created)
   */
  private invalidateActivityCache() {
    this.activityCatalogCache = {
      data: null,
      timestamp: 0,
      userId: null
    };
    console.log('🗑️ Activity catalog cache invalidated');
  }

  private loadFallbackActivityCatalog() {
    // Fallback mock data with proper typing
    this.activityCatalog = [
      {
        id: 'cat-1',
        name: 'Morning Yoga',
        description: 'Start your day with gentle stretching and mindfulness',
        domain: 'physical',
        base_challenge_rating: 30,
        type: 'generic' as const,
        icon: '🎯'
      },
      {
        id: 'cat-2',
        name: 'Creative Writing',
        description: 'Express yourself through words and storytelling',
        domain: 'creative',
        base_challenge_rating: 50,
        type: 'generic' as const,
        icon: '🎯'
      },
      {
        id: 'cat-3',
        name: 'Learn a New Recipe',
        description: 'Explore culinary skills with a new dish',
        domain: 'practical',
        base_challenge_rating: 40,
        type: 'generic' as const,
        icon: '🎯'
      },
      {
        id: 'cat-4',
        name: 'Nature Photography',
        description: 'Capture the beauty of the outdoors',
        domain: 'creative',
        base_challenge_rating: 60,
        type: 'generic' as const,
        icon: '🎯'
      },
      {
        id: 'cat-5',
        name: 'Meditation Session',
        description: 'Find inner peace and clarity',
        domain: 'mental',
        base_challenge_rating: 25,
        type: 'generic' as const,
        icon: '🎯'
      }
    ];
  }

  private get filteredCatalog() {
    let catalog = this.activityCatalog;

    // If we have a search query, the API already filtered the results
    // So we don't need to filter again locally
    // Just sort to show tailored activities first
    return catalog.sort((a, b) => {
      // Tailored activities come first
      if (a.type === 'tailored' && b.type === 'generic') return -1;
      if (a.type === 'generic' && b.type === 'tailored') return 1;

      // Within same type, sort alphabetically by name
      return a.name.localeCompare(b.name);
    });
  }

  private getWheelItems() {
    if (!this.wheelData?.segments) return [];

    return this.wheelData.segments.map(segment => {
      // CRITICAL FIX: Ensure we use the correct wheel item ID for removal operations
      // The segment.id should be the wheel item ID from the backend
      const wheelItemId = segment.id; // This comes from backend as wheel item ID (e.g., "item_0d7b6542")
      const activityTailoredId = segment.activity_tailored_id; // This is the activity tailored ID

      console.log(`🔍 Processing segment - Wheel Item ID: ${wheelItemId}, Activity Tailored ID: ${activityTailoredId}`);

      return {
        id: wheelItemId, // Use wheel item ID as the primary ID for removal operations
        name: segment.name || segment.text || 'Unknown Activity',
        description: segment.description || 'No description available',
        percentage: segment.percentage,
        color: segment.color,
        domain: segment.domain || 'general',
        base_challenge_rating: segment.base_challenge_rating || 50,
        activity_tailored_id: activityTailoredId,
        wheel_item_id: wheelItemId // Explicitly store wheel item ID for removal
      };
    });
  }

  private renderActivityList() {
    const wheelItems = this.getWheelItems();
    if (wheelItems.length === 0) return '';

    return html`
      <div class="activity-list">
        <div class="activity-list-header">
          <span>Activities (${wheelItems.length})</span>
          <button
            class="add-activity-btn"
            @click=${this.openAddActivityModal}
            title="Add activity to wheel"
          >
            +
          </button>
        </div>
        ${wheelItems.map(item => this.renderActivityItem(item))}
      </div>
    `;
  }

  private renderActivityItem(item: any) {
    const isExpanded = this.expandedActivities.has(item.id);

    return html`
      <div class="activity-item ${isExpanded ? 'expanded' : ''}">
        <div class="activity-item-header" @click=${() => this.toggleActivityExpanded(item.id)}>
          <div class="activity-item-title">
            <div class="activity-color-dot" style="background-color: ${item.color}"></div>
            <span class="activity-name">${item.name}</span>
          </div>
          <div style="display: flex; align-items: center; gap: var(--spacing-2);">
            <button
              class="remove-activity-btn"
              @click=${(e: Event) => this.handleRemoveActivity(e, item)}
              title="Remove from wheel"
            >
              ❌
            </button>
            <div class="activity-expand-icon">▼</div>
          </div>
        </div>
        <div class="activity-item-content">
          <div class="activity-item-details">
            <div class="activity-description">${item.description}</div>
            <div class="activity-meta">
              <div class="activity-meta-item">
                <span>🎯</span>
                <span>Domain: ${item.domain}</span>
              </div>
              <div class="activity-meta-item">
                <span>⚡</span>
                <span>Challenge: ${item.base_challenge_rating}/100</span>
              </div>
              <div class="activity-meta-item">
                <span>📊</span>
                <span>Weight: ${item.percentage.toFixed(1)}%</span>
              </div>
            </div>

          </div>
        </div>
      </div>
    `;
  }

  private renderActivityModal() {
    if (!this.showActivityModal) return '';

    return html`
      <div class="modal-overlay" @click=${this.closeActivityModal}>
        <div class="modal activity-modal" @click=${(e: Event) => e.stopPropagation()}>
          <div class="modal-header">
            <h3 class="modal-title">Change Activity</h3>
            <button class="modal-close" @click=${this.closeActivityModal}>×</button>
          </div>
          <div class="modal-body">
            <button class="create-activity-btn" @click=${this.openCreateActivityModal}>
              <span>➕</span>
              <span>Create New Activity</span>
            </button>
            <input
              type="text"
              class="search-input"
              placeholder="Search activities..."
              .value=${this.searchQuery}
              @input=${this.handleSearchChange}
            />
            <div class="activity-catalog">
              ${this.isLoading ? html`
                <div class="loading-state">
                  <div class="spinner"></div>
                  <p>Creating tailored activity...</p>
                </div>
              ` : this.filteredCatalog.length > 0 ?
                this.filteredCatalog.map(activity => html`
                  <div class="catalog-item ${activity.type}" @click=${() => this.selectCatalogActivity(activity)}>
                    <div class="catalog-item-info">
                      <div class="catalog-item-header">
                        <span class="activity-type-icon ${activity.type}">${activity.type === 'tailored' ? '⭐' : '🎯'}</span>
                        <div class="catalog-item-name">${activity.name}</div>
                        <span class="activity-type-badge ${activity.type}">${activity.type === 'tailored' ? 'Tailored' : 'Generic'}</span>
                      </div>
                      <div class="catalog-item-description">${activity.description}</div>
                      <div class="catalog-item-meta">
                        <span>🎯 ${activity.domain}</span>
                        <span>⚡ ${activity.base_challenge_rating}/100</span>
                      </div>
                    </div>
                  </div>
                `) :
                html`<div class="no-results">No activities found matching "${this.searchQuery}"</div>`
              }
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderCreateActivityModal() {
    if (!this.showCreateActivityModal) return '';

    return html`
      <div class="modal-overlay" @click=${this.closeCreateActivityModal}>
        <div class="modal" @click=${(e: Event) => e.stopPropagation()}>
          <div class="modal-header">
            <h3 class="modal-title">Create New Activity</h3>
            <button class="modal-close" @click=${this.closeCreateActivityModal}>×</button>
          </div>
          <div class="modal-body">
            <form @submit=${(e: Event) => { e.preventDefault(); this.createNewActivity(); }}>
              <div class="form-group">
                <label class="form-label" for="activity-name">Activity Name *</label>
                <input
                  type="text"
                  id="activity-name"
                  class="form-input"
                  placeholder="e.g., Morning Yoga, Creative Writing..."
                  .value=${this.newActivityForm.name}
                  @input=${(e: Event) => this.handleNewActivityFormChange('name', (e.target as HTMLInputElement).value)}
                  required
                />
              </div>

              <div class="form-group">
                <label class="form-label" for="activity-description">Description *</label>
                <textarea
                  id="activity-description"
                  class="form-textarea"
                  placeholder="Describe what this activity involves and why it's beneficial..."
                  .value=${this.newActivityForm.description}
                  @input=${(e: Event) => this.handleNewActivityFormChange('description', (e.target as HTMLTextAreaElement).value)}
                  required
                ></textarea>
              </div>

              <div class="form-group">
                <label class="form-label" for="activity-domain">Domain</label>
                <select
                  id="activity-domain"
                  class="form-select"
                  .value=${this.newActivityForm.domain}
                  @change=${(e: Event) => this.handleNewActivityFormChange('domain', (e.target as HTMLSelectElement).value)}
                >
                  <option value="general">General</option>
                  <option value="physical">Physical</option>
                  <option value="mental">Mental</option>
                  <option value="creative">Creative</option>
                  <option value="social">Social</option>
                  <option value="practical">Practical</option>
                  <option value="learning">Learning</option>
                  <option value="relaxation">Relaxation</option>
                </select>
              </div>

              <div class="form-group">
                <label class="form-label" for="activity-challenge">Challenge Level</label>
                <input
                  type="range"
                  id="activity-challenge"
                  class="form-range"
                  min="1"
                  max="100"
                  .value=${this.newActivityForm.base_challenge_rating.toString()}
                  @input=${(e: Event) => this.handleNewActivityFormChange('base_challenge_rating', parseInt((e.target as HTMLInputElement).value))}
                />
                <div class="range-value">${this.newActivityForm.base_challenge_rating}/100</div>
              </div>

              <div class="modal-actions">
                <button type="button" class="btn-secondary" @click=${this.closeCreateActivityModal}>
                  Cancel
                </button>
                <button
                  type="submit"
                  class="btn-primary"
                  ?disabled=${!this.newActivityForm.name.trim() || !this.newActivityForm.description.trim()}
                >
                  Create Activity
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    `;
  }

  render() {
    // Show login form if not authenticated (hide demo mode for now)
    if (!this.isAuthenticated) {
      return html`
        <login-form
          @login-success=${this.handleLoginSuccess}
          .hideDemoMode=${true}
        ></login-form>
      `;
    }

    return html`
      <div class="app-container">
        <!-- Header -->
        <header class="header">
          <div class="header-content">
            <div class="logo">
              <div class="logo-icon">🎯</div>
              <span>Goali</span>
              ${this.configService.isDebugMode() ? html`
                <span style="font-size: 0.7em; color: #ffaa00; margin-left: 8px;">DEBUG</span>
              ` : ''}
            </div>
            <div class="status-bar">
              <!-- Connection Status -->
              <div class="connection-status">
                <div class="status-indicator ${this.connectionState === 'connected' ? 'connected' : this.connectionState === 'connecting' ? 'connecting' : 'disconnected'}"></div>
                <span>${this.getConnectionStatusText()}</span>
              </div>

              <!-- User Info -->
              ${this.currentUser ? html`
                <div class="user-info">
                  <span class="user-name">${this.currentUser.name}</span>
                  ${this.currentUser.isStaff ? html`<span class="staff-badge">Staff</span>` : ''}
                  <button
                    class="user-icon-btn"
                    @click=${this.openUserProfile}
                    title="User Profile Management"
                  >
                    👤
                  </button>
                  <button
                    class="logout-btn"
                    @click=${this.handleLogout}
                    title="Logout"
                  >
                    🚪
                  </button>
                </div>
              ` : ''}

              <!-- Debug Controls (only for staff users) -->
              ${this.currentUser?.isStaff ? html`
                <button
                  class="debug-btn"
                  @click=${() => this.showDebugPanel = !this.showDebugPanel}
                >
                  🐛 Debug
                </button>
              ` : ''}
            </div>
          </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
          <!-- Error Banner -->
          ${this.error ? html`
            <div class="error-banner">
              <span>⚠️ ${this.error}</span>
              <button @click=${this.handleClearError}>Dismiss</button>
            </div>
          ` : ''}

          <!-- Progress Modal Overlay -->
          ${this.showProgressBar ? html`
            <div class="progress-modal-overlay" @click=${(e: Event) => {
              // Close modal if clicking on overlay (not the modal content)
              if (e.target === e.currentTarget && this.progressModalMode === 'debug') {
                this.closeProgressModal();
              }
            }}>
              <div class="progress-modal ${this.progressModalMode === 'user-friendly' ? 'user-friendly' : ''}" @click=${(e: Event) => e.stopPropagation()}>
                <div class="progress-modal-header">
                  <h3 class="progress-modal-title">${this.progressTitle}</h3>
                  ${this.progressModalMode === 'debug' ? html`
                    <button class="progress-modal-close" @click=${this.closeProgressModal}>×</button>
                  ` : ''}
                </div>
                <real-time-progress-bar
                  .trackerId=${this.currentProgressTrackerId}
                  .workflowType=${'wheel_generation'}
                  .title=${this.progressTitle}
                  .showMetrics=${this.progressModalMode === 'debug'}
                  .showTimeline=${this.progressModalMode === 'debug'}
                  .compact=${this.progressModalMode === 'user-friendly'}
                ></real-time-progress-bar>
              </div>
            </div>
          ` : ''}

          <!-- Wheel Section -->
          <section class="wheel-section">
            <div class="wheel-container">
              <!-- Background wheel (always visible, greyed out) -->
              <div class="wheel-background">
                <game-wheel
                  .wheelData=${this.getBackgroundWheelData()}
                  hide-ui
                  disable-interaction
                ></game-wheel>
              </div>

              <!-- Foreground content -->
              <div class="wheel-foreground">
                ${this.wheelData ? html`
                  <game-wheel
                    .wheelData=${this.wheelData}
                    @wheel-spin-complete=${this.handleWheelSpinComplete}
                  ></game-wheel>
                ` : html`
                  <div class="wheel-placeholder">
                    <div class="wheel-placeholder-icon">🎡</div>
                    <h3>Your Personalized Activity Wheel</h3>
                    <p>
                      ${this.wsConnected
                        ? 'Adjust your availability and energy level, and I\'ll create a custom wheel of activities just for you!'
                        : this.configService.isDebugMode()
                          ? 'Debug mode: Use the chat below to generate your wheel, or configure settings with the debug panel (Ctrl+Shift+D).'
                          : 'Connect to start creating your personalized activity wheel!'
                      }
                    </p>
                    ${this.wsConnected ? html`
                      <div style="margin-top: 16px; font-size: 14px; color: #666; font-style: italic;">
                        💡
                      </div>
                    ` : ''}
                  </div>
                `}
              </div>
            </div>

            <!-- Wheel Controls -->
            <div class="wheel-controls">
              <!-- Button Bar -->
              <div class="button-bar">
                <div class="potentiometer-control">
                  <label class="potentiometer-label">Time Available</label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    .value=${this.timeAvailable.toString()}
                    @input=${this.handleTimeAvailableChange}
                    class="potentiometer-slider"
                  />
                  <div class="potentiometer-value">${this.formatTimeAvailable()}</div>
                </div>

                <!-- Generate/Spin Button -->
                <div class="action-button-container">
                  ${this.wheelData ? html`
                    <button
                      class="action-button spin-button"
                      ?disabled=${this.isLoading || this.connectionState !== 'connected'}
                      @click=${this.handleSpinWheel}
                    >
                      ${this.isLoading ? 'Spinning...' : 'SPIN!'}
                    </button>
                  ` : html`
                    <button
                      class="action-button generate-button"
                      ?disabled=${this.connectionState !== 'connected' || this.isLoading}
                      @click=${this.handleGenerateWheel}
                    >
                      ${this.getButtonText()}
                    </button>
                  `}
                </div>

                <div class="potentiometer-control">
                  <label class="potentiometer-label">Energy Level</label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    .value=${this.energyLevel.toString()}
                    @input=${this.handleEnergyLevelChange}
                    class="potentiometer-slider"
                  />
                  <div class="potentiometer-value">${this.energyLevel}%</div>
                </div>
              </div>

              <!-- Activity List -->
              ${this.wheelData ? this.renderActivityList() : ''}
            </div>
          </section>

          <!-- Chat Section (hidden for wheel focus) -->
          <!--
          <section class="chat-section">
            <chat-interface
              .messages=${this.messages}
              .connectionStatus=${this.wsConnected ? 'connected' : 'disconnected'}
              .isProcessing=${this.isLoading}
              @message-send=${this.handleMessageSend}
            ></chat-interface>
          </section>
          -->
        </main>

        <!-- Connection Error Banner -->
        ${!this.wsConnected && this.connectionState === 'disconnected' ? html`
          <div class="error-banner connection-error-banner">
            <span>⚠️ Impossible to connect to backend</span>
          </div>
        ` : ''}

        <!-- Debug Panel -->
        ${this.configService.isDebugMode() ? html`
          <debug-panel
            .visible=${this.showDebugPanel}
            @user-changed=${this.handleDebugUserChange}
            @llm-config-changed=${this.handleDebugLLMConfigChange}
            @backend-url-changed=${this.handleDebugBackendUrlChange}
          ></debug-panel>
        ` : ''}

        <!-- Contract Disclaimer Modal -->
        <contract-disclaimer-modal
          ?visible=${this.showContractModal}
          @contract-accepted=${this.handleContractAccepted}
          @contract-cancelled=${this.handleContractCancelled}
        ></contract-disclaimer-modal>

        <!-- Activity Change Modal -->
        ${this.renderActivityModal()}

        <!-- Create Activity Modal -->
        ${this.renderCreateActivityModal()}

        <!-- Profile Modal -->
        ${this.renderProfileModal()}

        <!-- Winning Modal -->
        ${this.renderWinningModal()}

        <!-- Feedback Modal -->
        ${this.renderFeedbackModal()}

        <!-- Add Activity Modal -->
        ${this.renderAddActivityModal()}

        <!-- Enjoy Overlay -->
        ${this.renderEnjoyOverlay()}

        <!-- Post-Activity Feedback Modal -->
        ${this.renderPostActivityFeedbackModal()}
      </div>
    `;
  }

  /**
   * Handle debug user change
   */
  private handleDebugUserChange(event: CustomEvent) {
    console.log('Debug: User changed to', event.detail.userId);
    this.debugUserId = event.detail.userId;
    this.loadCurrentUser(); // Reload user info when debug user changes
  }

  /**
   * Handle debug LLM config change
   */
  private handleDebugLLMConfigChange(event: CustomEvent) {
    console.log('Debug: LLM config changed to', event.detail.configId);
    this.debugLLMConfigId = event.detail.configId;
  }

  /**
   * Handle debug backend URL change
   */
  private handleDebugBackendUrlChange(event: CustomEvent) {
    console.log('Debug: Backend URL changed to', event.detail.url);
    // Reconnect with new URL
    this.websocketManager.disconnect();
    setTimeout(() => {
      this.initializeApp();
    }, 1000);
  }

  /**
   * Render mobile-friendly profile modal
   */
  private renderProfileModal() {
    if (!this.showProfileModal) return '';

    return html`
      <div class="modal-overlay profile-modal-overlay" @click=${this.closeProfileModal}>
        <div class="modal profile-modal" @click=${(e: Event) => e.stopPropagation()}>
          <div class="modal-header">
            <h3>👤 User Profile</h3>
            <button class="modal-close" @click=${this.closeProfileModal}>×</button>
          </div>

          <div class="modal-body">
            ${this.currentUserProfile ? html`
              <!-- Basic Information & Demographics - Compact Display -->
              <div class="profile-compact-section">
                <div class="profile-compact-header">
                  <h4>
                    <span class="profile-section-icon">👤</span>
                    Basic Information & Demographics
                  </h4>
                </div>
                <div class="profile-compact-grid">
                  <div class="profile-field-compact">
                    <label>Full Name</label>
                    <div class="profile-value-compact">${this.currentUserProfile.demographics?.full_name || this.currentUserProfile.name || 'Not set'}</div>
                  </div>
                  <div class="profile-field-compact">
                    <label>Age</label>
                    <div class="profile-value-compact">${this.currentUserProfile.demographics?.age || 'Not set'}</div>
                  </div>
                  <div class="profile-field-compact">
                    <label>Gender</label>
                    <div class="profile-value-compact">${this.currentUserProfile.demographics?.gender || 'Not set'}</div>
                  </div>
                  <div class="profile-field-compact">
                    <label>Location</label>
                    <div class="profile-value-compact">${this.currentUserProfile.demographics?.location || 'Not set'}</div>
                  </div>
                  <div class="profile-field-compact">
                    <label>Language</label>
                    <div class="profile-value-compact">${this.currentUserProfile.demographics?.language || 'Not set'}</div>
                  </div>
                  <div class="profile-field-compact">
                    <label>Occupation</label>
                    <div class="profile-value-compact">${this.currentUserProfile.demographics?.occupation || 'Not set'}</div>
                  </div>
                  <div class="profile-field-compact profile-field-full-width">
                    <label>Profile Description</label>
                    <div class="profile-value-compact">${this.currentUserProfile.description || 'Not set'}</div>
                  </div>
                </div>
              </div>

              <!-- Goals Section -->
              <div class="profile-section ${this.expandedProfileSections.has('goals') ? 'expanded' : ''}">
                <div class="profile-section-header" @click=${() => this.toggleProfileSection('goals')}>
                  <h4>
                    <span class="profile-section-icon">🎯</span>
                    Goals & Aspirations
                  </h4>
                  <span class="profile-section-toggle">▼</span>
                </div>
                <div class="profile-section-content">
                  <div class="profile-section-body">
                    ${this.currentUserProfile.goals && Array.isArray(this.currentUserProfile.goals) && this.currentUserProfile.goals.length > 0 ?
                      this.currentUserProfile.goals.map((goal: any) => html`
                        <div class="profile-field">
                          <label>${goal.title} (${goal.goal_type})</label>
                          <div class="profile-value">
                            <div class="goal-description">${goal.description}</div>
                            <div class="goal-meta">
                              <span class="goal-importance">Importance: ${goal.importance_according_user}/100</span>
                              <span class="goal-strength">Strength: ${goal.strength}/100</span>
                            </div>
                          </div>
                        </div>
                      `) : html`
                        <div class="profile-field">
                          <label>Goals & Aspirations</label>
                          <div class="profile-value">No goals defined yet</div>
                        </div>
                      `
                    }
                  </div>
                </div>
              </div>

              <!-- Environment Section -->
              <div class="profile-section ${this.expandedProfileSections.has('environment') ? 'expanded' : ''}">
                <div class="profile-section-header" @click=${() => this.toggleProfileSection('environment')}>
                  <h4>
                    <span class="profile-section-icon">🏠</span>
                    Environment & Context
                  </h4>
                  <span class="profile-section-toggle">▼</span>
                </div>
                <div class="profile-section-content">
                  <div class="profile-section-body">
                    ${this.currentUserProfile.environment ? html`
                      <div class="profile-field">
                        <label>Current Environment</label>
                        <div class="profile-value">${this.currentUserProfile.environment.environment_name || 'Not set'}</div>
                      </div>
                      <div class="profile-field">
                        <label>Environment Description</label>
                        <div class="profile-value">${this.currentUserProfile.environment.environment_description || 'Not set'}</div>
                      </div>
                      ${this.currentUserProfile.environment.environment_details ? html`
                        <div class="profile-field">
                          <label>Environment Details</label>
                          <div class="profile-value environment-details">
                            ${Object.entries(this.currentUserProfile.environment.environment_details).map(([key, value]) => html`
                              <div class="detail-item">
                                <span class="detail-key">${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</span>
                                <span class="detail-value">${value}</span>
                              </div>
                            `)}
                          </div>
                        </div>
                      ` : ''}
                    ` : html`
                      <div class="profile-field">
                        <label>Environment & Context</label>
                        <div class="profile-value">No environment information available</div>
                      </div>
                    `}
                  </div>
                </div>
              </div>
            ` : html`
              <div class="loading-state">
                <div class="spinner"></div>
                <p>Loading profile...</p>
              </div>
            `}
          </div>

          <div class="modal-footer">
            <button class="btn btn-secondary" @click=${this.closeProfileModal}>
              Close
            </button>
            <button class="btn btn-primary" @click=${this.openFullProfileEditor}>
              Edit Profile
            </button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Render enjoy overlay
   */
  private renderEnjoyOverlay() {
    if (!this.showEnjoyOverlay || !this.enjoyOverlayActivity) return '';

    // Calculate remaining time
    const now = Date.now();
    const startTime = this.enjoyOverlayStartTime || now;
    const elapsedMs = now - startTime;
    const totalMs = 10 * 60 * 1000; // 10 minutes
    const remainingMs = Math.max(0, totalMs - elapsedMs);
    const remainingMinutes = Math.ceil(remainingMs / (60 * 1000));

    return html`
      <div class="enjoy-overlay">
        <div class="enjoy-overlay-content">
          <h1 class="enjoy-title">Enjoy!</h1>

          <div class="enjoy-activity-info">
            <div class="enjoy-activity-name">
              ${this.enjoyOverlayActivity.icon || '🎯'} ${this.enjoyOverlayActivity.name}
            </div>
            <div class="enjoy-activity-description">
              ${this.enjoyOverlayActivity.description || 'No description available'}
            </div>
          </div>

          <div class="enjoy-activity-details">
            <div class="enjoy-detail-item">
              <div class="enjoy-detail-label">Domain</div>
              <div class="enjoy-detail-value">${this.enjoyOverlayActivity.domain || 'General'}</div>
            </div>
            <div class="enjoy-detail-item">
              <div class="enjoy-detail-label">Challenge</div>
              <div class="enjoy-detail-value">${this.enjoyOverlayActivity.base_challenge_rating || 'N/A'}/100</div>
            </div>
            <div class="enjoy-detail-item">
              <div class="enjoy-detail-label">Duration</div>
              <div class="enjoy-detail-value">${this.enjoyOverlayActivity.duration_range || 'Flexible'}</div>
            </div>
            <div class="enjoy-detail-item">
              <div class="enjoy-detail-label">Type</div>
              <div class="enjoy-detail-value">${this.enjoyOverlayActivity.type === 'tailored' ? '✨ Tailored' : '📋 Generic'}</div>
            </div>
          </div>

          ${this.enjoyOverlayActivity.instructions ? html`
            <div class="enjoy-activity-info">
              <div class="enjoy-detail-label">📝 Instructions:</div>
              <div class="enjoy-activity-description">${this.enjoyOverlayActivity.instructions}</div>
            </div>
          ` : ''}

          ${this.enjoyOverlayActivity.requirements ? html`
            <div class="enjoy-activity-info">
              <div class="enjoy-detail-label">📋 Requirements:</div>
              <div class="enjoy-activity-description">${this.enjoyOverlayActivity.requirements}</div>
            </div>
          ` : ''}

          ${this.enjoyOverlayActivity.materials ? html`
            <div class="enjoy-activity-info">
              <div class="enjoy-detail-label">🛠️ Materials:</div>
              <div class="enjoy-activity-description">${this.enjoyOverlayActivity.materials}</div>
            </div>
          ` : ''}

          <div class="enjoy-hourglass">⏳</div>

          <div class="enjoy-timer">
            ${remainingMinutes > 0 ? `${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''} remaining` : 'Time\'s up!'}
          </div>

          <button class="enjoy-close-btn" @click=${this.resetAppFromEnjoyOverlay}>
            Reset App
          </button>
        </div>
      </div>
    `;
  }

  /**
   * Render post-activity feedback modal
   */
  private renderPostActivityFeedbackModal() {
    if (!this.showPostActivityFeedbackModal) return '';

    const activity = this.postActivityFeedbackConfig.activity;
    if (!activity) return '';

    return html`
      <div class="modal-overlay" @click=${this.closePostActivityFeedbackModal}>
        <div class="modal post-activity-feedback-modal" @click=${(e: Event) => e.stopPropagation()}>
          <div class="modal-header">
            <h3 class="modal-title">${this.postActivityFeedbackConfig.title}</h3>
            <button class="modal-close" @click=${this.closePostActivityFeedbackModal}>×</button>
          </div>
          <div class="modal-body">
            <div class="activity-summary">
              <div class="activity-info">
                <div class="activity-name">
                  ${activity.icon || '🎯'} ${activity.name}
                </div>
                <div class="activity-description">
                  ${activity.description || 'No description available'}
                </div>
              </div>
            </div>

            <p class="feedback-message">${this.postActivityFeedbackConfig.message}</p>

            <div class="feedback-form">
              <!-- Experience Rating -->
              <div class="form-group rating-group">
                <label class="form-label">How was your overall experience?</label>
                <div class="rating-options">
                  <label class="rating-option">
                    <input type="radio" name="experience-rating" value="1">
                    <span class="rating-text">😞 Poor</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="experience-rating" value="2">
                    <span class="rating-text">😐 Fair</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="experience-rating" value="3">
                    <span class="rating-text">🙂 Good</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="experience-rating" value="4">
                    <span class="rating-text">😊 Great</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="experience-rating" value="5">
                    <span class="rating-text">🤩 Excellent</span>
                  </label>
                </div>
              </div>

              <!-- Difficulty Rating -->
              <div class="form-group rating-group">
                <label class="form-label">How challenging was this activity?</label>
                <div class="rating-options">
                  <label class="rating-option">
                    <input type="radio" name="difficulty-rating" value="1">
                    <span class="rating-text">😴 Too Easy</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="difficulty-rating" value="2">
                    <span class="rating-text">😌 Easy</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="difficulty-rating" value="3">
                    <span class="rating-text">😊 Just Right</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="difficulty-rating" value="4">
                    <span class="rating-text">😅 Challenging</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="difficulty-rating" value="5">
                    <span class="rating-text">😰 Too Hard</span>
                  </label>
                </div>
              </div>

              <!-- Enjoyment Rating -->
              <div class="form-group rating-group">
                <label class="form-label">How much did you enjoy it?</label>
                <div class="rating-options">
                  <label class="rating-option">
                    <input type="radio" name="enjoyment-rating" value="1">
                    <span class="rating-text">😒 Boring</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="enjoyment-rating" value="2">
                    <span class="rating-text">😑 Meh</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="enjoyment-rating" value="3">
                    <span class="rating-text">🙂 Okay</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="enjoyment-rating" value="4">
                    <span class="rating-text">😄 Fun</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="enjoyment-rating" value="5">
                    <span class="rating-text">🥳 Amazing</span>
                  </label>
                </div>
              </div>

              <!-- Recommendation Rating -->
              <div class="form-group rating-group">
                <label class="form-label">Would you recommend this activity to others?</label>
                <div class="rating-options">
                  <label class="rating-option">
                    <input type="radio" name="recommendation-rating" value="1">
                    <span class="rating-text">👎 No</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="recommendation-rating" value="2">
                    <span class="rating-text">🤷 Maybe</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="recommendation-rating" value="3">
                    <span class="rating-text">👍 Yes</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="recommendation-rating" value="4">
                    <span class="rating-text">💯 Definitely</span>
                  </label>
                  <label class="rating-option">
                    <input type="radio" name="recommendation-rating" value="5">
                    <span class="rating-text">🌟 Absolutely</span>
                  </label>
                </div>
              </div>

              <!-- Additional Comments -->
              <div class="form-group">
                <label class="form-label" for="post-activity-feedback-comment">Additional thoughts or suggestions:</label>
                <textarea
                  id="post-activity-feedback-comment"
                  class="form-textarea"
                  placeholder="Share any additional thoughts, what you learned, or suggestions for improvement..."
                  rows="4"
                ></textarea>
              </div>
            </div>

            <div class="modal-actions">
              <button type="button" class="btn-secondary" @click=${this.closePostActivityFeedbackModal}>
                Skip Feedback
              </button>
              <button type="button" class="btn-primary" @click=${this.submitPostActivityFeedback}>
                Submit Feedback
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Render winning activity modal
   */
  private renderWinningModal() {
    if (!this.showWinningModal || !this.winningActivity) return '';

    // Get stored signature from contract modal
    const storedSignature = ContractDisclaimerModal.getStoredSignature();

    return html`
      <div class="modal-overlay winning-modal-overlay" @click=${this.closeWinningModal}>
        <div class="modal winning-modal-content" @click=${(e: Event) => e.stopPropagation()}>
          <div class="winning-header">
            <div class="winning-icon">🎉</div>
            <h2>Congratulations!</h2>
            <button class="modal-close" @click=${this.closeWinningModal}>×</button>
          </div>

          ${storedSignature ? html`
            <div class="contract-confirmation">
              <div class="contract-confirmation-header">
                <span class="contract-icon">⚖️</span>
                <span class="contract-message">You have approved this choice</span>
              </div>
              <div class="signature-display">
                <img
                  src="${storedSignature.imageData}"
                  alt="Your signature"
                  class="signature-image"
                />
                <div class="signature-timestamp">
                  Signed: ${new Date(storedSignature.timestamp).toLocaleString()}
                </div>
              </div>
            </div>
          ` : ''}

          <div class="modal-body">
            <div class="winning-activity">
              <!-- Activity Header -->
              <div class="activity-header">
                <div class="activity-icon">${this.winningActivity.icon || '🎯'}</div>
                <div class="activity-title">
                  <div class="activity-name">${this.winningActivity.name || this.winningActivity.text}</div>
                  <div class="activity-type-badge ${this.winningActivity.type || 'generic'}">
                    ${this.winningActivity.type === 'tailored' ? '✨ Tailored' : '📋 Generic'}
                  </div>
                </div>
              </div>

              <!-- Activity Description -->
              <div class="activity-description">
                ${this.winningActivity.description || 'No description available'}
              </div>

              <!-- Activity Instructions (if available) -->
              ${this.winningActivity.instructions ? html`
                <div class="activity-instructions">
                  <h4>📝 Instructions:</h4>
                  <p>${this.winningActivity.instructions}</p>
                </div>
              ` : ''}

              <!-- Tailored Information (if available) -->
              ${this.winningActivity.tailored_description && this.winningActivity.tailored_description !== this.winningActivity.description ? html`
                <div class="tailored-info">
                  <h4>🎯 Personalized for You:</h4>
                  <p>${this.winningActivity.tailored_description}</p>
                </div>
              ` : ''}

              <!-- Activity Details Grid -->
              <div class="activity-details">
                <div class="detail-item">
                  <span class="detail-label">🏷️ Domain:</span>
                  <span class="detail-value">${this.winningActivity.domain || 'General'}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">⚡ Challenge:</span>
                  <span class="detail-value">${this.winningActivity.base_challenge_rating || 'N/A'}/100</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">⏱️ Duration:</span>
                  <span class="detail-value">${this.winningActivity.duration_range || 'Flexible'}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">🎯 Type:</span>
                  <span class="detail-value">${this.winningActivity.type === 'tailored' ? '✨ Tailored' : '📋 Generic'}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">📊 Weight:</span>
                  <span class="detail-value">${this.winningActivity.percentage ? this.winningActivity.percentage.toFixed(1) + '%' : 'N/A'}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">🎲 Spin Time:</span>
                  <span class="detail-value">${this.winningActivity.spinDuration ? (this.winningActivity.spinDuration / 1000).toFixed(1) + 's' : 'N/A'}</span>
                </div>
              </div>

              <!-- Additional Information -->
              ${this.winningActivity.requirements || this.winningActivity.materials ? html`
                <div class="activity-requirements">
                  ${this.winningActivity.requirements ? html`
                    <div class="requirement-section">
                      <h4>📋 Requirements:</h4>
                      <p>${this.winningActivity.requirements}</p>
                    </div>
                  ` : ''}
                  ${this.winningActivity.materials ? html`
                    <div class="requirement-section">
                      <h4>🛠️ Materials:</h4>
                      <p>${this.winningActivity.materials}</p>
                    </div>
                  ` : ''}
                </div>
              ` : ''}

              <!-- Action Button -->
              <div class="winning-actions">
                <button class="primary-button" @click=${this.closeWinningModal}>
                  Let's Do This! 🚀
                </button>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button class="btn btn-primary" @click=${this.closeWinningModal}>
              Let's Do It! 🚀
            </button>
            <button class="btn btn-secondary" @click=${this.closeWinningModal}>
              Maybe Later
            </button>
          </div>
        </div>
      </div>
    `;
  }

  private openFullProfileEditor = () => {
    // Open Django admin user profile management page for full editing
    const adminUrl = `${window.location.origin}/admin/user-profiles/`;
    window.open(adminUrl, '_blank');
    this.closeProfileModal();
  };

  private renderFeedbackModal() {
    if (!this.showFeedbackModal) return '';

    return html`
      <div class="modal-overlay" @click=${this.closeFeedbackModal}>
        <div class="modal" @click=${(e: Event) => e.stopPropagation()}>
          <div class="modal-header">
            <h3 class="modal-title">${this.feedbackModalConfig.title}</h3>
            <button class="modal-close" @click=${this.closeFeedbackModal}>×</button>
          </div>
          <div class="modal-body">
            <p>${this.feedbackModalConfig.message}</p>
            <div class="form-group">
              <label class="form-label" for="feedback-comment">Your feedback:</label>
              <textarea
                id="feedback-comment"
                class="form-textarea"
                placeholder="Tell us why you don't want this activity..."
                rows="4"
              ></textarea>
            </div>
            <div class="modal-actions">
              <button type="button" class="btn-secondary" @click=${this.closeFeedbackModal}>
                ${this.feedbackModalConfig.secondaryButtonLabel}
              </button>
              <button type="button" class="btn-primary" @click=${this.submitFeedback}>
                ${this.feedbackModalConfig.primaryButtonLabel}
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderAddActivityModal() {
    if (!this.showAddActivityModal) return '';

    return html`
      <div class="modal-overlay" @click=${this.closeAddActivityModal}>
        <div class="modal activity-modal" @click=${(e: Event) => e.stopPropagation()}>
          <div class="modal-header">
            <h3 class="modal-title">Add Activity to Wheel</h3>
            <button class="modal-close" @click=${this.closeAddActivityModal}>×</button>
          </div>
          <div class="modal-body">
            <input
              type="text"
              class="search-input"
              placeholder="Search activities..."
              .value=${this.searchQuery}
              @input=${this.handleSearchChange}
            />
            <div class="activity-catalog">
              ${this.isLoading ? html`
                <div class="loading-state">
                  <div class="spinner"></div>
                  <p>Loading activities...</p>
                </div>
              ` : this.filteredCatalog.length > 0 ?
                this.filteredCatalog.map(activity => html`
                  <div class="catalog-item ${activity.type}" @click=${() => this.addActivityToWheel(activity)}>
                    <div class="catalog-item-info">
                      <div class="catalog-item-header">
                        <span class="activity-type-icon ${activity.type}">${activity.type === 'tailored' ? '⭐' : '🎯'}</span>
                        <div class="catalog-item-name">${activity.name}</div>
                        <span class="activity-type-badge ${activity.type}">${activity.type === 'tailored' ? 'Tailored' : 'Generic'}</span>
                      </div>
                      <div class="catalog-item-description">${activity.description}</div>
                      <div class="catalog-item-meta">
                        <span>🎯 ${activity.domain}</span>
                        <span>⚡ ${activity.base_challenge_rating}/100</span>
                      </div>
                    </div>
                  </div>
                `) :
                html`<div class="no-results">No activities found matching "${this.searchQuery}"</div>`
              }
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Get background wheel data for the greyed-out wheel
   */
  private getBackgroundWheelData(): WheelData {
    return {
      segments: [
        {
          id: 'bg-1',
          text: '🏃‍♂️ Exercise',
          percentage: 20,
          color: '#FF6B6B',
          description: 'Physical activity and movement'
        },
        {
          id: 'bg-2',
          text: '📚 Learning',
          percentage: 20,
          color: '#4ECDC4',
          description: 'Study and skill development'
        },
        {
          id: 'bg-3',
          text: '🎨 Creative',
          percentage: 20,
          color: '#45B7D1',
          description: 'Artistic and creative pursuits'
        },
        {
          id: 'bg-4',
          text: '🧘‍♀️ Mindfulness',
          percentage: 20,
          color: '#96CEB4',
          description: 'Meditation and relaxation'
        },
        {
          id: 'bg-5',
          text: '👥 Social',
          percentage: 20,
          color: '#FFEAA7',
          description: 'Connect with friends and family'
        }
      ]
    };
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'app-shell': AppShell;
  }
}
