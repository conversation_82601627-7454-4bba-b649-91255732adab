# backend/apps/main/tests/test_celery_results.py
import pytest
from unittest.mock import patch, MagicMock, call
from apps.main.celery_results import (
    handle_task_success, 
    handle_task_failure, 
    handle_graph_workflow_result, 
    handle_agent_node_result,
    handle_onboarding_result
)

@pytest.mark.test_type("unit")
@pytest.mark.component("main.celery_results")
def test_handle_task_success_with_graph_workflow():
    """Test handling of successful graph workflow task completion."""
    # Create a mock sender (task)
    sender = MagicMock()
    sender.name = "execute_graph_workflow"
    sender.request.id = "test-task-123"
    
    # Create a mock result
    result = {
        "workflow_id": "test-workflow-456",
        "result": {
            "output_data": {
                "user_response": "Here are some activities for you."
            },
            "workflow_type": "wheel_generation",
            "user_ws_session_name": "test-session-789"
        }
    }
    
    # Mock the workflow result handler where it's used (in the celery_results module)
    with patch('apps.main.celery_results.workflow_result_handler') as mock_handler:
        # Call the function
        handle_task_success(sender=sender, result=result)
        
        # Verify the handler's sync_process_result was called via handle_graph_workflow_result
        mock_handler.sync_process_result.assert_called_once_with(
            workflow_id="test-workflow-456",
            result=result["result"],
            task_id="test-task-123",
            workflow_type="wheel_generation"
        )

@pytest.mark.test_type("unit")
@pytest.mark.component("main.celery_results")
def test_handle_task_success_with_agent_node():
    """Test handling of successful agent node task completion."""
    # Create a mock sender (task)
    sender = MagicMock()
    sender.name = "execute_agent_node"
    sender.request.id = "test-task-123"
    
    # Create a mock result with completed state
    result = {
        "output_data": {
            "user_response": "Here are some activities for you."
        },
        "updated_state": {
            "completed": True,
            "workflow_id": "test-workflow-456",
            "input": {
                "task_type": "wheel_generation"
            }
        },
        "run_id": "test-run-789"
    }
    
    # Mock the workflow result handler where it's used
    with patch('apps.main.celery_results.workflow_result_handler') as mock_handler:
        # Call the function
        handle_task_success(sender=sender, result=result)
        
        # Verify the handler's sync_process_result was called via handle_agent_node_result
        mock_handler.sync_process_result.assert_called_once()
        
        # Check the arguments passed to sync_process_result
        call_args = mock_handler.sync_process_result.call_args
        assert call_args is not None, "sync_process_result was not called"
        
        # Extract keyword arguments
        args_dict = call_args.kwargs
        
        assert args_dict.get("workflow_id") == "test-workflow-456"
        assert args_dict.get("task_id") == "test-task-123"
        assert args_dict.get("workflow_type") == "wheel_generation"
        # Check that the 'result' argument passed contains the expected structure
        # handle_agent_node_result wraps the original result
        passed_result = args_dict.get("result")
        assert passed_result is not None
        assert "output_data" in passed_result
        assert "state" in passed_result # It passes the updated_state as 'state'
        assert passed_result["output_data"] == result["output_data"]
        assert passed_result["state"] == result["updated_state"]


@pytest.mark.test_type("unit")
@pytest.mark.component("main.celery_results")
def test_handle_task_success_with_onboarding():
    """Test handling of successful onboarding task completion."""
    # Create a mock sender (task)
    sender = MagicMock()
    sender.name = "execute_onboarding_workflow"
    sender.request.id = "test-task-123"
    
    # Create a mock result with mentor response
    result = {
        "mentor_response": "Welcome to the Game of Life!",
        "success": True
    }
    
    # Mock the channel layer and async_to_sync
    with patch('apps.main.celery_results.get_channel_layer') as mock_get_channel_layer, \
         patch('apps.main.celery_results.async_to_sync') as mock_async_to_sync:
        
        # Configure mocks
        mock_channel_layer = MagicMock()
        mock_get_channel_layer.return_value = mock_channel_layer
        
        mock_group_send = MagicMock()
        mock_async_to_sync.return_value = mock_group_send
        
        # Call the function
        handle_task_success(sender=sender, result=result)
        
        # Verify channel layer send was called correctly
        mock_group_send.assert_called_once_with(
            'game',
            {
                'type': 'onboarding_response',
                'mentor_response': "Welcome to the Game of Life!",
                'task_id': "test-task-123",
                'status': 'completed'
            }
        )

@pytest.mark.test_type("unit")
@pytest.mark.component("main.celery_results")
def test_handle_task_failure():
    """Test handling of task failure."""
    # Create a mock sender (task)
    sender = MagicMock()
    sender.name = "execute_graph_workflow"
    sender.request.id = "test-task-123"
    sender.request.kwargs = {
        "workflow_id": "test-workflow-456",
        "workflow_type": "wheel_generation",
        "state": {
            "workflow_id": "test-workflow-456"
        }
    }
    
    # Create a mock exception
    exception = ValueError("Test error message")
    traceback_str = "Traceback (most recent call last):\n  File \"<stdin>\", line 1, in <module>\nValueError: Test error message" # Example traceback

    # Mock the workflow result handler where it's used and logger
    with patch('apps.main.celery_results.workflow_result_handler') as mock_handler, \
         patch('apps.main.celery_results.logger') as mock_logger:
        
        # Call the function
        handle_task_failure(sender=sender, exception=exception, task_id="test-task-123", traceback=traceback_str)
        
        # Verify error was logged
        mock_logger.error.assert_called()
        
        # Verify workflow result handler's sync_handle_error was called
        mock_handler.sync_handle_error.assert_called_once()
        
        # Check arguments passed to sync_handle_error
        call_args = mock_handler.sync_handle_error.call_args
        assert call_args is not None, "sync_handle_error was not called"
        
        args_dict = call_args.kwargs
        assert args_dict.get("workflow_id") == "test-workflow-456"
        assert args_dict.get("task_id") == "test-task-123"
        assert args_dict.get("workflow_type") == "wheel_generation"
        assert args_dict.get("error") == str(exception)
        # Check user_ws_session (might be None if not found in state)
        assert "user_ws_session" in args_dict # Check key exists


@pytest.mark.test_type("unit")
@pytest.mark.component("main.celery_results")
def test_handle_graph_workflow_result_nested_format():
    """Test handling of graph workflow result with nested format."""
    # Create a mock task ID
    task_id = "test-task-123"

    # Create a mock result with nested format
    result = {
        "workflow_id": "test-workflow-456",
        "result": {
            "output_data": {
                "user_response": "Here are some activities for you."
            },
            "workflow_type": "wheel_generation",
            "user_ws_session_name": "test-session-789"
        }
    }

    # Mock the workflow result handler where it's used
    with patch('apps.main.celery_results.workflow_result_handler') as mock_handler:
        # Call the function
        handle_graph_workflow_result(result, task_id)

        # Verify workflow result handler's sync_process_result was called correctly
        mock_handler.sync_process_result.assert_called_once_with(
            workflow_id="test-workflow-456",
            result=result["result"],
            task_id=task_id,
            workflow_type="wheel_generation"
        )

@pytest.mark.test_type("unit")
@pytest.mark.component("main.celery_results")
def test_handle_graph_workflow_result_direct_format():
    """Test handling of graph workflow result with direct format (the fix)."""
    # Create a mock task ID
    task_id = "test-task-123"

    # Create a mock result with direct format (no nested 'result' key)
    result = {
        "workflow_id": "test-workflow-456",
        "output_data": {
            "user_response": "Here are some activities for you."
        },
        "workflow_type": "wheel_generation",
        "user_ws_session_name": "test-session-789"
    }

    # Mock the workflow result handler where it's used
    with patch('apps.main.celery_results.workflow_result_handler') as mock_handler:
        # Call the function
        handle_graph_workflow_result(result, task_id)

        # Verify workflow result handler's sync_process_result was called correctly
        # For direct format, the entire result should be passed as workflow_result
        mock_handler.sync_process_result.assert_called_once_with(
            workflow_id="test-workflow-456",
            result=result,  # The entire result, not result["result"]
            task_id=task_id,
            workflow_type="wheel_generation"
        )

@pytest.mark.test_type("unit")
@pytest.mark.component("main.celery_results")
def test_handle_agent_node_result():
    """Test handling of agent node result."""
    # Create a mock task ID
    task_id = "test-task-123"
    
    # Create a mock result with completed state
    result = {
        "output_data": {
            "user_response": "Here are some activities for you."
        },
        "updated_state": {
            "completed": True,
            "workflow_id": "test-workflow-456",
            "input": {
                "task_type": "wheel_generation"
            }
        },
        "run_id": "test-run-789"
    }
    
    # Mock the workflow result handler where it's used
    with patch('apps.main.celery_results.workflow_result_handler') as mock_handler:
        # Call the function
        handle_agent_node_result(result, task_id)
        
        # Verify workflow result handler's sync_process_result was called correctly
        mock_handler.sync_process_result.assert_called_once()
        
        # Check arguments passed to sync_process_result
        call_args = mock_handler.sync_process_result.call_args
        assert call_args is not None, "sync_process_result was not called"
        
        args_dict = call_args.kwargs
        assert args_dict.get("workflow_id") == "test-workflow-456"
        assert args_dict.get("task_id") == task_id
        assert args_dict.get("workflow_type") == "wheel_generation"
        # Check that the 'result' argument passed contains the expected structure
        passed_result = args_dict.get("result")
        assert passed_result is not None
        assert "output_data" in passed_result
        assert "state" in passed_result # It passes the updated_state as 'state'
        assert passed_result["output_data"] == result["output_data"]
        assert passed_result["state"] == result["updated_state"]


@pytest.mark.test_type("unit")
@pytest.mark.component("main.celery_results")
def test_handle_onboarding_result():
    """Test handling of onboarding result."""
    # Create a mock task ID
    task_id = "test-task-123"
    
    # Create a mock result
    result = {
        "mentor_response": "Welcome to the Game of Life!",
        "success": True
    }
    
    # Mock the channel layer
    with patch('apps.main.celery_results.get_channel_layer') as mock_get_channel_layer, \
         patch('apps.main.celery_results.async_to_sync') as mock_async_to_sync:
        
        # Configure mocks
        mock_channel_layer = MagicMock()
        mock_get_channel_layer.return_value = mock_channel_layer
        
        mock_group_send = MagicMock()
        mock_async_to_sync.return_value = mock_group_send
        
        # Call the function
        handle_onboarding_result(result, task_id)
        
        # Verify channel layer was used correctly
        mock_get_channel_layer.assert_called_once()
        
        # Verify message was sent to game group
        mock_group_send.assert_called_once_with(
            'game',
            {
                'type': 'onboarding_response',
                'mentor_response': "Welcome to the Game of Life!",
                'task_id': task_id,
                'status': 'completed'
            }
        )
