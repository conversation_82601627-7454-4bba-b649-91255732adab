"""
Test workflow benchmark fixes for:
1. OrchestratorAgent tool_registry parameter error
2. Workflow benchmark display issues
3. Error handling and display
"""
from unittest.mock import Mock
from django.test import TestCase
from django.contrib.auth.models import User

from apps.main.models import BenchmarkScenario, GenericAgent, BenchmarkRun, LLMConfig
from tests.factories import GenericAgentFactory
from apps.main.graphs.wheel_generation_graph import _configure_agent_for_execution_mode
from apps.admin_tools.benchmark.views import _determine_execution_type


class TestWorkflowBenchmarkFixes(TestCase):
    """Test fixes for workflow benchmark issues."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(username='testuser', password='testpass')

        # Create a workflow scenario
        self.workflow_scenario = BenchmarkScenario.objects.create(
            name="Test Workflow Scenario",
            description="Test scenario for workflow benchmarks",
            agent_role="orchestrator",
            input_data={"user_message": "Test message"},
            metadata={"expected_response": "Test response", "workflow_type": "wheel_generation"}
        )

        # Create an LLM config
        self.llm_config = LLMConfig.objects.create(
            name="test-gpt-4",
            model_name="gpt-4",
            temperature=0.7
        )

        # Create an agent definition
        self.agent_def = GenericAgentFactory(
            description="Test orchestrator agent",
            system_instructions="Test instructions",
            input_schema={"type": "object"},
            output_schema={"type": "object"},
            langgraph_node_class="test.TestNode",
            llm_config=self.llm_config
        )

    def test_configure_agent_for_execution_mode_no_tool_registry(self):
        """Test that _configure_agent_for_execution_mode doesn't pass tool_registry parameter."""
        # Create a mock state
        mock_state = Mock()
        mock_state.use_real_tools = False
        mock_state.mock_tools = {"test_tool": Mock()}
        mock_state.use_real_llm = False
        mock_state.use_real_db = False

        # Mock the async function to be synchronous for testing
        async def mock_configure_agent():
            return await _configure_agent_for_execution_mode(mock_state, "OrchestratorAgent", "test_user_id")

        # Run the async function
        import asyncio
        agent_kwargs = asyncio.run(mock_configure_agent())

        # Verify that tool_registry is not in the kwargs
        self.assertNotIn('tool_registry', agent_kwargs)

        # Test with real tools
        mock_state.use_real_tools = True
        mock_state.mock_tools = None

        async def mock_configure_agent_real():
            return await _configure_agent_for_execution_mode(mock_state, "OrchestratorAgent", "test_user_id")

        agent_kwargs = asyncio.run(mock_configure_agent_real())
        self.assertNotIn('tool_registry', agent_kwargs)

    def test_determine_execution_type_workflow(self):
        """Test that _determine_execution_type correctly identifies workflow benchmarks."""
        # Create a workflow benchmark run
        workflow_run = BenchmarkRun.objects.create(
            scenario=self.workflow_scenario,
            agent_definition=self.agent_def,
            agent_version="test",
            parameters={"workflow_type": "wheel_generation"},
            raw_results={},
            runs_count=1,
            mean_duration=100.0,
            median_duration=100.0,
            min_duration=100.0,
            max_duration=100.0,
            std_dev=0.0,
            success_rate=1.0
        )
        
        execution_type = _determine_execution_type(workflow_run)
        self.assertEqual(execution_type, "Workflow (wheel_generation)")

    def test_determine_execution_type_agent(self):
        """Test that _determine_execution_type correctly identifies agent benchmarks."""
        # Create an agent scenario (no workflow_type)
        agent_scenario = BenchmarkScenario.objects.create(
            name="Test Agent Scenario",
            description="Test scenario for agent benchmarks",
            agent_role="assistant",
            input_data={"user_message": "Test message"},
            metadata={"expected_response": "Test response"}
        )
        
        # Create an agent benchmark run
        agent_run = BenchmarkRun.objects.create(
            scenario=agent_scenario,
            agent_definition=self.agent_def,
            agent_version="test",
            parameters={},
            raw_results={},
            runs_count=1,
            mean_duration=100.0,
            median_duration=100.0,
            min_duration=100.0,
            max_duration=100.0,
            std_dev=0.0,
            success_rate=1.0
        )
        
        execution_type = _determine_execution_type(agent_run)
        self.assertEqual(execution_type, "Agent Evaluation")

    def test_benchmark_run_view_includes_execution_type(self):
        """Test that BenchmarkRunView list response includes execution_type."""
        # This test verifies that the execution_type field is included in the API response
        # We'll test this by checking that the _determine_execution_type function works correctly
        # and that the field is properly included in the response structure

        # Create a benchmark run
        benchmark_run = BenchmarkRun.objects.create(
            scenario=self.workflow_scenario,
            agent_definition=self.agent_def,
            agent_version="test",
            parameters={"workflow_type": "wheel_generation"},
            raw_results={},
            runs_count=1,
            mean_duration=100.0,
            median_duration=100.0,
            min_duration=100.0,
            max_duration=100.0,
            std_dev=0.0,
            success_rate=1.0
        )

        # Test that _determine_execution_type works correctly
        from apps.admin_tools.benchmark.views import _determine_execution_type
        execution_type = _determine_execution_type(benchmark_run)
        self.assertEqual(execution_type, "Workflow (wheel_generation)")

        # Verify that the execution_type would be included in the response
        # by checking the structure that would be created
        expected_fields = [
            'id', 'scenario_id', 'scenario_name', 'agent_role', 'execution_date',
            'execution_type', 'success_rate', 'semantic_score', 'parameters'
        ]

        # This simulates what the view would do
        run_data = {
            'id': benchmark_run.id,
            'scenario_id': benchmark_run.scenario_id,
            'scenario_name': benchmark_run.scenario.name,
            'agent_role': benchmark_run.agent_definition.role,
            'execution_date': benchmark_run.execution_date.isoformat(),
            'execution_type': execution_type,
            'success_rate': benchmark_run.success_rate,
            'semantic_score': benchmark_run.semantic_score,
            'parameters': benchmark_run.parameters,
        }

        # Verify all expected fields are present
        for field in expected_fields:
            self.assertIn(field, run_data)

        # Verify execution_type has the correct value
        self.assertEqual(run_data['execution_type'], "Workflow (wheel_generation)")

    def test_error_storage_format(self):
        """Test that errors are stored in the correct format for frontend display."""
        # Create a benchmark run with errors
        error_data = {
            "errors": [
                {
                    "type": "critical",
                    "level": "error", 
                    "message": "OrchestratorAgent.__init__() got an unexpected keyword argument 'tool_registry'",
                    "source": "workflow_benchmark",
                    "timestamp": "2025-01-31T12:00:00.123Z",
                    "details": {
                        "error_type": "TypeError",
                        "traceback": "Traceback...",
                        "context": {}
                    }
                }
            ]
        }
        
        benchmark_run = BenchmarkRun.objects.create(
            scenario=self.workflow_scenario,
            agent_definition=self.agent_def,
            agent_version="test",
            parameters={"workflow_type": "wheel_generation"},
            raw_results=error_data,
            runs_count=1,
            mean_duration=100.0,
            median_duration=100.0,
            min_duration=100.0,
            max_duration=100.0,
            std_dev=0.0,
            success_rate=0.0  # Failed run
        )
        
        # Verify the error format
        self.assertIn('errors', benchmark_run.raw_results)
        errors = benchmark_run.raw_results['errors']
        self.assertTrue(len(errors) > 0)
        
        error = errors[0]
        self.assertIn('type', error)
        self.assertIn('level', error)
        self.assertIn('message', error)
        self.assertIn('source', error)
        self.assertIn('timestamp', error)
        self.assertIn('details', error)
