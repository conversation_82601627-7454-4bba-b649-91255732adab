"""
Agent Communication Tracker

This module provides functionality to track and capture agent-to-agent communications
during workflow execution for benchmarking purposes. It records the exact objects
transmitted between agents, state transitions, and timing information.

Enhanced for Phase 1 & Phase 2 Data Model Enhancement:
- Detailed LLM interaction logging
- Enhanced execution context tracking
- Comprehensive error context capture
- Performance metrics and debug metadata
- Real-time state tracking with consistency validation (Phase 2)
- Tool call sequence analysis and performance correlation (Phase 2)
- Advanced monitoring capabilities (Phase 2)
"""

import time
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from copy import deepcopy

logger = logging.getLogger(__name__)


@dataclass
class LLMInteraction:
    """Represents a single LLM interaction during agent execution."""
    interaction_id: str
    agent: str
    model: str
    prompt: str
    response: str
    token_usage: Dict[str, int]  # {"input": int, "output": int}
    temperature: float
    timestamp: str
    duration_ms: float
    success: bool = True
    error: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'interaction_id': self.interaction_id,
            'agent': self.agent,
            'model': self.model,
            'prompt': self.prompt,
            'response': self.response,
            'token_usage': self.token_usage,
            'temperature': self.temperature,
            'timestamp': self.timestamp,
            'duration_ms': self.duration_ms,
            'success': self.success,
            'error': self.error
        }


@dataclass
class ToolCallContext:
    """Represents a tool call with full execution context."""
    tool_name: str
    tool_input: Dict[str, Any]
    tool_output: Dict[str, Any]
    execution_mode: str  # "real" or "mock"
    timestamp: str
    duration_ms: float
    success: bool = True
    error: Optional[str] = None
    call_id: Optional[str] = None  # Store the UUID for reference

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.call_id,
            'tool_name': self.tool_name,
            'tool_input': self.tool_input,
            'tool_output': self.tool_output,
            'execution_mode': self.execution_mode,
            'timestamp': self.timestamp,
            'duration_ms': self.duration_ms,
            'success': self.success,
            'error': self.error
        }


@dataclass
class ErrorContext:
    """Represents detailed error context information."""
    error_id: str
    error_type: str
    level: str  # "error", "warning", "info"
    agent: str
    stage: str
    message: str
    context: Dict[str, Any]
    timestamp: str
    resolution: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'error_id': self.error_id,
            'error_type': self.error_type,
            'level': self.level,
            'agent': self.agent,
            'stage': self.stage,
            'message': self.message,
            'context': self.context,
            'timestamp': self.timestamp,
            'resolution': self.resolution
        }


@dataclass
class AgentCommunication:
    """Represents a single agent communication event with enhanced debugging context."""
    agent: str
    stage: str
    input_data: Dict[str, Any]
    output_data: Dict[str, Any]
    timestamp: str
    duration_ms: float
    success: bool = True
    error: Optional[str] = None

    # Phase 1 Enhancements
    execution_context: Dict[str, Any] = field(default_factory=dict)
    performance_metrics: Dict[str, Any] = field(default_factory=dict)
    debug_metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'agent': self.agent,
            'stage': self.stage,
            'input': self.input_data,
            'output': self.output_data,
            'timestamp': self.timestamp,
            'duration_ms': self.duration_ms,
            'success': self.success,
            'error': self.error,
            'execution_context': self.execution_context,
            'performance_metrics': self.performance_metrics,
            'debug_metadata': self.debug_metadata
        }


@dataclass
class StateTransition:
    """Represents a workflow state transition."""
    from_state: Dict[str, Any]
    to_state: Dict[str, Any]
    agent: str
    timestamp: str

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'from_state': self.from_state,
            'to_state': self.to_state,
            'agent': self.agent,
            'timestamp': self.timestamp
        }


# Phase 2 Enhancements: Advanced Monitoring Data Structures

@dataclass
class StateSnapshot:
    """Represents a complete state snapshot at a specific point in time."""
    snapshot_id: str
    timestamp: str
    agent: str
    stage: str
    state_data: Dict[str, Any]
    state_hash: str  # For consistency validation
    validation_status: str  # "valid", "inconsistent", "warning"
    validation_details: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'snapshot_id': self.snapshot_id,
            'timestamp': self.timestamp,
            'agent': self.agent,
            'stage': self.stage,
            'state_data': self.state_data,
            'state_hash': self.state_hash,
            'validation_status': self.validation_status,
            'validation_details': self.validation_details
        }


@dataclass
class ToolCallSequence:
    """Represents a sequence of related tool calls with analysis."""
    sequence_id: str
    agent: str
    tool_calls: List[str]  # List of tool call IDs
    start_timestamp: str
    end_timestamp: str
    total_duration_ms: float
    sequence_type: str  # "sequential", "parallel", "dependent"
    performance_impact: Dict[str, Any]
    dependencies: List[str]  # Dependencies between tool calls

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'sequence_id': self.sequence_id,
            'agent': self.agent,
            'tool_calls': self.tool_calls,
            'start_timestamp': self.start_timestamp,
            'end_timestamp': self.end_timestamp,
            'total_duration_ms': self.total_duration_ms,
            'sequence_type': self.sequence_type,
            'performance_impact': self.performance_impact,
            'dependencies': self.dependencies
        }


@dataclass
class PerformanceCorrelation:
    """Represents performance correlation analysis between components."""
    correlation_id: str
    component_a: str  # e.g., "llm_interaction", "tool_call", "agent_execution"
    component_b: str
    correlation_coefficient: float
    correlation_type: str  # "positive", "negative", "none"
    significance_level: float
    analysis_details: Dict[str, Any]
    timestamp: str

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'correlation_id': self.correlation_id,
            'component_a': self.component_a,
            'component_b': self.component_b,
            'correlation_coefficient': self.correlation_coefficient,
            'correlation_type': self.correlation_type,
            'significance_level': self.significance_level,
            'analysis_details': self.analysis_details,
            'timestamp': self.timestamp
        }


class AgentCommunicationTracker:
    """
    Tracks agent communications and state transitions during workflow execution.

    Enhanced for Phase 1 & Phase 2 Data Model Enhancement:
    - Agent input/output pairs with execution context
    - State transitions between agents
    - Detailed LLM interaction logging
    - Tool call context tracking
    - Enhanced error context capture
    - Performance metrics and debug metadata
    - Complete workflow execution trace

    Phase 2 Advanced Monitoring:
    - Real-time state tracking with consistency validation
    - Tool call sequence analysis and performance correlation
    - Advanced monitoring capabilities
    - State snapshot management
    - Performance correlation analysis
    """

    def __init__(self, workflow_id: str, enabled: bool = True):
        """
        Initialize the communication tracker.

        Args:
            workflow_id: Unique identifier for the workflow being tracked
            enabled: Whether tracking is enabled (for performance control)
        """
        self.workflow_id = workflow_id
        self.enabled = enabled
        self.communications: List[AgentCommunication] = []
        self.state_transitions: List[StateTransition] = []
        self._current_agent_start_time: Optional[float] = None
        self._current_agent: Optional[str] = None
        self._previous_state: Optional[Dict[str, Any]] = None

        # Phase 1 Enhancements
        self.llm_interactions: List[LLMInteraction] = []
        self.tool_calls: List[ToolCallContext] = []
        self.error_contexts: List[ErrorContext] = []
        self._current_execution_context: Dict[str, Any] = {}
        self._current_agent_llm_interactions: List[str] = []  # Track interaction IDs for current agent
        self._current_agent_tool_calls: List[str] = []  # Track tool call IDs for current agent

        # Phase 2 Advanced Monitoring
        self.state_snapshots: List[StateSnapshot] = []
        self.tool_call_sequences: List[ToolCallSequence] = []
        self.performance_correlations: List[PerformanceCorrelation] = []
        self._state_consistency_enabled: bool = True
        self._performance_monitoring_enabled: bool = True
        self._current_tool_sequence: Optional[Dict[str, Any]] = None
        
    def start_agent_execution(self, agent_name: str, input_data: Dict[str, Any], 
                            current_state: Optional[Dict[str, Any]] = None) -> str:
        """
        Mark the start of an agent execution.
        
        Args:
            agent_name: Name of the agent being executed
            input_data: Input data passed to the agent
            current_state: Current workflow state
            
        Returns:
            Execution ID for tracking this specific agent run
        """
        if not self.enabled:
            return str(uuid.uuid4())
            
        execution_id = str(uuid.uuid4())
        self._current_agent = agent_name
        self._current_agent_start_time = time.monotonic()

        # Record state transition if we have a previous state
        if self._previous_state is not None and current_state is not None:
            transition = StateTransition(
                from_state=deepcopy(self._previous_state),
                to_state=deepcopy(current_state),
                agent=agent_name,
                timestamp=datetime.now().isoformat()
            )
            self.state_transitions.append(transition)

        # Store current state for next transition
        if current_state is not None:
            self._previous_state = deepcopy(current_state)

        # Phase 1 Enhancement: Initialize execution context for this agent
        self._current_execution_context = {
            'input_data': deepcopy(input_data),
            'processing_steps': [],
            'llm_interactions': [],
            'tool_calls': [],
            'decision_points': [],
            'execution_mode': {}
        }
        self._current_agent_llm_interactions = []
        self._current_agent_tool_calls = []

        logger.debug(f"Started tracking agent execution: {agent_name} (ID: {execution_id})")
        return execution_id
        
    def end_agent_execution(self, execution_id: str, agent_name: str,
                          input_data: Dict[str, Any], output_data: Dict[str, Any],
                          stage: str = "unknown", success: bool = True,
                          error: Optional[str] = None) -> None:
        """
        Mark the end of an agent execution and record the communication.

        Args:
            execution_id: Execution ID from start_agent_execution
            agent_name: Name of the agent that was executed
            input_data: Input data that was passed to the agent
            output_data: Output data produced by the agent
            stage: Workflow stage identifier
            success: Whether the execution was successful
            error: Error message if execution failed
        """
        if not self.enabled:
            return

        # Calculate duration
        duration_ms = 0.0
        if (self._current_agent_start_time is not None and
            self._current_agent == agent_name):
            duration_ms = (time.monotonic() - self._current_agent_start_time) * 1000

        # Phase 1 Enhancement: Build execution context
        execution_context = deepcopy(self._current_execution_context)
        execution_context['llm_interactions'] = [
            interaction_id for interaction_id in self._current_agent_llm_interactions
        ]
        execution_context['tool_calls'] = [
            tool_call_id for tool_call_id in self._current_agent_tool_calls
        ]

        # Build performance metrics
        performance_metrics = {
            'duration_ms': duration_ms,
            'token_usage': self._calculate_agent_token_usage(),
            'tool_call_count': len(self._current_agent_tool_calls),
            'success': success
        }

        # Build debug metadata
        debug_metadata = {
            'execution_id': execution_id,
            'llm_interaction_count': len(self._current_agent_llm_interactions),
            'processing_steps_count': len(execution_context.get('processing_steps', [])),
            'decision_points_count': len(execution_context.get('decision_points', []))
        }

        # Create enhanced communication record
        communication = AgentCommunication(
            agent=agent_name,
            stage=stage,
            input_data=deepcopy(input_data),
            output_data=deepcopy(output_data),
            timestamp=datetime.now().isoformat(),
            duration_ms=duration_ms,
            success=success,
            error=error,
            execution_context=execution_context,
            performance_metrics=performance_metrics,
            debug_metadata=debug_metadata
        )

        self.communications.append(communication)

        # Reset current agent tracking
        self._current_agent = None
        self._current_agent_start_time = None
        self._current_execution_context = {}
        self._current_agent_llm_interactions = []
        self._current_agent_tool_calls = []

        logger.debug(f"Recorded enhanced agent communication: {agent_name} -> {len(output_data)} output fields, "
                    f"{len(self._current_agent_llm_interactions)} LLM interactions, "
                    f"{len(self._current_agent_tool_calls)} tool calls")

    def record_llm_interaction(self, agent: str, model: str, prompt: str, response: str,
                              token_usage: Dict[str, int], temperature: float,
                              duration_ms: float, success: bool = True,
                              error: Optional[str] = None) -> str:
        """
        Record an LLM interaction during agent execution.

        Args:
            agent: Name of the agent making the LLM call
            model: LLM model used
            prompt: Full prompt sent to LLM
            response: Raw response received from LLM
            token_usage: Token usage {"input": int, "output": int}
            temperature: Temperature parameter used
            duration_ms: Duration of the LLM call in milliseconds
            success: Whether the LLM call was successful
            error: Error message if the call failed

        Returns:
            Interaction ID for reference
        """
        if not self.enabled:
            return str(uuid.uuid4())

        interaction_id = str(uuid.uuid4())

        llm_interaction = LLMInteraction(
            interaction_id=interaction_id,
            agent=agent,
            model=model,
            prompt=prompt,
            response=response,
            token_usage=token_usage,
            temperature=temperature,
            timestamp=datetime.now().isoformat(),
            duration_ms=duration_ms,
            success=success,
            error=error
        )

        self.llm_interactions.append(llm_interaction)

        # Track this interaction for the current agent
        if self._current_agent == agent:
            self._current_agent_llm_interactions.append(interaction_id)

        logger.debug(f"Recorded LLM interaction: {agent} -> {model} ({token_usage.get('input', 0)}+{token_usage.get('output', 0)} tokens)")
        return interaction_id

    def record_tool_call(self, tool_name: str, tool_input: Dict[str, Any],
                        tool_output: Dict[str, Any], execution_mode: str,
                        duration_ms: float, success: bool = True,
                        error: Optional[str] = None) -> str:
        """
        Record a tool call during agent execution.

        Args:
            tool_name: Name of the tool called
            tool_input: Input parameters passed to the tool
            tool_output: Output returned by the tool
            execution_mode: "real" or "mock"
            duration_ms: Duration of the tool call in milliseconds
            success: Whether the tool call was successful
            error: Error message if the call failed

        Returns:
            Tool call ID for reference
        """
        if not self.enabled:
            return str(uuid.uuid4())

        tool_call_id = str(uuid.uuid4())

        tool_call = ToolCallContext(
            tool_name=tool_name,
            tool_input=deepcopy(tool_input),
            tool_output=deepcopy(tool_output),
            execution_mode=execution_mode,
            timestamp=datetime.now().isoformat(),
            duration_ms=duration_ms,
            success=success,
            error=error,
            call_id=tool_call_id
        )

        self.tool_calls.append(tool_call)

        # Track this tool call for the current agent
        if self._current_agent:
            self._current_agent_tool_calls.append(tool_call_id)

        logger.debug(f"Recorded tool call: {tool_name} ({execution_mode} mode) -> {success}")
        return tool_call_id

    def get_tool_call_details(self, tool_call_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about a specific tool call by its ID.

        Args:
            tool_call_id: UUID of the tool call to retrieve

        Returns:
            Dictionary with tool call details or None if not found
        """
        if not self.enabled:
            return None

        # Find the tool call by ID
        for tool_call in self.tool_calls:
            if tool_call.call_id == tool_call_id:
                return tool_call.to_dict()

        # If not found, return None so the caller can handle it appropriately
        logger.warning(f"Tool call with ID {tool_call_id} not found in tracker")
        return None

    def record_error_context(self, error_type: str, level: str, agent: str,
                           stage: str, message: str, context: Dict[str, Any],
                           resolution: Optional[str] = None) -> str:
        """
        Record detailed error context information.

        Args:
            error_type: Type of error (e.g., "llm_failure", "tool_error", "validation_error")
            level: Error level ("error", "warning", "info")
            agent: Agent where the error occurred
            stage: Stage where the error occurred
            message: Error message
            context: Additional context information
            resolution: How the error was resolved (if applicable)

        Returns:
            Error ID for reference
        """
        if not self.enabled:
            return str(uuid.uuid4())

        error_id = str(uuid.uuid4())

        error_context = ErrorContext(
            error_id=error_id,
            error_type=error_type,
            level=level,
            agent=agent,
            stage=stage,
            message=message,
            context=deepcopy(context),
            timestamp=datetime.now().isoformat(),
            resolution=resolution
        )

        self.error_contexts.append(error_context)
        logger.debug(f"Recorded error context: {error_type} in {agent} -> {level}")
        return error_id

    def add_processing_step(self, step_description: str, step_data: Dict[str, Any]) -> None:
        """
        Add a processing step to the current agent's execution context.

        Args:
            step_description: Description of the processing step
            step_data: Data associated with the step
        """
        if not self.enabled or not self._current_execution_context:
            return

        step = {
            'description': step_description,
            'data': deepcopy(step_data),
            'timestamp': datetime.now().isoformat()
        }

        self._current_execution_context['processing_steps'].append(step)
        logger.debug(f"Added processing step: {step_description}")

    def add_decision_point(self, decision_description: str, options: List[str],
                          chosen_option: str, reasoning: str) -> None:
        """
        Add a decision point to the current agent's execution context.

        Args:
            decision_description: Description of the decision being made
            options: Available options
            chosen_option: Option that was chosen
            reasoning: Reasoning for the choice
        """
        if not self.enabled or not self._current_execution_context:
            return

        decision = {
            'description': decision_description,
            'options': options,
            'chosen_option': chosen_option,
            'reasoning': reasoning,
            'timestamp': datetime.now().isoformat()
        }

        self._current_execution_context['decision_points'].append(decision)
        logger.debug(f"Added decision point: {decision_description} -> {chosen_option}")

    def _calculate_agent_token_usage(self) -> Dict[str, int]:
        """Calculate total token usage for the current agent."""
        total_input = 0
        total_output = 0

        for interaction_id in self._current_agent_llm_interactions:
            # Find the interaction
            for interaction in self.llm_interactions:
                if interaction.interaction_id == interaction_id:
                    total_input += interaction.token_usage.get('input', 0)
                    total_output += interaction.token_usage.get('output', 0)
                    break

        return {'input': total_input, 'output': total_output}

    def record_state_transition(self, from_state: Dict[str, Any],
                              to_state: Dict[str, Any], agent: str) -> None:
        """
        Manually record a state transition.

        Args:
            from_state: Previous workflow state
            to_state: New workflow state
            agent: Agent responsible for the transition
        """
        if not self.enabled:
            return

        transition = StateTransition(
            from_state=deepcopy(from_state),
            to_state=deepcopy(to_state),
            agent=agent,
            timestamp=datetime.now().isoformat()
        )

        self.state_transitions.append(transition)
        logger.debug(f"Recorded state transition by {agent}")
        
    def get_communication_summary(self) -> Dict[str, Any]:
        """
        Get a summary of all tracked communications with Phase 1 enhancements.

        Returns:
            Dictionary containing communication summary statistics
        """
        if not self.enabled:
            return {'enabled': False}

        total_duration = sum(comm.duration_ms for comm in self.communications)
        successful_comms = sum(1 for comm in self.communications if comm.success)

        # Phase 1 Enhancement: Add LLM and tool call statistics
        total_llm_interactions = len(self.llm_interactions)
        successful_llm_interactions = sum(1 for interaction in self.llm_interactions if interaction.success)
        total_tool_calls = len(self.tool_calls)
        successful_tool_calls = sum(1 for tool_call in self.tool_calls if tool_call.success)
        total_errors = len(self.error_contexts)

        # Calculate total token usage
        total_input_tokens = sum(interaction.token_usage.get('input', 0) for interaction in self.llm_interactions)
        total_output_tokens = sum(interaction.token_usage.get('output', 0) for interaction in self.llm_interactions)

        return {
            'enabled': True,
            'workflow_id': self.workflow_id,
            'total_communications': len(self.communications),
            'successful_communications': successful_comms,
            'failed_communications': len(self.communications) - successful_comms,
            'total_duration_ms': total_duration,
            'state_transitions': len(self.state_transitions),
            'agents_involved': list(set(comm.agent for comm in self.communications)),
            # Phase 1 Enhancements
            'llm_interactions': {
                'total': total_llm_interactions,
                'successful': successful_llm_interactions,
                'failed': total_llm_interactions - successful_llm_interactions,
                'total_input_tokens': total_input_tokens,
                'total_output_tokens': total_output_tokens
            },
            'tool_calls': {
                'total': total_tool_calls,
                'successful': successful_tool_calls,
                'failed': total_tool_calls - successful_tool_calls,
                'real_mode_calls': sum(1 for tc in self.tool_calls if tc.execution_mode == 'real'),
                'mock_mode_calls': sum(1 for tc in self.tool_calls if tc.execution_mode == 'mock')
            },
            'error_contexts': {
                'total': total_errors,
                'by_level': {
                    'error': sum(1 for ec in self.error_contexts if ec.level == 'error'),
                    'warning': sum(1 for ec in self.error_contexts if ec.level == 'warning'),
                    'info': sum(1 for ec in self.error_contexts if ec.level == 'info')
                }
            }
        }

    # Phase 2 Advanced Monitoring Methods

    def capture_state_snapshot(self, agent: str, stage: str, state_data: Dict[str, Any]) -> str:
        """
        Capture a complete state snapshot for consistency validation.

        Args:
            agent: Agent capturing the state
            stage: Current workflow stage
            state_data: Complete state data

        Returns:
            Snapshot ID for reference
        """
        if not self.enabled or not self._state_consistency_enabled:
            return str(uuid.uuid4())

        import hashlib
        import json

        snapshot_id = str(uuid.uuid4())

        # Create state hash for consistency validation
        state_json = json.dumps(state_data, sort_keys=True, default=str)
        state_hash = hashlib.md5(state_json.encode()).hexdigest()

        # Validate state consistency
        validation_status, validation_details = self._validate_state_consistency(state_data, state_hash)

        snapshot = StateSnapshot(
            snapshot_id=snapshot_id,
            timestamp=datetime.now().isoformat(),
            agent=agent,
            stage=stage,
            state_data=deepcopy(state_data),
            state_hash=state_hash,
            validation_status=validation_status,
            validation_details=validation_details
        )

        self.state_snapshots.append(snapshot)
        logger.debug(f"Captured state snapshot: {agent} -> {stage} ({validation_status})")
        return snapshot_id

    def analyze_tool_call_sequence(self, agent: str, tool_call_ids: List[str]) -> str:
        """
        Analyze a sequence of tool calls for performance patterns.

        Args:
            agent: Agent that made the tool calls
            tool_call_ids: List of tool call IDs in sequence

        Returns:
            Sequence ID for reference
        """
        if not self.enabled or not self._performance_monitoring_enabled:
            return str(uuid.uuid4())

        sequence_id = str(uuid.uuid4())

        # Get tool calls for analysis
        sequence_tool_calls = [tc for tc in self.tool_calls if tc.tool_name in tool_call_ids]

        if not sequence_tool_calls:
            return sequence_id

        # Analyze sequence timing and dependencies
        start_timestamp = min(tc.timestamp for tc in sequence_tool_calls)
        end_timestamp = max(tc.timestamp for tc in sequence_tool_calls)
        total_duration = sum(tc.duration_ms for tc in sequence_tool_calls)

        # Determine sequence type and dependencies
        sequence_type, dependencies = self._analyze_tool_dependencies(sequence_tool_calls)
        performance_impact = self._calculate_sequence_performance_impact(sequence_tool_calls)

        sequence = ToolCallSequence(
            sequence_id=sequence_id,
            agent=agent,
            tool_calls=tool_call_ids,
            start_timestamp=start_timestamp,
            end_timestamp=end_timestamp,
            total_duration_ms=total_duration,
            sequence_type=sequence_type,
            performance_impact=performance_impact,
            dependencies=dependencies
        )

        self.tool_call_sequences.append(sequence)
        logger.debug(f"Analyzed tool call sequence: {agent} -> {len(tool_call_ids)} calls ({sequence_type})")
        return sequence_id

    def calculate_performance_correlation(self, component_a: str, component_b: str) -> str:
        """
        Calculate performance correlation between two components.

        Args:
            component_a: First component (e.g., "llm_interaction", "tool_call")
            component_b: Second component

        Returns:
            Correlation ID for reference
        """
        if not self.enabled or not self._performance_monitoring_enabled:
            return str(uuid.uuid4())

        correlation_id = str(uuid.uuid4())

        # Extract performance data for correlation analysis
        data_a = self._extract_performance_data(component_a)
        data_b = self._extract_performance_data(component_b)

        # Calculate correlation
        correlation_result = self._calculate_correlation(data_a, data_b)

        correlation = PerformanceCorrelation(
            correlation_id=correlation_id,
            component_a=component_a,
            component_b=component_b,
            correlation_coefficient=correlation_result['coefficient'],
            correlation_type=correlation_result['type'],
            significance_level=correlation_result['significance'],
            analysis_details=correlation_result['details'],
            timestamp=datetime.now().isoformat()
        )

        self.performance_correlations.append(correlation)
        logger.debug(f"Calculated performance correlation: {component_a} vs {component_b} -> {correlation_result['coefficient']:.3f}")
        return correlation_id

    def export_data(self) -> Dict[str, Any]:
        """
        Export all tracked data for storage in BenchmarkRun with Phase 1 & Phase 2 enhancements.

        Returns:
            Dictionary containing all communication, state, LLM interaction, tool call, error,
            and advanced monitoring data
        """
        if not self.enabled:
            return {'enabled': False}

        # Generate performance insights and recommendations
        performance_insights = self._generate_performance_insights()
        debugging_recommendations = self._generate_debugging_recommendations()

        return {
            'enabled': True,
            'workflow_id': self.workflow_id,
            'agents': [comm.to_dict() for comm in self.communications],
            'state_transitions': [trans.to_dict() for trans in self.state_transitions],
            'summary': self.get_communication_summary(),
            # Phase 1 Enhancements
            'llm_interactions': [interaction.to_dict() for interaction in self.llm_interactions],
            'tool_calls': [tool_call.to_dict() for tool_call in self.tool_calls],
            'error_contexts': [error.to_dict() for error in self.error_contexts],
            'enhanced_debugging': True,  # Flag to indicate Phase 1 enhancement presence
            # Phase 2 Advanced Monitoring
            'state_snapshots': [snapshot.to_dict() for snapshot in self.state_snapshots],
            'tool_call_sequences': [sequence.to_dict() for sequence in self.tool_call_sequences],
            'performance_correlations': [corr.to_dict() for corr in self.performance_correlations],
            'advanced_monitoring': True,  # Flag to indicate Phase 2 enhancement presence
            'performance_insights': performance_insights,
            'debugging_recommendations': debugging_recommendations,
            'data_model_version': '2.0.0'  # Updated version for Phase 2
        }
        
    def clear(self) -> None:
        """Clear all tracked data including Phase 1 enhancements."""
        self.communications.clear()
        self.state_transitions.clear()
        self._current_agent = None
        self._current_agent_start_time = None
        self._previous_state = None

        # Phase 1 Enhancement: Clear new data structures
        self.llm_interactions.clear()
        self.tool_calls.clear()
        self.error_contexts.clear()
        self._current_execution_context = {}
        self._current_agent_llm_interactions = []
        self._current_agent_tool_calls = []

        # Phase 2 Enhancement: Clear new data structures
        self.state_snapshots.clear()
        self.tool_call_sequences.clear()
        self.performance_correlations.clear()
        self._current_tool_sequence = None

        logger.debug(f"Cleared enhanced communication tracker for workflow {self.workflow_id}")

    # Phase 2 Helper Methods

    def _validate_state_consistency(self, state_data: Dict[str, Any], state_hash: str) -> tuple[str, Dict[str, Any]]:
        """
        Validate state consistency against previous snapshots.

        Args:
            state_data: Current state data
            state_hash: Hash of current state

        Returns:
            Tuple of (validation_status, validation_details)
        """
        validation_details = {
            'checks_performed': [],
            'inconsistencies_found': [],
            'warnings': []
        }

        # Check for duplicate state hashes
        duplicate_hashes = [s for s in self.state_snapshots if s.state_hash == state_hash]
        if duplicate_hashes:
            validation_details['warnings'].append(f"Duplicate state hash found: {len(duplicate_hashes)} occurrences")

        # Check for required fields
        required_fields = ['current_stage', 'workflow_id']
        missing_fields = [field for field in required_fields if field not in state_data]
        if missing_fields:
            validation_details['inconsistencies_found'].append(f"Missing required fields: {missing_fields}")
            return "inconsistent", validation_details

        # Check state progression logic
        if self.state_snapshots:
            last_snapshot = self.state_snapshots[-1]
            if self._is_invalid_state_progression(last_snapshot.state_data, state_data):
                validation_details['inconsistencies_found'].append("Invalid state progression detected")
                return "inconsistent", validation_details

        validation_details['checks_performed'] = ['duplicate_hash', 'required_fields', 'state_progression']

        if validation_details['warnings']:
            return "warning", validation_details
        else:
            return "valid", validation_details

    def _is_invalid_state_progression(self, previous_state: Dict[str, Any], current_state: Dict[str, Any]) -> bool:
        """
        Check if state progression is invalid.

        Args:
            previous_state: Previous state data
            current_state: Current state data

        Returns:
            True if progression is invalid
        """
        # Example validation: workflow should not go backwards in stages
        stage_order = ['orchestrator', 'resource', 'engagement', 'psychological', 'strategy', 'activity', 'ethical']

        prev_stage = previous_state.get('current_stage', '')
        curr_stage = current_state.get('current_stage', '')

        if prev_stage in stage_order and curr_stage in stage_order:
            prev_index = stage_order.index(prev_stage)
            curr_index = stage_order.index(curr_stage)

            # Allow same stage (retry) or forward progression
            if curr_index < prev_index - 1:  # Significant backward movement
                return True

        return False

    def _analyze_tool_dependencies(self, tool_calls: List[ToolCallContext]) -> tuple[str, List[str]]:
        """
        Analyze dependencies between tool calls.

        Args:
            tool_calls: List of tool calls to analyze

        Returns:
            Tuple of (sequence_type, dependencies)
        """
        if len(tool_calls) <= 1:
            return "single", []

        # Sort by timestamp
        sorted_calls = sorted(tool_calls, key=lambda tc: tc.timestamp)

        # Analyze timing patterns
        time_gaps = []
        for i in range(1, len(sorted_calls)):
            prev_time = datetime.fromisoformat(sorted_calls[i-1].timestamp)
            curr_time = datetime.fromisoformat(sorted_calls[i].timestamp)
            gap = (curr_time - prev_time).total_seconds() * 1000  # Convert to ms
            time_gaps.append(gap)

        # Determine sequence type based on timing
        avg_gap = sum(time_gaps) / len(time_gaps) if time_gaps else 0

        if avg_gap < 100:  # Less than 100ms between calls
            sequence_type = "parallel"
        elif all(gap < 1000 for gap in time_gaps):  # All gaps less than 1 second
            sequence_type = "sequential"
        else:
            sequence_type = "dependent"

        # Analyze dependencies (simplified)
        dependencies = []
        for i, call in enumerate(sorted_calls[1:], 1):
            prev_call = sorted_calls[i-1]
            # Check if current call's input depends on previous call's output
            if self._has_data_dependency(prev_call, call):
                dependencies.append(f"{prev_call.tool_name} -> {call.tool_name}")

        return sequence_type, dependencies

    def _has_data_dependency(self, call_a: ToolCallContext, call_b: ToolCallContext) -> bool:
        """
        Check if call_b depends on call_a's output.

        Args:
            call_a: First tool call
            call_b: Second tool call

        Returns:
            True if dependency exists
        """
        # Simplified dependency detection
        # In practice, this would analyze data flow between calls
        output_keys = set(call_a.tool_output.keys()) if isinstance(call_a.tool_output, dict) else set()
        input_keys = set(call_b.tool_input.keys()) if isinstance(call_b.tool_input, dict) else set()

        # Check for common keys (simplified heuristic)
        return bool(output_keys.intersection(input_keys))

    def _calculate_sequence_performance_impact(self, tool_calls: List[ToolCallContext]) -> Dict[str, Any]:
        """
        Calculate performance impact of tool call sequence.

        Args:
            tool_calls: List of tool calls in sequence

        Returns:
            Performance impact analysis
        """
        if not tool_calls:
            return {}

        total_duration = sum(tc.duration_ms for tc in tool_calls)
        avg_duration = total_duration / len(tool_calls)

        # Calculate efficiency metrics
        real_calls = [tc for tc in tool_calls if tc.execution_mode == 'real']
        mock_calls = [tc for tc in tool_calls if tc.execution_mode == 'mock']

        success_rate = sum(1 for tc in tool_calls if tc.success) / len(tool_calls)

        return {
            'total_duration_ms': total_duration,
            'average_duration_ms': avg_duration,
            'call_count': len(tool_calls),
            'real_calls': len(real_calls),
            'mock_calls': len(mock_calls),
            'success_rate': success_rate,
            'efficiency_score': success_rate * (1 / (avg_duration / 1000)) if avg_duration > 0 else 0
        }

    def _extract_performance_data(self, component: str) -> List[float]:
        """
        Extract performance data for correlation analysis.

        Args:
            component: Component type to extract data for

        Returns:
            List of performance values
        """
        if component == "llm_interaction":
            return [interaction.duration_ms for interaction in self.llm_interactions]
        elif component == "tool_call":
            return [tool_call.duration_ms for tool_call in self.tool_calls]
        elif component == "agent_execution":
            return [comm.duration_ms for comm in self.communications]
        else:
            return []

    def _calculate_correlation(self, data_a: List[float], data_b: List[float]) -> Dict[str, Any]:
        """
        Calculate correlation between two data sets.

        Args:
            data_a: First data set
            data_b: Second data set

        Returns:
            Correlation analysis results
        """
        if len(data_a) < 2 or len(data_b) < 2 or len(data_a) != len(data_b):
            return {
                'coefficient': 0.0,
                'type': 'none',
                'significance': 0.0,
                'details': {'error': 'Insufficient or mismatched data'}
            }

        # Simple correlation calculation (Pearson)
        import statistics

        mean_a = statistics.mean(data_a)
        mean_b = statistics.mean(data_b)

        numerator = sum((a - mean_a) * (b - mean_b) for a, b in zip(data_a, data_b))

        sum_sq_a = sum((a - mean_a) ** 2 for a in data_a)
        sum_sq_b = sum((b - mean_b) ** 2 for b in data_b)

        denominator = (sum_sq_a * sum_sq_b) ** 0.5

        if denominator == 0:
            coefficient = 0.0
        else:
            coefficient = numerator / denominator

        # Determine correlation type
        if coefficient > 0.3:
            correlation_type = "positive"
        elif coefficient < -0.3:
            correlation_type = "negative"
        else:
            correlation_type = "none"

        # Simple significance calculation (absolute value)
        significance = abs(coefficient)

        return {
            'coefficient': coefficient,
            'type': correlation_type,
            'significance': significance,
            'details': {
                'sample_size': len(data_a),
                'mean_a': mean_a,
                'mean_b': mean_b,
                'std_dev_a': statistics.stdev(data_a) if len(data_a) > 1 else 0,
                'std_dev_b': statistics.stdev(data_b) if len(data_b) > 1 else 0
            }
        }

    def _generate_performance_insights(self) -> Dict[str, Any]:
        """
        Generate automated performance insights from collected data.

        Returns:
            Performance insights and analysis
        """
        insights = {
            'workflow_efficiency': {},
            'bottleneck_analysis': {},
            'resource_utilization': {},
            'quality_metrics': {}
        }

        if not self.communications:
            return insights

        # Workflow efficiency analysis
        total_duration = sum(comm.duration_ms for comm in self.communications)
        avg_duration = total_duration / len(self.communications)

        insights['workflow_efficiency'] = {
            'total_execution_time_ms': total_duration,
            'average_agent_duration_ms': avg_duration,
            'agent_count': len(self.communications),
            'efficiency_score': self._calculate_efficiency_score()
        }

        # Bottleneck analysis
        slowest_agent = max(self.communications, key=lambda c: c.duration_ms)
        insights['bottleneck_analysis'] = {
            'slowest_agent': slowest_agent.agent,
            'slowest_duration_ms': slowest_agent.duration_ms,
            'bottleneck_percentage': (slowest_agent.duration_ms / total_duration) * 100 if total_duration > 0 else 0
        }

        # Resource utilization
        if self.llm_interactions:
            total_tokens = sum(
                interaction.token_usage.get('input', 0) + interaction.token_usage.get('output', 0)
                for interaction in self.llm_interactions
            )
            insights['resource_utilization'] = {
                'total_llm_calls': len(self.llm_interactions),
                'total_tokens': total_tokens,
                'avg_tokens_per_call': total_tokens / len(self.llm_interactions) if self.llm_interactions else 0
            }

        # Quality metrics
        success_rate = sum(1 for comm in self.communications if comm.success) / len(self.communications)
        insights['quality_metrics'] = {
            'success_rate': success_rate,
            'error_count': len(self.error_contexts),
            'state_consistency_issues': sum(1 for snapshot in self.state_snapshots if snapshot.validation_status != 'valid')
        }

        return insights

    def _generate_debugging_recommendations(self) -> List[Dict[str, Any]]:
        """
        Generate automated debugging recommendations based on collected data.

        Returns:
            List of debugging recommendations
        """
        recommendations = []

        # Performance recommendations
        if self.communications:
            avg_duration = sum(comm.duration_ms for comm in self.communications) / len(self.communications)
            if avg_duration > 5000:  # More than 5 seconds
                recommendations.append({
                    'type': 'performance',
                    'priority': 'high',
                    'title': 'High Agent Execution Time',
                    'description': f'Average agent execution time is {avg_duration:.0f}ms, which is above optimal threshold',
                    'suggestion': 'Consider optimizing LLM prompts or reducing tool call complexity'
                })

        # Error analysis recommendations
        if self.error_contexts:
            error_types = {}
            for error in self.error_contexts:
                error_types[error.error_type] = error_types.get(error.error_type, 0) + 1

            most_common_error = max(error_types.items(), key=lambda x: x[1])
            recommendations.append({
                'type': 'error_handling',
                'priority': 'medium',
                'title': f'Frequent {most_common_error[0]} Errors',
                'description': f'Detected {most_common_error[1]} occurrences of {most_common_error[0]} errors',
                'suggestion': 'Review error handling and add fallback mechanisms'
            })

        # State consistency recommendations
        inconsistent_snapshots = [s for s in self.state_snapshots if s.validation_status == 'inconsistent']
        if inconsistent_snapshots:
            recommendations.append({
                'type': 'state_consistency',
                'priority': 'high',
                'title': 'State Consistency Issues',
                'description': f'Found {len(inconsistent_snapshots)} state consistency issues',
                'suggestion': 'Review state management logic and add validation checks'
            })

        # Tool call optimization recommendations
        if self.tool_calls:
            failed_tools = [tc for tc in self.tool_calls if not tc.success]
            if len(failed_tools) > len(self.tool_calls) * 0.1:  # More than 10% failure rate
                recommendations.append({
                    'type': 'tool_optimization',
                    'priority': 'medium',
                    'title': 'High Tool Call Failure Rate',
                    'description': f'Tool call failure rate is {len(failed_tools)/len(self.tool_calls)*100:.1f}%',
                    'suggestion': 'Review tool implementations and add better error handling'
                })

        return recommendations

    def _calculate_efficiency_score(self) -> float:
        """
        Calculate overall workflow efficiency score.

        Returns:
            Efficiency score between 0 and 1
        """
        if not self.communications:
            return 0.0

        # Base score on success rate
        success_rate = sum(1 for comm in self.communications if comm.success) / len(self.communications)

        # Adjust for performance
        avg_duration = sum(comm.duration_ms for comm in self.communications) / len(self.communications)
        performance_factor = max(0, 1 - (avg_duration / 10000))  # Penalty for durations over 10s

        # Adjust for errors
        error_factor = max(0, 1 - (len(self.error_contexts) / len(self.communications)))

        # Combined efficiency score
        efficiency = (success_rate * 0.5) + (performance_factor * 0.3) + (error_factor * 0.2)
        return min(1.0, max(0.0, efficiency))
