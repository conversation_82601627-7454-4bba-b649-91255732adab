"""
Workflow Result Handler Service

This service is responsible for processing workflow results and sending them
to the appropriate WebSocket clients. It acts as the bridge between the agent
workflow system and the real-time communication layer.

Key responsibilities:
1. Process completed workflow results
2. Format results based on workflow type
3. Send formatted results to WebSocket consumers
4. Handle error conditions gracefully
5. Prevent duplicate message sending
"""

import json
import logging
import asyncio
from typing import Dict, Any, Optional, List, Set
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.db import close_old_connections
from apps.main.services.mentor_service import MentorService

# Configure logging
logger = logging.getLogger(__name__)

# Global set to track processed workflows and prevent duplicates
_processed_workflows: Set[str] = set()
# Additional tracking for message-level deduplication
_sent_messages: Set[str] = set()

class WorkflowResultHandler:
    """
    Service for processing workflow results and sending them to WebSocket clients.
    
    This class handles the final stage of the workflow process, ensuring that
    results are properly formatted and delivered to the appropriate clients.
    """
    
    def __init__(self):
        """Initialize the workflow result handler."""
        self.channel_layer = get_channel_layer()
    
    async def process_result(self, workflow_id: str, result: Dict[str, Any], task_id: str, workflow_type: str):
        """
        Process a completed workflow result and send it to the appropriate clients.

        This method is designed to be called by Celery signal handlers when a
        workflow task completes.

        Args:
            workflow_id: Unique identifier for the workflow
            result: The workflow result data
            task_id: The Celery task ID
            workflow_type: Type of workflow (wheel_generation, activity_feedback, etc.)
        """
        try:
            # DUPLICATE PREVENTION: Check if this workflow has already been processed
            workflow_key = f"{workflow_id}_{task_id}"
            if workflow_key in _processed_workflows:
                logger.warning(f"🚨 DUPLICATE PREVENTED: Workflow {workflow_id} (task {task_id}) already processed")
                return

            # Mark this workflow as processed
            _processed_workflows.add(workflow_key)
            logger.info(f"Processing workflow result: {workflow_id} ({workflow_type})")

            # Get the user session group from the result if available
            user_ws_session = result.get('user_ws_session_name')
            user_profile_id = result.get('user_profile_id')

            if not user_ws_session:
                logger.warning(f"No WebSocket session found in result for workflow: {workflow_id}")
                # Fallback to broadcasting to all game sessions if we can't find a specific one
                user_ws_session = 'game'

            # Route result through MentorService if user_profile_id is available
            enhanced_result = result
            if user_profile_id:
                try:
                    mentor_service = MentorService.get_instance(user_profile_id, user_ws_session)
                    enhanced_result = await mentor_service.process_workflow_result(result)
                    logger.debug(f"Result processed by MentorService for user {user_profile_id}")
                except Exception as e:
                    logger.warning(f"Error processing result through MentorService: {str(e)}")
                    # Continue with original result if MentorService fails

            # Format the result based on workflow type (using enhanced result)
            formatted_messages = self._format_result_messages(enhanced_result, workflow_type)

            logger.info(f"Formatted {len(formatted_messages)} messages for workflow {workflow_id}")
            for i, message in enumerate(formatted_messages):
                logger.info(f"  Message {i+1}: {message.get('type', 'unknown')}")

            # Send all formatted messages to the client with additional deduplication
            for message in formatted_messages:
                # Create a unique key for this message to prevent duplicates
                message_content = str(message.get('content', '')) + str(message.get('wheel', ''))
                message_key = f"{user_ws_session}_{message.get('type')}_{hash(message_content)}"

                if message_key in _sent_messages:
                    logger.warning(f"🚨 MESSAGE DUPLICATE PREVENTED: {message.get('type')} for {user_ws_session}")
                    continue

                _sent_messages.add(message_key)
                logger.info(f"Sending message to {user_ws_session}: {message.get('type', 'unknown')}")
                await self.channel_layer.group_send(user_ws_session, message)

            # Send conversation state updates if present
            if 'conversation_state_updates' in enhanced_result:
                logger.info(f"Sending conversation state update to {user_ws_session}")
                await self.channel_layer.group_send(
                    user_ws_session,
                    {
                        'type': 'conversation_state_update',
                        'updates': enhanced_result['conversation_state_updates']
                    }
                )

            # Send workflow status update
            await self.channel_layer.group_send(
                user_ws_session,
                {
                    'type': 'workflow_status',
                    'workflow_id': workflow_id,
                    'status': 'completed'
                }
            )
            
            logger.info(f"Successfully sent workflow result for: {workflow_id}")

            # Clean up old processed workflows and sent messages to prevent memory leaks
            # Keep only the last 1000 processed workflows
            if len(_processed_workflows) > 1000:
                # Remove oldest entries (this is a simple approach)
                workflows_to_remove = list(_processed_workflows)[:500]  # Remove half
                for old_workflow in workflows_to_remove:
                    _processed_workflows.discard(old_workflow)
                logger.debug(f"Cleaned up {len(workflows_to_remove)} old processed workflows")

            # Clean up old sent messages to prevent memory leaks
            if len(_sent_messages) > 2000:
                # Remove oldest entries
                messages_to_remove = list(_sent_messages)[:1000]  # Remove half
                for old_message in messages_to_remove:
                    _sent_messages.discard(old_message)
                logger.debug(f"Cleaned up {len(messages_to_remove)} old sent messages")

        except Exception as e:
            logger.error(f"Error processing workflow result: {str(e)}", exc_info=True)

            # Remove from processed set on error to allow retry
            workflow_key = f"{workflow_id}_{task_id}"
            _processed_workflows.discard(workflow_key)

            # Try to send error message if possible
            try:
                if user_ws_session:
                    await self.channel_layer.group_send(
                        user_ws_session,
                        {
                            'type': 'error',
                            'content': "Error processing your request."
                        }
                    )
            except:
                pass
    

    def sync_process_result(self, workflow_id, result, task_id, workflow_type):
        """Synchronous wrapper for process_result"""
        import asyncio
        loop = asyncio.new_event_loop()
        try:
            return loop.run_until_complete(
                self.process_result(workflow_id, result, task_id, workflow_type)
            )
        finally:
            loop.close()

    def _format_result_messages(self, result: Dict[str, Any], workflow_type: str) -> List[Dict[str, Any]]:
        """
        Format result data into appropriate WebSocket messages based on workflow type.
        
        Different workflow types need different message formatting to meet the API contract.
        
        Args:
            result: The workflow result data
            workflow_type: Type of workflow
            
        Returns:
            List of formatted messages to send to the client
        """
        messages = []
        
        # Send processing_status completion only for certain workflow types
        # Post-spin workflows should not send duplicate completion status
        if workflow_type not in ['post_spin']:
            messages.append({
                'type': 'processing_status',
                'status': 'completed'
            })
        
        # Extract output_data from result if available
        output_data = result.get('output_data', {})
        if not output_data and 'result' in result:
            # Handle alternative result structure
            output_data = result['result'].get('output_data', {})
        
        # Check for Mentor response first (takes priority)
        mentor_response = result.get('mentor_response')
        if mentor_response:
            messages.append({
                'type': 'chat_message',
                'content': mentor_response,
                'is_user': False
            })

        # Handle specific workflow types
        if workflow_type == 'wheel_generation':
            # Debug logging for wheel generation
            logger.info(f"Processing wheel_generation workflow result:")
            logger.info(f"  - Has mentor_response: {bool(mentor_response)}")
            logger.info(f"  - Has user_response in output_data: {'user_response' in output_data}")
            logger.info(f"  - Has wheel in output_data: {'wheel' in output_data}")
            if 'wheel' in output_data:
                wheel_data = output_data['wheel']
                logger.info(f"  - Wheel items count: {len(wheel_data.get('items', []))}")
                logger.info(f"  - Wheel activities count: {len(wheel_data.get('activities', []))}")

            # Add chat response if available (only if no mentor response)
            if 'user_response' in output_data and not mentor_response:
                messages.append({
                    'type': 'chat_message',
                    'content': output_data['user_response'],
                    'is_user': False
                })

            # Add wheel data if available
            if 'wheel' in output_data:
                logger.info("✅ Adding wheel_data message to WebSocket")
                messages.append({
                    'type': 'wheel_data',
                    'wheel': output_data['wheel']
                })
            else:
                logger.warning("❌ No wheel data found in output_data for wheel_generation workflow")
                
        elif workflow_type == 'post_spin':
            # Add activity details response
            if 'user_response' in output_data:
                messages.append({
                    'type': 'chat_message',
                    'content': output_data['user_response'],
                    'is_user': False
                })
                
            # Add activity details if available
            if 'activity_details' in output_data:
                messages.append({
                    'type': 'activity_details',
                    'details': output_data['activity_details']
                })

        elif workflow_type in ['activity_feedback', 'pre_spin_feedback', 'user_onboarding', 'progress_review', 'discussion']: # Added 'discussion'
            # These all send chat messages in response
            if 'user_response' in output_data:
                messages.append({
                    'type': 'chat_message',
                    'content': output_data['user_response'],
                    'is_user': False
                })
        
        # If no specific messages were added, add a generic message
        if not any(msg.get('type') == 'chat_message' for msg in messages):
            messages.append({
                'type': 'chat_message',
                'content': "I've processed your request.",
                'is_user': False
            })
        
        return messages

    async def handle_error(self, workflow_id: str, error: str, task_id: str, workflow_type: str = None, user_ws_session: str = None): # Changed to async def
        """
        Handle workflow errors and notify the client. (Now async)

        Args:
            workflow_id: Unique identifier for the workflow
            error: Error message or description
            task_id: The Celery task ID
            workflow_type: Optional workflow type
            user_ws_session: Optional WebSocket session name
        """
        try:
            logger.error(f"Workflow error: {workflow_id} - {error}")
            
            if not user_ws_session:
                # Fallback to game group
                user_ws_session = 'game'

            # Send error message directly using await
            await self.channel_layer.group_send(
                user_ws_session,
                {
                    'type': 'error',
                    'content': "Error processing your request."
                }
            )

            # Send processing status update directly using await
            await self.channel_layer.group_send(
                user_ws_session,
                {
                    'type': 'processing_status',
                    'status': 'error'
                }
            )

            # Send workflow status update directly using await
            await self.channel_layer.group_send(
                user_ws_session,
                {
                    'type': 'workflow_status',
                    'workflow_id': workflow_id,
                    'status': 'failed'
                }
            )
            
        except Exception as e:
            logger.error(f"Error sending workflow error notification: {str(e)}", exc_info=True)

    # Removed sync_handle_error wrapper as handle_error is now async

# Create singleton instance for importing
workflow_result_handler = WorkflowResultHandler()
