*_generated.*
__pycache__/
myenv/
.git/
backend/docs/build/
backend/apps/main/tests/test_data/
frontend/node_modules/
TASK_OLD.md
.langgraph_api/
.VSCodeCounter/
backend/staticfiles/
backend/coverage.lcov
.coverage
backend/benchmark_results/
backend/scripts/codebase.json
backend/test-results/junit.xml
backend/backend/error_report.txt
backend/test-db.sqlite3

frontend_mock/
frontend_enhanced/node_modules/
frontend_enhanced/dist/
frontend_enhanced/.env*
codebase_export.json

backend/coverage.json
monitoring/grafana/plugins/
node_modules/
package.json
package-lock.json
.idea/
bmad-agent/
codebase-analysis/venv/
tools/graphiti/analysis/venv/
**/.DS_Store
.DS_Store
tools/mock_server/mock_server.log
.history/

# Secure Secrets Management - NEVER COMMIT THESE FILES
.env.local
.env.*.local
.env.prod.local
.env.dev.local
*.local.env
.secrets/
secrets.json
*.key
*.pem

# Backup files that might contain secrets
*.sql
backups/
logs/
*.log

# IDE files that might store secrets in configurations
.vscode/settings.json
.idea/workspace.xml
.idea/misc.xml

# OS files
Thumbs.db

# Temporary files that might contain sensitive data
*.tmp
*.temp
.cache/
.temp/

# Docker secrets and volumes
docker-secrets/
docker-compose.override.yml

# Cloud deployment temporary files
.terraform/
.aws/
.gcp/
.azure/

# Production configuration files with real credentials
config/production.yml
config/secrets.yml
frontend/dist/assets/
