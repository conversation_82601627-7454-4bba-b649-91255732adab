import logging
import json
from pathlib import Path
from typing import Optional

from apps.main.agents.benchmarking import AgentBenchmarkImproved
from apps.main.testing.mock_tool_registry import MockToolRegistry
from apps.main.llm.client import <PERSON><PERSON><PERSON>lient, Mock<PERSON><PERSON>lient
from apps.main.llm.service import LLMService
from apps.main.agents.base_agent import LangGraph<PERSON>gent # Assuming agents inherit from this
from apps.main.services.conversation_dispatcher import ConversationDispatcher # If needed

logger = logging.getLogger(__name__)

def debug_benchmark_scenario(
    scenario_path: str,
    use_real_llm: bool = False,
    log_level: int = logging.INFO,
    agent_class: Optional[type[LangGraphAgent]] = None, # Allow specifying agent
    **agent_kwargs # Allow passing agent specific kwargs
):
    """
    Helper function to debug a specific benchmark scenario.

    Args:
        scenario_path: Path to the benchmark scenario JSON file.
        use_real_llm: Whether to use the real LLM client instead of the mock.
        log_level: Logging level for the debugging session.
        agent_class: The agent class to benchmark (e.g., MentorAgent).
        **agent_kwargs: Keyword arguments to pass to the agent constructor.
    """
    logging.basicConfig(level=log_level)
    logger.info(f"Debugging benchmark scenario: {scenario_path}")

    scenario_file = Path(scenario_path)
    if not scenario_file.exists():
        logger.error(f"Scenario file not found: {scenario_path}")
        return

    try:
        with open(scenario_file, 'r') as f:
            scenario_data = json.load(f)
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON from scenario file {scenario_path}: {e}")
        return
    except Exception as e:
        logger.error(f"Error reading scenario file {scenario_path}: {e}")
        return

    # Determine which agent to use if not specified
    if agent_class is None:
         # Attempt to infer agent from scenario data if possible, or default
         agent_name = scenario_data.get("agent_name") # Assuming scenario has agent_name
         if agent_name:
             # This would require a mapping from agent_name string to class
             # For now, we might need to require agent_class or have a known list
             logger.warning(f"Agent class not specified. Attempted to infer '{agent_name}' but no mapping exists.")
             logger.error("Please provide the 'agent_class' parameter.")
             return
         else:
             logger.error("Agent class not specified and could not be inferred from scenario.")
             return

    # Setup LLM Client
    llm_client = RealLLMClient() if use_real_llm else MockLLMClient()
    llm_service = LLMService(llm_client=llm_client)

    # Setup Mock Tool Registry
    mock_tool_registry = MockToolRegistry()
    # You might need to load tool expectations from the scenario here
    # mock_tool_registry.load_expectations(scenario_data.get("tool_expectations", []))

    # Instantiate the agent
    try:
        # Note: Agents don't accept tool_registry parameter
        # They load tools through their database service instead
        agent_instance = agent_class(
            llm_service=llm_service,
            # Pass other dependencies as required by the agent's __init__
            # This might need to be generalized or passed via agent_kwargs
            **agent_kwargs
        )
    except TypeError as e:
        logger.error(f"Error instantiating agent {agent_class.__name__}: {e}")
        logger.error("Please ensure all required agent dependencies are provided via agent_kwargs.")
        return
    except Exception as e:
        logger.error(f"Unexpected error instantiating agent {agent_class.__name__}: {e}")
        return


    # Create the benchmark runner
    benchmark_runner = AgentBenchmarkImproved(
        agent=agent_instance,
        scenario=scenario_data,
        # Pass other dependencies needed by the runner if any
    )

    logger.info("Starting benchmark run...")
    try:
        # The run_benchmark method might need adjustments to provide more verbose output
        # For now, we rely on the agent's internal logging and the runner's potential logging
        result = benchmark_runner.run_benchmark()
        logger.info("Benchmark run completed.")
        logger.info(f"Result: {result}")
        # You might want to print more details from the result here
        # e.g., result.stage_performance_details, result.tool_calls, result.llm_calls
    except Exception as e:
        logger.error(f"An error occurred during the benchmark run: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    # Example usage (replace with actual scenario path and agent class)
    # from apps.main.agents.mentor_agent import MentorAgent # Example import
    # debug_benchmark_scenario(
    #     scenario_path='backend/testing/benchmark_data/scenarios/mentor_wheelgen_35.json',
    #     use_real_llm=False, # Set to True to test with real LLM
    #     log_level=logging.DEBUG, # Set to DEBUG for more verbose output
    #     agent_class=MentorAgent,
    #     # Add any required agent_kwargs here, e.g., user_profile=mock_user_profile
    # )
    logger.info("Benchmark debugging helper. Use the debug_benchmark_scenario function.")
    logger.info("Example usage is commented out in the __main__ block.")
