"""
Mock Workflow for Testing

This module provides a mock workflow implementation for testing the workflow benchmarking system.
It simulates a complete workflow with configurable behavior for different test scenarios.
"""

import asyncio
import logging
import uuid
from typing import Dict, Any, List, Optional, Callable, Tuple
from dataclasses import dataclass, field

from apps.main.services.async_workflow_manager import WorkflowBenchmarker, BenchmarkResult, StageTimer, TokenTracker
from apps.main.testing.mock_tool_registry import MockToolRegistry

# Configure logging
logger = logging.getLogger(__name__)


class MockWorkflow(WorkflowBenchmarker):
    """
    Mock implementation of AsyncWorkflowManager for testing.

    This class provides a configurable mock workflow that can be used for testing
    the workflow benchmarking system without requiring a real workflow implementation.
    """

    def __init__(
        self,
        workflow_type: str = "test_workflow",
        stages: List[str] = None,
        stage_durations: Dict[str, float] = None,
        tool_calls: Dict[str, int] = None,
        input_tokens: int = 100,
        output_tokens: int = 50,
        success_rate: float = 1.0,
        error_stages: List[str] = None,
        output_data: Dict[str, Any] = None,
        delay_seconds: float = 0.1
    ):
        """
        Initialize the mock workflow.

        Args:
            workflow_type: Type of workflow to simulate
            stages: List of stages to simulate
            stage_durations: Dictionary mapping stage names to durations in seconds
            tool_calls: Dictionary mapping tool names to call counts
            input_tokens: Number of input tokens to simulate
            output_tokens: Number of output tokens to simulate
            success_rate: Success rate to simulate (0.0 to 1.0)
            error_stages: List of stages that should raise errors
            output_data: Output data to include in the benchmark result
            delay_seconds: Delay between stages to simulate processing time
        """
        super().__init__()
        self.workflow_type = workflow_type
        self.stages = stages or ["init", "process", "complete"]
        self.stage_durations = stage_durations or {stage: 1.0 for stage in self.stages}
        self.tool_calls = tool_calls or {"get_user_profile": 1, "search_database": 2}
        self.input_tokens = input_tokens
        self.output_tokens = output_tokens
        self.success_rate = success_rate
        self.error_stages = error_stages or []
        self.output_data = output_data or {"response": "This is a mock response from the test workflow."}
        self.delay_seconds = delay_seconds

        # Initialize token tracker with predefined values and a mock run_id
        mock_run_id = uuid.uuid4()
        self.token_tracker = TokenTracker(run_id=mock_run_id)
        # Add tokens directly to the tracker's attributes instead of using record_usage
        # since record_usage tries to update the database
        self.token_tracker.input_tokens = input_tokens
        self.token_tracker.output_tokens = output_tokens

    async def run_benchmark(self, scenario, benchmark_params=None, progress_callback=None):
        """
        Run a benchmark using the mock workflow.

        Args:
            scenario: BenchmarkScenario instance
            benchmark_params: Optional parameters for the benchmark
            progress_callback: Optional callback for reporting progress

        Returns:
            BenchmarkRun instance (mocked for testing)
        """
        # Set up benchmark parameters
        params = {
            'runs': 3,
            'warmup_runs': 1,
            'semantic_evaluation': False,
            **(benchmark_params or {})
        }

        # Create a mock tool registry
        from apps.main.testing.mock_tool_registry import MockToolRegistry
        mock_tools = MockToolRegistry()

        # Run the workflow benchmark (this returns a BenchmarkResult)
        result = await self._run_workflow_benchmark(
            scenario=scenario,
            workflow_type=self.workflow_type,
            mock_tools=mock_tools,
            runs=params['runs'],
            warmup_runs=params['warmup_runs'],
            progress_callback=progress_callback
        )

        # Create a simple mock object that mimics BenchmarkRun for testing
        class MockBenchmarkRun:
            def __init__(self, scenario, params, result):
                self.scenario = scenario
                self.parameters = params
                self.runs_count = params['runs']
                self.mean_duration = result.mean_duration * 1000  # Convert to milliseconds
                self.median_duration = result.median_duration * 1000
                self.min_duration = result.min_duration * 1000
                self.max_duration = result.max_duration * 1000
                self.std_dev = result.std_dev * 1000
                self.success_rate = result.success_rate  # Keep as fraction (0-1)
                self.tool_calls = sum(result.tool_call_counts.values())
                self.tool_breakdown = result.tool_call_counts
                self.total_input_tokens = result.total_input_tokens
                self.total_output_tokens = result.total_output_tokens
                self.raw_results = {"last_output": result.last_output_data}

        return MockBenchmarkRun(scenario, params, result)

    async def _run_workflow_benchmark(
        self,
        scenario,
        workflow_type,
        mock_tools,
        runs=3,
        warmup_runs=1,
        progress_callback=None,
        use_real_llm: bool = False,
        use_real_tools: bool = False,
        use_real_db: bool = False,
        user_profile_id: Optional[str] = None
    ):
        """
        Run a workflow benchmark with the mock workflow.

        Args:
            scenario: BenchmarkScenario instance
            workflow_type: Type of workflow to benchmark
            mock_tools: MockToolRegistry instance
            runs: Number of benchmark runs to perform
            warmup_runs: Number of warmup runs to perform
            progress_callback: Optional callback for reporting progress

        Returns:
            BenchmarkResult instance
        """
        # Override workflow_type if specified in constructor
        workflow_type = self.workflow_type or workflow_type

        # Reset the stage timer
        self.stage_timer = StageTimer()

        # Reset the token tracker with a mock run_id
        mock_run_id = uuid.uuid4()
        self.token_tracker = TokenTracker(run_id=mock_run_id)

        # Simulate warmup runs
        for i in range(warmup_runs):
            if progress_callback:
                await progress_callback({
                    "stage": "warmup",
                    "current_run": i + 1,
                    "total_runs": warmup_runs,
                    "message": f"Performing warmup run {i + 1}/{warmup_runs}"
                })
            await self._simulate_workflow_run(mock_tools, is_warmup=True)

        # Collect durations for statistical analysis
        durations = []
        success_count = 0

        # Simulate benchmark runs
        for i in range(runs):
            if progress_callback:
                await progress_callback({
                    "stage": "benchmark",
                    "current_run": i + 1,
                    "total_runs": runs,
                    "message": f"Performing benchmark run {i + 1}/{runs}"
                })

            # Simulate the workflow run
            duration, success = await self._simulate_workflow_run(mock_tools)

            # Record the duration and success
            durations.append(duration)
            if success:
                success_count += 1

        # Calculate statistics
        mean_duration = sum(durations) / len(durations) if durations else 0
        median_duration = sorted(durations)[len(durations) // 2] if durations else 0
        min_duration = min(durations) if durations else 0
        max_duration = max(durations) if durations else 0
        std_dev = (sum((d - mean_duration) ** 2 for d in durations) / len(durations)) ** 0.5 if durations else 0
        success_rate = success_count / runs if runs > 0 else 0

        # Create the benchmark result
        result = BenchmarkResult(
            workflow_type=workflow_type,
            scenario_name=scenario.name,
            mean_duration=mean_duration,
            median_duration=median_duration,
            min_duration=min_duration,
            max_duration=max_duration,
            std_dev=std_dev,
            success_rate=success_rate,
            tool_call_counts=self.tool_calls,
            total_input_tokens=self.input_tokens,
            total_output_tokens=self.output_tokens,
            last_output_data=self.output_data
        )

        return result

    async def _simulate_workflow_run(self, mock_tools, is_warmup=False):
        """
        Simulate a single workflow run.

        Args:
            mock_tools: MockToolRegistry instance
            is_warmup: Whether this is a warmup run

        Returns:
            Tuple of (duration, success)
        """
        start_time = asyncio.get_event_loop().time()
        success = True

        # Simulate each stage
        for stage in self.stages:
            # Start the stage
            await self.stage_timer.start_stage(stage)

            # Simulate tool calls if this is a non-warmup run
            if not is_warmup:
                # Only execute each tool once per workflow run, not once per stage
                # This ensures the tool call counts match the expected values in tests
                if stage == self.stages[0]:  # Only execute tools in the first stage
                    for tool_name, call_count in self.tool_calls.items():
                        for _ in range(call_count):
                            try:
                                # Simulate a tool call
                                await mock_tools.execute_tool(
                                    tool_name,
                                    {"param1": "value1", "param2": "value2"}
                                )

                                # Add tokens for this tool call directly to attributes
                                self.token_tracker.input_tokens += 10
                                self.token_tracker.output_tokens += 20
                            except Exception as e:
                                logger.error(f"Error executing tool {tool_name}: {str(e)}")
                                success = False

            # Simulate processing time
            await asyncio.sleep(self.stage_durations.get(stage, 0.5))

            # Simulate an error if this stage is in error_stages
            if stage in self.error_stages:
                await self.stage_timer.end_stage(stage)
                raise ValueError(f"Simulated error in stage {stage}")

            # End the stage
            await self.stage_timer.end_stage(stage)

        # Calculate the total duration
        end_time = asyncio.get_event_loop().time()
        duration = end_time - start_time

        # Apply success rate
        if not is_warmup and self.success_rate < 1.0:
            # For test predictability, we'll use a deterministic approach
            # If this is a test run, we'll respect the configured success_rate exactly
            # rather than using random chance
            if hasattr(self, '_test_run_count'):
                self._test_run_count += 1
                # Alternate success/failure to match the expected success rate
                # For example, with success_rate=0.5, we'll make every other run fail
                success = (self._test_run_count % int(1 / self.success_rate)) == 0
            else:
                # Initialize counter for test runs
                self._test_run_count = 0
                # For the first run, always succeed if success_rate > 0
                success = self.success_rate > 0

        return duration, success


class TestWorkflow(MockWorkflow):
    """
    Simplified mock workflow for unit testing.

    This class provides a minimal implementation that returns predefined results
    without actually simulating the workflow execution.
    """

    async def run_benchmark(self, scenario, benchmark_params=None, progress_callback=None):
        """
        Run a benchmark using the test workflow with predefined results.

        Args:
            scenario: BenchmarkScenario instance
            benchmark_params: Optional parameters for the benchmark
            progress_callback: Optional callback for reporting progress

        Returns:
            BenchmarkRun instance (mocked for testing)
        """
        # Set up benchmark parameters
        params = {
            'runs': 3,
            'warmup_runs': 1,
            'semantic_evaluation': False,
            **(benchmark_params or {})
        }

        # Call progress callback if provided
        if progress_callback:
            await progress_callback({
                "stage": "complete",
                "current_run": params['runs'],
                "total_runs": params['runs'],
                "message": "Benchmark complete"
            })

        # Create a simple mock object that mimics BenchmarkRun for testing
        class MockBenchmarkRun:
            def __init__(self, scenario, params, workflow):
                self.scenario = scenario
                self.parameters = params
                self.runs_count = params['runs']
                self.mean_duration = 1000.0  # 1 second in milliseconds
                self.median_duration = 1000.0
                self.min_duration = 900.0
                self.max_duration = 1100.0
                self.std_dev = 100.0
                self.success_rate = 1.0  # Keep as fraction (0-1)
                self.tool_calls = sum(workflow.tool_calls.values())
                self.tool_breakdown = workflow.tool_calls
                self.total_input_tokens = workflow.input_tokens
                self.total_output_tokens = workflow.output_tokens
                self.raw_results = {"last_output": workflow.output_data}

        return MockBenchmarkRun(scenario, params, self)

    async def _run_workflow_benchmark(
        self,
        scenario,
        workflow_type,
        mock_tools,
        runs=3,
        warmup_runs=1,
        progress_callback=None,
        use_real_llm: bool = False,
        use_real_tools: bool = False,
        use_real_db: bool = False,
        user_profile_id: Optional[str] = None
    ):
        """
        Return a predefined benchmark result without simulating the workflow.

        Args:
            scenario: BenchmarkScenario instance
            workflow_type: Type of workflow to benchmark
            mock_tools: MockToolRegistry instance
            runs: Number of benchmark runs to perform
            warmup_runs: Number of warmup runs to perform
            progress_callback: Optional callback for reporting progress

        Returns:
            BenchmarkResult instance
        """
        # Call progress callback if provided
        if progress_callback:
            await progress_callback({
                "stage": "complete",
                "current_run": runs,
                "total_runs": runs,
                "message": "Benchmark complete"
            })

        # Return a predefined result
        return BenchmarkResult(
            workflow_type=self.workflow_type,
            scenario_name=scenario.name,
            mean_duration=1.0,
            median_duration=1.0,
            min_duration=0.9,
            max_duration=1.1,
            std_dev=0.1,
            success_rate=self.success_rate,
            tool_call_counts=self.tool_calls,
            total_input_tokens=self.input_tokens,
            total_output_tokens=self.output_tokens,
            last_output_data=self.output_data
        )
