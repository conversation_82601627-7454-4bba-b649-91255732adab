# Generated migration for adding is_real field to UserProfile model

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='userprofile',
            name='is_real',
            field=models.BooleanField(
                default=True,
                help_text='True for real users, False for fake/archetypal profiles used in benchmarking and testing'
            ),
        ),
        migrations.AddIndex(
            model_name='userprofile',
            index=models.Index(fields=['is_real'], name='user_userprofile_is_real_idx'),
        ),
        migrations.AddIndex(
            model_name='userprofile',
            index=models.Index(fields=['profile_name'], name='user_userprofile_profile_name_idx'),
        ),
    ]
