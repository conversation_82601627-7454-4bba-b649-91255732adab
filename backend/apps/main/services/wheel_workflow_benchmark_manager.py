# ACTIVE_FILE - 29-05-2025
"""
Wheel Generation Workflow Benchmark Manager

This module implements a concrete workflow benchmark manager for the wheel generation workflow.
It extends the AsyncWorkflowManager base class to provide specific implementation for
benchmarking the wheel generation workflow.
"""

import asyncio
import logging
import time
import uuid
import statistics
from typing import Dict, Any, List, Optional, Callable, Type, Union
from dataclasses import dataclass, field
from collections import defaultdict

from asgiref.sync import sync_to_async
from django.db import transaction
from django.utils import timezone

from apps.main.models import BenchmarkScenario, BenchmarkRun
from apps.main.services.async_workflow_manager import WorkflowBenchmarker, BenchmarkResult
from apps.main.testing.mock_tool_registry import MockToolRegistry
from apps.main.graphs.wheel_generation_graph import create_wheel_generation_graph, run_wheel_generation_workflow
from apps.main.services.agent_communication_tracker import AgentCommunicationTracker

# Configure logging
logger = logging.getLogger(__name__)

class WheelWorkflowBenchmarkManager(WorkflowBenchmarker):
    """
    Benchmark manager for the wheel generation workflow.

    This class extends AsyncWorkflowManager to provide specific implementation
    for benchmarking the wheel generation workflow.
    """

    async def _run_workflow_benchmark(self,
                                     scenario: BenchmarkScenario,
                                     workflow_type: str,
                                     mock_tools: Optional[MockToolRegistry],
                                     runs: int = 3,
                                     warmup_runs: int = 1,
                                     progress_callback: Optional[Callable] = None,
                                     use_real_llm: bool = False,
                                     use_real_tools: bool = False,
                                     use_real_db: bool = False,
                                     user_profile_id: Optional[str] = None) -> BenchmarkResult:
        """
        Run a wheel generation workflow benchmark.

        Args:
            scenario: The benchmark scenario
            workflow_type: Type of workflow to benchmark (should be 'wheel_generation')
            mock_tools: Configured mock tool registry (None if using real tools)
            runs: Number of benchmark runs
            warmup_runs: Number of warmup runs
            progress_callback: Optional callback for progress reporting
            use_real_llm: Whether to use real LLM services instead of mocks
            use_real_tools: Whether to use real tool implementations instead of mocks
            use_real_db: Whether to use real database operations instead of mocks

        Returns:
            BenchmarkResult: The benchmark results
        """
        if workflow_type != 'wheel_generation':
            raise ValueError(f"Expected workflow_type 'wheel_generation', got '{workflow_type}'")

        # Initialize result object
        result = BenchmarkResult(
            workflow_type=workflow_type,
            scenario_name=scenario.name
        )

        # Extract input data from scenario
        input_data = scenario.input_data
        if not isinstance(input_data, dict):
            raise ValueError(f"Expected dict for input_data, got {type(input_data)}")

        # CRITICAL FIX: Always prioritize the provided user_profile_id parameter over scenario data
        # This ensures that when quick_test.js or other interfaces provide a user_profile_id,
        # it takes precedence over any hardcoded values in the scenario
        scenario_user_id = input_data.get('user_profile_id')

        if user_profile_id:
            # Use the provided parameter (highest priority)
            final_user_profile_id = user_profile_id
            logger.info(f"Using provided user_profile_id '{final_user_profile_id}' for scenario '{scenario.name}' (overriding scenario value: '{scenario_user_id}')")
        elif scenario_user_id:
            # Use the scenario's user_profile_id as fallback
            final_user_profile_id = scenario_user_id
            logger.info(f"Using scenario user_profile_id '{final_user_profile_id}' for scenario '{scenario.name}'")
        else:
            # Generate a default user_profile_id for scenarios that don't have one
            final_user_profile_id = f"benchmark-user-{uuid.uuid4().hex[:8]}"
            logger.info(f"No user_profile_id found, using generated default: '{final_user_profile_id}' for scenario '{scenario.name}'")

        # CRITICAL FIX: Always update input_data with the final user_profile_id to ensure consistency
        # This ensures that any validation or processing that expects user_profile_id in input_data will work
        input_data = input_data.copy()  # Don't modify the original
        input_data['user_profile_id'] = final_user_profile_id

        # Update the user_profile_id variable to use the final value
        user_profile_id = final_user_profile_id

        # Prepare context packet AFTER updating input_data with correct user_profile_id
        context_packet = input_data.get('context_packet', {})
        if not isinstance(context_packet, dict):
            context_packet = {}

        # CRITICAL FIX: Always set user_id in context packet to match the final user_profile_id
        # This ensures consistency between user_profile_id parameter and context_packet.user_id
        context_packet['user_id'] = user_profile_id
        logger.debug(f"Set context_packet.user_id to: {user_profile_id}")

        # Add task_type if not present
        if 'task_type' not in context_packet:
            context_packet['task_type'] = 'wheel_generation'

        # Add workflow origin for benchmark workflows
        if 'system_metadata' not in context_packet:
            context_packet['system_metadata'] = {}
        context_packet['system_metadata']['workflow_origin'] = 'benchmark'
        logger.debug(f"Set workflow origin to: benchmark")

        # Track durations
        durations = []

        # Run warmup iterations
        for i in range(warmup_runs):
            try:
                logger.info(f"Running warmup iteration {i+1}/{warmup_runs} for scenario '{scenario.name}'")

                # Generate a unique workflow ID for this run
                workflow_id = str(uuid.uuid4())

                # Choose between real and mock workflow execution
                if use_real_llm or use_real_tools or use_real_db:
                    # Run real workflow
                    await self._run_real_wheel_generation_workflow(
                        user_profile_id=user_profile_id,
                        context_packet=context_packet,
                        workflow_id=workflow_id,
                        use_real_llm=use_real_llm,
                        use_real_tools=use_real_tools,
                        use_real_db=use_real_db,
                        mock_tools=mock_tools
                    )
                else:
                    # Run mock workflow
                    await self._run_mock_wheel_generation_workflow(
                        user_profile_id=user_profile_id,
                        context_packet=context_packet,
                        workflow_id=workflow_id,
                        mock_tools=mock_tools
                    )
            except Exception as e:
                logger.warning(f"Error in warmup iteration {i+1}: {str(e)}", exc_info=True)

        # Run benchmark iterations
        successful_runs = 0

        for i in range(runs):
            try:
                logger.info(f"Running benchmark iteration {i+1}/{runs} for scenario '{scenario.name}'")

                if progress_callback:
                    progress_callback(state='PROGRESS', meta={
                        'current': 30 + int((i / runs) * 50),  # Scale from 30% to 80%
                        'total': 100,
                        'status': f"Running benchmark iteration {i+1}/{runs}"
                    })

                # Generate a unique workflow ID for this run
                workflow_id = str(uuid.uuid4())

                # Measure execution time
                start_time = time.monotonic()

                # Choose between real and mock workflow execution
                if use_real_llm or use_real_tools or use_real_db:
                    # Run real workflow
                    output = await self._run_real_wheel_generation_workflow(
                        user_profile_id=user_profile_id,
                        context_packet=context_packet,
                        workflow_id=workflow_id,
                        use_real_llm=use_real_llm,
                        use_real_tools=use_real_tools,
                        use_real_db=use_real_db,
                        mock_tools=mock_tools
                    )
                else:
                    # Run mock workflow
                    output = await self._run_mock_wheel_generation_workflow(
                        user_profile_id=user_profile_id,
                        context_packet=context_packet,
                        workflow_id=workflow_id,
                        mock_tools=mock_tools
                    )

                # Calculate duration
                end_time = time.monotonic()
                duration = end_time - start_time
                durations.append(duration)

                # Store the last output
                if i == runs - 1:
                    result.last_output_data = output

                # Track successful runs
                successful_runs += 1

                # Collect tool usage statistics
                if mock_tools is not None:
                    # Using mock tools - get counts from mock registry
                    for tool_name, count in mock_tools.call_counts.items():
                        if tool_name in result.tool_call_counts:
                            result.tool_call_counts[tool_name] += count
                        else:
                            result.tool_call_counts[tool_name] = count
                else:
                    # Using real tools - extract from output if available
                    if 'tool_usage' in output:
                        for tool_name, count in output['tool_usage'].items():
                            if tool_name in result.tool_call_counts:
                                result.tool_call_counts[tool_name] += count
                            else:
                                result.tool_call_counts[tool_name] = count

                # Collect stage timings from the workflow
                # Note: This assumes the workflow uses the StageTimer pattern
                if 'stage_timings' in output:
                    for stage, timing in output['stage_timings'].items():
                        result.stage_timings[stage].append(timing)

                # Collect token usage
                if 'token_usage' in output:
                    result.total_input_tokens += output['token_usage'].get('input_tokens', 0)
                    result.total_output_tokens += output['token_usage'].get('output_tokens', 0)

            except Exception as e:
                logger.error(f"Error in benchmark iteration {i+1}: {str(e)}", exc_info=True)
                result.errors.append(f"Error in iteration {i+1}: {str(e)}")

        # Calculate statistics
        if durations:
            result.mean_duration = statistics.mean(durations)
            result.median_duration = statistics.median(durations)
            result.min_duration = min(durations)
            result.max_duration = max(durations)
            result.std_dev = statistics.stdev(durations) if len(durations) > 1 else 0.0

        # Calculate success rate
        result.success_rate = successful_runs / runs if runs > 0 else 0.0

        return result

    async def _run_real_wheel_generation_workflow(self,
                                                 user_profile_id: str,
                                                 context_packet: Dict[str, Any],
                                                 workflow_id: str,
                                                 use_real_llm: bool = False,
                                                 use_real_tools: bool = False,
                                                 use_real_db: bool = False,
                                                 mock_tools: Optional[MockToolRegistry] = None) -> Dict[str, Any]:
        """
        Run a real wheel generation workflow for benchmarking.

        This executes the actual wheel generation workflow with real LLM calls,
        real tool implementations, and real database operations as configured.

        Args:
            user_profile_id: The user profile ID
            context_packet: Context information from the scenario
            workflow_id: Unique workflow ID
            use_real_llm: Whether to use real LLM services
            use_real_tools: Whether to use real tool implementations
            use_real_db: Whether to use real database operations
            mock_tools: Optional mock tool registry for partial mocking

        Returns:
            Dict containing the real workflow output with wheel data
        """
        logger.info(f"Running REAL wheel generation workflow {workflow_id} with real_llm={use_real_llm}, real_tools={use_real_tools}, real_db={use_real_db}")

        try:
            # Import the real workflow function
            from apps.main.graphs.wheel_generation_graph import run_wheel_generation_workflow

            # Prepare the workflow input with enhanced tracking enabled for better debugging
            workflow_input = {
                "user_profile_id": user_profile_id,
                "context_packet": context_packet,
                "workflow_id": workflow_id,
                "use_real_llm": use_real_llm,
                "use_real_tools": use_real_tools,
                "use_real_db": use_real_db,
                "enable_enhanced_tracking": True,  # Enable enhanced tracking for benchmarking
                "enable_advanced_monitoring": True  # Enable advanced monitoring for detailed analysis
            }

            # If we have mock tools for partial mocking, pass them
            if mock_tools is not None:
                workflow_input["mock_tools"] = mock_tools

            # Execute the real workflow using the new benchmarking interface
            result = await run_wheel_generation_workflow(workflow_input=workflow_input)

            # Ensure the result has the expected structure for benchmarking
            if not isinstance(result, dict):
                logger.warning(f"Real workflow returned non-dict result: {type(result)}")
                result = {"output_data": result, "workflow_id": workflow_id}

            # Add benchmarking metadata
            result.update({
                "workflow_id": workflow_id,
                "user_profile_id": user_profile_id,
                "workflow_type": "wheel_generation",
                "execution_mode": "real",
                "real_llm_used": use_real_llm,
                "real_tools_used": use_real_tools,
                "real_db_used": use_real_db
            })

            logger.info(f"Real wheel generation workflow {workflow_id} completed successfully")
            return result

        except Exception as e:
            logger.error(f"Error in real wheel generation workflow {workflow_id}: {str(e)}", exc_info=True)

            # Instead of silently falling back to mock, provide detailed error information
            # This helps users understand why real mode failed
            error_details = {
                "error_type": type(e).__name__,
                "error_message": str(e),
                "workflow_id": workflow_id,
                "requested_mode": {
                    "use_real_llm": use_real_llm,
                    "use_real_tools": use_real_tools,
                    "use_real_db": use_real_db
                },
                "fallback_reason": "Real workflow execution failed"
            }

            # Check if this is a common error that we can provide guidance for
            if "NotImplementedError" in str(e):
                error_details["user_message"] = "Real workflow mode is not fully implemented yet. Please use mock mode for now."
                error_details["suggestion"] = "Try using 'Mock Mode' or 'Real Tools Only' mode instead."
            elif "ConnectionError" in str(e) or "timeout" in str(e).lower():
                error_details["user_message"] = "Failed to connect to external services required for real mode."
                error_details["suggestion"] = "Check your network connection and try again, or use mock mode."
            elif "authentication" in str(e).lower() or "api" in str(e).lower():
                error_details["user_message"] = "Authentication or API configuration issue in real mode."
                error_details["suggestion"] = "Check your API keys and configuration, or use mock mode."
            else:
                error_details["user_message"] = f"Real workflow execution failed: {str(e)}"
                error_details["suggestion"] = "Try using mock mode or check the logs for more details."

            # Re-raise the exception with enhanced error information
            # This ensures the error is properly reported to the user instead of being hidden
            enhanced_error = Exception(f"Real workflow mode failed: {error_details['user_message']}")
            enhanced_error.error_details = error_details
            raise enhanced_error

    async def _run_mock_wheel_generation_workflow(self,
                                                 user_profile_id: str,
                                                 context_packet: Dict[str, Any],
                                                 workflow_id: str,
                                                 mock_tools: MockToolRegistry) -> Dict[str, Any]:
        """
        Run a mock version of the wheel generation workflow for benchmarking.

        This creates a realistic simulation of the wheel generation workflow without
        requiring real LLM clients or database connections.

        Args:
            user_profile_id: The user profile ID
            context_packet: Context information from the scenario
            workflow_id: Unique workflow ID
            mock_tools: Mock tool registry for tool calls

        Returns:
            Dict containing the mock workflow output with wheel data
        """
        # Simulate processing delay
        await asyncio.sleep(0.1)

        # Extract context information
        trust_level = context_packet.get('trust_level', 50)
        user_message = context_packet.get('user_message', 'I want to improve myself')

        # Generate mock wheel data based on context
        wheel_data = self._generate_mock_wheel(trust_level, user_message)

        # Generate mock user response
        user_response = self._generate_mock_user_response(trust_level, user_message, wheel_data)

        # Initialize communication tracker
        comm_tracker = AgentCommunicationTracker(workflow_id, enabled=True)

        # Simulate the complete agent workflow with communication tracking
        workflow_state = {
            "workflow_id": workflow_id,
            "user_profile_id": user_profile_id,
            "context_packet": context_packet,
            "current_stage": "initial"
        }

        # Simulate each agent in the workflow
        agents_sequence = [
            ("orchestrator", "orchestration"),
            ("resource", "resource_analysis"),
            ("engagement", "engagement_analysis"),
            ("psychological", "psychological_assessment"),
            ("strategy", "strategy_formulation"),
            ("activity", "activity_generation"),
            ("ethical", "ethical_validation")
        ]

        for agent_name, stage in agents_sequence:
            # Start tracking this agent
            exec_id = comm_tracker.start_agent_execution(
                agent_name,
                workflow_state.copy(),
                workflow_state.copy()
            )

            # Simulate agent processing
            await asyncio.sleep(0.02)  # Simulate processing time

            # Generate agent-specific output
            agent_output = await self._simulate_agent_execution(
                agent_name, workflow_state, trust_level, user_message
            )

            # Update workflow state with agent output
            workflow_state[f"{agent_name}_output"] = agent_output
            workflow_state["current_stage"] = stage

            # End tracking this agent
            comm_tracker.end_agent_execution(
                exec_id,
                agent_name,
                workflow_state.copy(),
                agent_output,
                stage,
                success=True
            )

        # Create realistic workflow output with communication data
        output = {
            "workflow_id": workflow_id,
            "user_profile_id": user_profile_id,
            "completed": True,
            "output_data": {
                "user_response": user_response,
                "wheel": wheel_data
            },
            "session_timestamp": context_packet.get('session_timestamp'),
            "user_ws_session_name": context_packet.get('user_ws_session_name'),
            "workflow_type": "wheel_generation",
            "agent_outputs": {
                "orchestrator": {"status": "completed", "routing_decisions": 6},
                "resource": {"available_time": "30 minutes"},
                "engagement": {"preferred_domains": ["wellness", "productivity"]},
                "psychological": {"trust_phase": "foundation" if trust_level < 50 else "expansion"},
                "strategy": {"challenge_level": "moderate", "focus": "balanced_growth"},
                "wheel_activity": {"activities_generated": len(wheel_data["items"])},
                "ethical": {"validation_passed": True, "concerns": []}
            },
            "agent_communications": comm_tracker.export_data(),
            # Add token usage for benchmarking
            "token_usage": {
                "input_tokens": 100,
                "output_tokens": 200
            },
            # Add stage timings for benchmarking
            "stage_timings": {
                "initial_conversation": 0.5,
                "profile_analysis": 0.8,
                "wheel_generation": 1.2
            },
            # Add execution mode metadata to clearly indicate this was mocked
            "execution_mode": "mock",
            "real_llm_used": False,
            "real_tools_used": False,
            "real_db_used": False,
            "mock_execution_note": "This workflow was executed in mock mode for testing/benchmarking purposes"
        }

        return output

    async def _simulate_agent_execution(self, agent_name: str, workflow_state: Dict[str, Any],
                                      trust_level: int, user_message: str) -> Dict[str, Any]:
        """
        Simulate the execution of a specific agent and generate realistic output.

        Args:
            agent_name: Name of the agent being simulated
            workflow_state: Current workflow state
            trust_level: User's trust level
            user_message: User's original message

        Returns:
            Dict containing agent-specific output data
        """
        # Generate agent-specific outputs based on the agent type
        if agent_name == "orchestrator":
            return {
                "status": "completed",
                "routing_decisions": 6,
                "next_agent": "resource",
                "workflow_stage": "agent_coordination",
                "context_analysis": {
                    "trust_phase": "foundation" if trust_level < 50 else "expansion",
                    "complexity_level": "simple" if trust_level < 40 else "moderate"
                }
            }
        elif agent_name == "resource":
            return {
                "available_time": "30 minutes",
                "physical_resources": ["basic tools", "home environment"],
                "limitations": ["no gym access"] if trust_level < 30 else [],
                "resource_score": min(trust_level + 20, 100),
                "environment_assessment": "supportive" if trust_level > 60 else "neutral"
            }
        elif agent_name == "engagement":
            return {
                "preferred_domains": ["wellness", "productivity"] if trust_level > 50 else ["wellness"],
                "engagement_patterns": {
                    "completion_rate": 0.7 + (trust_level / 100) * 0.3,
                    "preferred_difficulty": "easy" if trust_level < 40 else "medium"
                },
                "historical_preferences": ["meditation", "reading", "walking"]
            }
        elif agent_name == "psychological":
            return {
                "trust_phase": "foundation" if trust_level < 50 else "expansion",
                "mood_assessment": "cautious" if trust_level < 40 else "optimistic",
                "readiness_level": trust_level,
                "recommended_approach": "gentle" if trust_level < 40 else "balanced",
                "psychological_safety": "high" if trust_level > 70 else "moderate"
            }
        elif agent_name == "strategy":
            return {
                "challenge_level": "low" if trust_level < 40 else "moderate",
                "focus": "balanced_growth",
                "strategy_type": "supportive" if trust_level < 50 else "growth_oriented",
                "activity_count": 6 if trust_level < 50 else 8,
                "personalization_level": "high" if trust_level > 70 else "medium"
            }
        elif agent_name == "activity":
            return {
                "activities_generated": 6 if trust_level < 50 else 8,
                "difficulty_distribution": {
                    "easy": 0.7 if trust_level < 40 else 0.5,
                    "medium": 0.3 if trust_level < 40 else 0.4,
                    "hard": 0.0 if trust_level < 40 else 0.1
                },
                "domains_covered": ["wellness", "learning", "social"],
                "customization_applied": True
            }
        elif agent_name == "ethical":
            return {
                "validation_passed": True,
                "concerns": [],
                "safety_score": 95 + (trust_level / 100) * 5,
                "ethical_guidelines_met": True,
                "recommendations": ["maintain supportive tone", "respect user boundaries"]
            }
        else:
            return {
                "status": "completed",
                "agent_type": agent_name,
                "processing_time": 0.05
            }

    def _generate_mock_wheel(self, trust_level: int, user_message: str) -> Dict[str, Any]:
        """Generate mock wheel data based on context."""
        # Determine number of activities based on trust level
        num_activities = 6 if trust_level < 50 else 8

        # Base activities pool
        activities = [
            {"name": "Morning Meditation", "domain": "wellness", "difficulty": "easy"},
            {"name": "Read for 20 minutes", "domain": "learning", "difficulty": "easy"},
            {"name": "Take a nature walk", "domain": "wellness", "difficulty": "easy"},
            {"name": "Practice gratitude journaling", "domain": "wellness", "difficulty": "easy"},
            {"name": "Learn a new skill online", "domain": "learning", "difficulty": "medium"},
            {"name": "Cook a healthy meal", "domain": "wellness", "difficulty": "medium"},
            {"name": "Call a friend or family member", "domain": "social", "difficulty": "easy"},
            {"name": "Organize your workspace", "domain": "productivity", "difficulty": "medium"},
            {"name": "Try a new creative hobby", "domain": "creativity", "difficulty": "medium"},
            {"name": "Exercise for 30 minutes", "domain": "wellness", "difficulty": "medium"}
        ]

        # Select activities based on trust level and message content
        selected_activities = activities[:num_activities]

        # Create wheel items
        wheel_items = []
        for i, activity in enumerate(selected_activities):
            wheel_items.append({
                "id": f"activity_{i+1}",
                "name": activity["name"],
                "description": f"A {activity['difficulty']} {activity['domain']} activity to help you grow",
                "domain": activity["domain"],
                "difficulty": activity["difficulty"],
                "estimated_time": "15-30 minutes",
                "position": i
            })

        return {
            "id": f"wheel_{uuid.uuid4()}",
            "name": "Personal Growth Wheel",
            "description": "A personalized wheel of activities to support your growth journey",
            "items": wheel_items,
            "total_items": len(wheel_items),
            "created_at": "2025-01-31T12:00:00Z",
            "trust_level": trust_level,
            "personalization_level": "high" if trust_level > 70 else "medium"
        }

    def _generate_mock_user_response(self, trust_level: int, user_message: str, wheel_data: Dict[str, Any]) -> str:
        """Generate a mock user response based on context."""
        if trust_level < 40:
            # Foundation phase - supportive and gentle
            return f"I understand you're feeling a bit uncertain today. I've created a gentle wheel with {len(wheel_data['items'])} activities that are simple and not too demanding. Each one is designed to be manageable and supportive of where you are right now. Take your time exploring them, and remember - there's no pressure to do anything that doesn't feel right for you."
        elif trust_level < 70:
            # Building phase - encouraging and balanced
            return f"I've created a personalized wheel with {len(wheel_data['items'])} activities that balance challenge with comfort. These activities are designed to help you grow while respecting your current needs and preferences. Feel free to spin the wheel when you're ready to try something new!"
        else:
            # Expansion phase - collaborative and empowering
            return f"Here's your personalized growth wheel with {len(wheel_data['items'])} carefully selected activities! I've included a mix of challenges and familiar territory based on your preferences and goals. This wheel is designed to push your boundaries while keeping you engaged. Ready to see what adventure awaits?"
