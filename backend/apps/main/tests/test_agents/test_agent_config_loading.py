"""
Tests for agent configuration loading.
"""
import pytest
import logging
import os
import django
from unittest.mock import As<PERSON><PERSON>ock, MagicMock, patch
import datetime
from pydantic import BaseModel

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.test')
django.setup()

# Now import Django models after setup
from apps.main.agents.mentor_agent import MentorAgent
from apps.main.agents.base_agent import LangGraphAgent
from apps.main.services.mock_services import MockDatabaseService
from apps.main.models import LLMConfig

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Define a simple state model for testing
class TestState(BaseModel):
    """Simple state model for testing."""
    context_packet: dict = {}
    output_data: dict = {}
    error: str = None
    error_context: dict = None
    conversation_history: list = []
    current_stage: str = "initial_conversation"
    workflow_id: str = "test-workflow"
    user_ws_session_name: str = "test-session"
    run_id: str = None  # Add run_id field

@pytest.mark.asyncio
@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents")
@patch('mistralai.Mistral')  # Mock LLM client to avoid API key requirement
async def test_agent_config_loading_failure(mock_mistral_client):
    """Test that agent handles configuration loading failures gracefully."""
    # Create a test LLMConfig to avoid environment variable issues
    test_llm_config = LLMConfig(
        name="test-config",
        model_name="mistral-small-latest",
        temperature=0.7,
        is_default=False
    )

    # Create a mock database service that will fail to load agent definition
    mock_db_service = MockDatabaseService()

    # Patch the load_agent_definition method to raise an exception
    original_load_agent_definition = mock_db_service.load_agent_definition

    def failing_load_agent_definition(agent_role):
        """Mock implementation that raises an exception."""
        logger.debug(f"Mock failing to load agent definition for role: {agent_role}")
        raise RuntimeError("Simulated agent definition loading failure")

    mock_db_service.load_agent_definition = failing_load_agent_definition

    # Create the agent with the mock database service and explicit LLMConfig
    agent = MentorAgent(
        user_profile_id="1",  # Use numeric string
        db_service=mock_db_service,
        llm_config=test_llm_config
    )

    # Create a test state
    state = TestState()

    # Process the state, which should trigger the agent definition loading failure
    updated_state = await agent(state)

    # Verify that the agent handled the error gracefully
    assert hasattr(updated_state, "error"), "Missing 'error' attribute in state"
    assert updated_state.error is not None, "Error attribute is None"
    assert hasattr(updated_state, "output_data"), "Missing 'output_data' attribute in state"
    assert "error" in updated_state.output_data, "Missing 'error' key in output_data"
    assert "debug" in updated_state.output_data, "Missing 'debug' key in output_data"
    assert "failed_operation" in updated_state.output_data["debug"], "Missing 'failed_operation' in debug info"
    assert updated_state.output_data["debug"]["failed_operation"] == "ensuring_agent_loaded", "Incorrect failed_operation value"

    # Restore the original method
    mock_db_service.load_agent_definition = original_load_agent_definition

@pytest.mark.asyncio
@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents")
@patch('mistralai.Mistral')  # Mock LLM client to avoid API key requirement
async def test_agent_config_loading_success(mock_mistral_client):
    """Test that agent loads configuration successfully."""
    # Create a test LLMConfig to avoid environment variable issues
    test_llm_config = LLMConfig(
        name="test-config",
        model_name="mistral-small-latest",
        temperature=0.7,
        is_default=False
    )

    # Create a mock database service with a valid agent definition
    mock_db_service = MockDatabaseService()

    # Create a mock agent definition for the mentor role
    mock_agent_definition = MagicMock()
    mock_agent_definition.role = "mentor"
    mock_agent_definition.system_instructions = "You are a helpful mentor agent."
    mock_agent_definition.input_schema = {}
    mock_agent_definition.output_schema = {}
    mock_agent_definition.state_schema = {}
    mock_agent_definition.memory_schema = {}
    mock_agent_definition.version = "1.0.0"
    mock_agent_definition.is_active = True
    mock_agent_definition.langgraph_node_class = "apps.main.agents.mentor_agent.MentorAgent"
    mock_agent_definition.processing_timeout = 30
    mock_agent_definition.llm_config = test_llm_config
    mock_agent_definition.get_available_tools_dict = lambda: []

    # Add the agent definition to the mock service
    mock_db_service.agent_definitions["mentor"] = mock_agent_definition

    # Create the agent with the mock database service and explicit LLMConfig
    agent = MentorAgent(
        user_profile_id="1",  # Use a numeric string that can be converted to int
        db_service=mock_db_service,
        llm_config=test_llm_config
    )

    # Create a test state
    state = TestState()

    # Mock the _get_llm_response method to avoid actual LLM calls
    async def mock_get_llm_response(*args, **kwargs):
        """Mock implementation that returns a simple response."""
        mock_response = MagicMock()
        mock_response.content = "This is a test response."
        mock_response.response_type = "text"
        mock_response.input_tokens = 10
        mock_response.output_tokens = 5
        return mock_response

    # Mock the db_service.start_run and complete_run methods
    async def mock_start_run(*args, **kwargs):
        """Mock implementation that returns a run object."""
        mock_run = MagicMock()
        mock_run.id = "test-run-id"

        # Explicitly set the run_id in the state
        state.run_id = "test-run-id"

        return mock_run

    async def mock_complete_run(*args, **kwargs):
        """Mock implementation that does nothing."""
        return None

    # Apply the patches
    with patch.object(agent, '_get_llm_response', mock_get_llm_response), \
         patch.object(mock_db_service, 'start_run', mock_start_run), \
         patch.object(mock_db_service, 'complete_run', mock_complete_run):

        # Process the state
        updated_state = await agent(state)

    # Verify that the agent processed the state successfully
    assert not hasattr(updated_state, "error") or updated_state.error is None, f"Unexpected 'error' attribute in state: {updated_state.error if hasattr(updated_state, 'error') else 'None'}"
    assert hasattr(updated_state, "output_data"), "Missing 'output_data' attribute in state"
    assert "user_response" in updated_state.output_data, "Missing 'user_response' key in output_data"
    # The run_id might be in the output_data or as a direct attribute, check both
    assert "run_id" in updated_state.output_data or hasattr(updated_state, "run_id"), "Missing 'run_id' in state or output_data"
    if "run_id" in updated_state.output_data:
        assert updated_state.output_data["run_id"] == "test-run-id", "Incorrect run_id value in output_data"
    elif hasattr(updated_state, "run_id"):
        assert updated_state.run_id == "test-run-id", "Incorrect run_id value in state attribute"

@pytest.mark.asyncio
@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents")
@patch('mistralai.Mistral')  # Mock LLM client to avoid API key requirement
async def test_agent_missing_output_data_handling(mock_mistral_client):
    """Test that agent handles missing output_data gracefully."""
    # Create a test LLMConfig to avoid environment variable issues
    test_llm_config = LLMConfig(
        name="test-config",
        model_name="mistral-small-latest",
        temperature=0.7,
        is_default=False
    )

    # Create a mock database service
    mock_db_service = MockDatabaseService()

    # Create the agent with the mock database service and explicit LLMConfig
    agent = MentorAgent(
        user_profile_id="1",  # Use a numeric string that can be converted to int
        db_service=mock_db_service,
        llm_config=test_llm_config
    )

    # Create a test state
    state = TestState()

    # Create a custom process method that returns state updates without output_data
    original_process = agent.process

    async def process_without_output_data(state):
        """Mock process method that returns state updates without output_data."""
        return {"run_id": "test-run-id", "current_stage": "next_stage"}

    # Apply the patch
    agent.process = process_without_output_data

    # Process the state
    updated_state = await agent(state)

    # Verify that the agent handled the missing output_data gracefully
    assert hasattr(updated_state, "output_data"), "Missing 'output_data' attribute in state"
    assert "error" in updated_state.output_data, "Missing 'error' key in output_data"
    assert "debug" in updated_state.output_data, "Missing 'debug' key in output_data"
    assert "last_error" in updated_state.output_data["debug"], "Missing 'last_error' in debug info"

    # Restore the original method
    agent.process = original_process
