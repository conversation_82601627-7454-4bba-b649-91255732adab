from typing import Dict, Any, Optional, List
from pydantic import field_validator, model_validator, Field
from .base import VersionedModel, BenchmarkBaseModel, logger


class EvaluationCriterion(BenchmarkBaseModel):
    """A single evaluation criterion for benchmark scenarios."""
    dimension: str
    description: str
    weight: float = 1.0

    @field_validator('weight')
    def validate_weight(cls, v):
        """Validate that weight is between 0 and 1."""
        if v < 0 or v > 1:
            raise ValueError("Weight must be between 0 and 1")
        return v


class EvaluationCriteria(BenchmarkBaseModel):
    """Collection of evaluation criteria for benchmark scenarios."""
    criteria: List[EvaluationCriterion]

    def get_dimensions(self) -> List[str]:
        """Get the list of unique dimensions in the criteria."""
        return list(set(c.dimension for c in self.criteria))

    def get_criteria_by_dimension(self, dimension: str) -> List[EvaluationCriterion]:
        """Get all criteria for a specific dimension."""
        return [c for c in self.criteria if c.dimension == dimension]

    def get_weight_for_dimension(self, dimension: str) -> float:
        """Get the total weight for a specific dimension."""
        criteria = self.get_criteria_by_dimension(dimension)
        if not criteria:
            return 0.0
        return sum(c.weight for c in criteria)


class PhaseAwareCriteria(BenchmarkBaseModel):
    """Phase-aware evaluation criteria for benchmark scenarios.

    This model supports different evaluation criteria for different trust phases:
    - foundation (0-39): Basic support with focus on clarity and simplicity
    - expansion (40-69): Growth-oriented with increasing complexity
    - integration (70-100): Advanced synthesis with philosophical depth
    """
    foundation: Optional[Dict[str, List[str]]] = None
    expansion: Optional[Dict[str, List[str]]] = None
    integration: Optional[Dict[str, List[str]]] = None

    def get_criteria_for_phase(self, phase: str) -> Optional[Dict[str, List[str]]]:
        """Get the evaluation criteria for a specific trust phase."""
        if phase == "foundation" and self.foundation:
            return self.foundation
        elif phase == "expansion" and self.expansion:
            return self.expansion
        elif phase == "integration" and self.integration:
            return self.integration
        return None

    def get_criteria_for_trust_level(self, trust_level: int) -> Optional[Dict[str, List[str]]]:
        """Get the evaluation criteria for a specific trust level."""
        if trust_level < 40:
            return self.get_criteria_for_phase("foundation")
        elif trust_level < 70:
            return self.get_criteria_for_phase("expansion")
        else:
            return self.get_criteria_for_phase("integration")

    def get_phase_for_trust_level(self, trust_level: int) -> str:
        """Get the trust phase for a specific trust level."""
        if trust_level < 40:
            return "foundation"
        elif trust_level < 70:
            return "expansion"
        else:
            return "integration"


class ToolExpectation(BenchmarkBaseModel):
    """Expectation for a tool call in a benchmark scenario."""
    tool_name: str
    expected_calls: Optional[int] = None
    parameters: Optional[Dict[str, Any]] = None  # Added parameters field
    mock_responses: Optional[List[Dict[str, Any]]] = None
    conditional_responses: Optional[List[Dict[str, Any]]] = None
    error_simulation: Optional[Dict[str, Any]] = None

    model_config = {
        "extra": "allow",  # Allow extra fields for backward compatibility
    }


class BenchmarkScenarioMetadata(BenchmarkBaseModel):
    """Metadata for benchmark scenarios."""
    user_profile_context: Optional[Dict[str, Any]] = None
    expected_quality_criteria: Optional[Dict[str, List[str]]] = None
    evaluation_criteria_by_phase: Optional[PhaseAwareCriteria] = None
    evaluator_models: Optional[List[str]] = None
    mock_tool_responses: Optional[Dict[str, Any]] = None
    mock_responses: Optional[Dict[str, Any]] = None  # Added for backward compatibility with test_validate_benchmark_scenario_with_separate_mock_responses
    tool_expectations: Optional[List[ToolExpectation]] = None
    workflow_type: Optional[str] = None
    situation: Optional[Dict[str, Any]] = None
    evaluation_criteria: Optional[Dict[str, Any]] = None
    warmup_runs: Optional[int] = 1

    # Contextual evaluation fields
    evaluation_template_id: Optional[int] = Field(
        None,
        description="ID of the EvaluationCriteriaTemplate to use for contextual evaluation"
    )
    context: Optional[Dict[str, Any]] = Field(
        None,
        description="Contextual variables for evaluation adaptation (trust_level, mood, environment)"
    )
    benchmark_runs: Optional[int] = 3

    model_config = {
        "extra": "allow",  # Allow extra fields for backward compatibility
    }

    @field_validator('mock_responses')
    def validate_mock_responses(cls, v, info):
        """
        Validate mock_responses and sync with mock_tool_responses if needed.
        This is for backward compatibility with older test code.
        """
        if v is not None:
            # Get the current values
            values = info.data

            # If mock_tool_responses is not set, use mock_responses
            if 'mock_tool_responses' not in values or values['mock_tool_responses'] is None:
                # We can't modify values directly in Pydantic v2, but we can log this
                logger.debug("mock_responses field is used, should be migrated to mock_tool_responses",
                            extra={"mock_responses": v})
        return v

    @field_validator('mock_tool_responses')
    def validate_mock_tool_responses(cls, v):
        """
        Validate mock_tool_responses to ensure it has the correct structure.
        """
        if v is not None:
            # Log the structure for debugging
            logger.debug("Validating mock_tool_responses",
                        extra={"mock_tool_responses": v})

            # Ensure each tool response has the correct structure
            for tool_name, response in v.items():
                if isinstance(response, dict):
                    # Check if it's a structured response with 'response' field
                    if 'response' not in response and not any(k.startswith('condition') for k in response.keys()):
                        logger.warning(f"Tool response for {tool_name} is missing 'response' field",
                                    extra={"tool_name": tool_name, "response": response})
                elif not isinstance(response, str) and not isinstance(response, list):
                    logger.warning(f"Tool response for {tool_name} has invalid type: {type(response)}",
                                extra={"tool_name": tool_name, "response_type": str(type(response))})
        return v


class BenchmarkScenario(VersionedModel):
    """Schema for benchmark scenarios with structured fields."""
    version: str = "1.0.0"
    name: str
    description: Optional[str] = None
    agent_role: str
    input_data: Dict[str, Any]

    # Structured fields (extracted from common metadata usage)
    workflow_type: Optional[str] = Field(
        None,
        description="Type of workflow this scenario tests (e.g., 'wheel_generation', 'discussion')"
    )
    warmup_runs: int = Field(
        1,
        description="Number of warmup runs before actual benchmarking"
    )
    benchmark_runs: int = Field(
        3,
        description="Number of benchmark runs to perform"
    )
    timeout_seconds: Optional[int] = Field(
        None,
        description="Timeout in seconds for scenario execution"
    )
    evaluation_template_id: Optional[int] = Field(
        None,
        description="ID of the evaluation criteria template to use"
    )
    evaluation_template_name: Optional[str] = Field(
        None,
        description="Name of the evaluation criteria template to use"
    )
    expected_quality_criteria: Optional[Dict[str, Any]] = Field(
        None,
        description="Expected quality criteria for evaluation"
    )
    mock_tool_responses: Optional[Dict[str, Any]] = Field(
        None,
        description="Mock responses for tool calls during testing"
    )
    user_profile_context: Optional[Dict[str, Any]] = Field(
        None,
        description="User profile context for the scenario"
    )
    activity_context: Optional[Dict[str, Any]] = Field(
        None,
        description="Activity-specific context for the scenario"
    )

    # Legacy metadata field for backward compatibility
    metadata: Optional[BenchmarkScenarioMetadata] = Field(
        None,
        description="Additional metadata and legacy fields"
    )

    is_active: bool = True

    @field_validator('warmup_runs', 'benchmark_runs')
    @classmethod
    def validate_run_counts(cls, v):
        """Validate that run counts are positive."""
        if v <= 0:
            raise ValueError("Run counts must be positive")
        if v > 100:
            raise ValueError("Run counts must be 100 or less")
        return v

    @field_validator('timeout_seconds')
    @classmethod
    def validate_timeout(cls, v):
        """Validate timeout is reasonable."""
        if v is not None and (v <= 0 or v > 3600):
            raise ValueError("Timeout must be between 1 and 3600 seconds")
        return v

    @field_validator('workflow_type')
    @classmethod
    def validate_workflow_type(cls, v):
        """Validate workflow type format."""
        if v is not None:
            # Basic validation - could be expanded with allowed values
            if not v.strip():
                raise ValueError("Workflow type cannot be empty")
            if len(v) > 50:
                raise ValueError("Workflow type must be 50 characters or less")
            # Validate against known workflow types (including test types)
            allowed_types = [
                'wheel_generation', 'discussion', 'activity_feedback',
                'goal_setting', 'progress_tracking', 'reflection',
                # Test workflow types for testing purposes
                'test_workflow', 'test_workflow_1', 'test_workflow_2',
                'workflow1', 'workflow2', 'updated_workflow'
            ]
            if v not in allowed_types:
                raise ValueError(f"Workflow type must be one of: {', '.join(allowed_types)}")
        return v

    @field_validator('agent_role')
    @classmethod
    def validate_agent_role(cls, v):
        """Validate agent role format."""
        if not v or not v.strip():
            raise ValueError("Agent role is required")
        if len(v) > 50:
            raise ValueError("Agent role must be 50 characters or less")
        # Validate against known agent roles
        allowed_roles = [
            'mentor', 'orchestrator', 'strategy', 'feedback',
            'analysis', 'content_generator', 'evaluator'
        ]
        if v not in allowed_roles:
            raise ValueError(f"Agent role must be one of: {', '.join(allowed_roles)}")
        return v

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate scenario name."""
        if not v or not v.strip():
            raise ValueError("Scenario name is required")
        if len(v) > 100:
            raise ValueError("Scenario name must be 100 characters or less")
        # Check for valid characters (alphanumeric, spaces, hyphens, underscores)
        import re
        if not re.match(r'^[a-zA-Z0-9\s\-_]+$', v):
            raise ValueError("Scenario name can only contain letters, numbers, spaces, hyphens, and underscores")
        return v.strip()

    @field_validator('input_data')
    @classmethod
    def validate_input_data(cls, v):
        """Validate input data structure."""
        if not isinstance(v, dict):
            raise ValueError("Input data must be a dictionary")
        if not v:
            raise ValueError("Input data cannot be empty")

        # Check for required fields based on common patterns
        required_fields = ['user_message', 'context']
        missing_fields = [field for field in required_fields if field not in v]
        if missing_fields:
            raise ValueError(f"Input data missing required fields: {', '.join(missing_fields)}")

        # Validate user_message
        if not isinstance(v.get('user_message'), str) or not v['user_message'].strip():
            raise ValueError("Input data 'user_message' must be a non-empty string")

        return v

    @field_validator('expected_quality_criteria')
    @classmethod
    def validate_quality_criteria(cls, v):
        """Validate quality criteria structure."""
        if v is not None:
            if not isinstance(v, dict):
                raise ValueError("Expected quality criteria must be a dictionary")

            # Validate structure: each key should map to a list of criteria
            for dimension, criteria in v.items():
                if not isinstance(dimension, str) or not dimension.strip():
                    raise ValueError("Quality criteria dimensions must be non-empty strings")

                if not isinstance(criteria, (list, str)):
                    raise ValueError(f"Quality criteria for '{dimension}' must be a list or string")

                if isinstance(criteria, list):
                    for criterion in criteria:
                        if not isinstance(criterion, str) or not criterion.strip():
                            raise ValueError(f"All criteria in '{dimension}' must be non-empty strings")

        return v

    @field_validator('mock_tool_responses')
    @classmethod
    def validate_mock_tool_responses(cls, v):
        """Validate mock tool responses structure."""
        if v is not None:
            if not isinstance(v, dict):
                raise ValueError("Mock tool responses must be a dictionary")

            # Validate each tool response
            for tool_name, response in v.items():
                if not isinstance(tool_name, str) or not tool_name.strip():
                    raise ValueError("Tool names must be non-empty strings")

                # Response can be any JSON-serializable structure
                try:
                    import json
                    json.dumps(response)
                except (TypeError, ValueError):
                    raise ValueError(f"Mock response for '{tool_name}' must be JSON-serializable")

        return v

    @field_validator('user_profile_context', 'activity_context')
    @classmethod
    def validate_context_fields(cls, v):
        """Validate context field structures."""
        if v is not None:
            if not isinstance(v, dict):
                raise ValueError("Context fields must be dictionaries")

            # Validate that context has meaningful content
            if v and not any(isinstance(val, (str, int, float, bool, list, dict)) for val in v.values()):
                raise ValueError("Context must contain valid data types")

        return v

    @model_validator(mode='after')
    def validate_scenario_consistency(self):
        """Validate consistency across fields."""
        # Ensure evaluation template is specified if quality criteria are provided
        if self.expected_quality_criteria and not (self.evaluation_template_id or self.evaluation_template_name):
            raise ValueError("Evaluation template must be specified when quality criteria are provided")

        # Ensure workflow type matches agent role expectations
        workflow_agent_mapping = {
            'wheel_generation': ['mentor', 'orchestrator'],
            'discussion': ['mentor', 'strategy'],
            'activity_feedback': ['mentor', 'feedback'],
            'goal_setting': ['mentor', 'strategy'],
            'progress_tracking': ['analysis', 'mentor'],
            'reflection': ['mentor', 'analysis']
        }

        if self.workflow_type and self.agent_role:
            expected_agents = workflow_agent_mapping.get(self.workflow_type, [])
            if expected_agents and self.agent_role not in expected_agents:
                raise ValueError(
                    f"Agent role '{self.agent_role}' is not typically used with workflow type '{self.workflow_type}'. "
                    f"Expected agents: {', '.join(expected_agents)}"
                )

        # Validate that mock responses match expected tools for the workflow
        if self.mock_tool_responses and self.workflow_type:
            expected_tools = self._get_expected_tools_for_workflow(self.workflow_type)
            provided_tools = set(self.mock_tool_responses.keys())
            missing_tools = expected_tools - provided_tools

            if missing_tools:
                raise ValueError(
                    f"Missing mock responses for expected tools: {', '.join(missing_tools)}"
                )

        return self

    def _get_expected_tools_for_workflow(self, workflow_type: str) -> set:
        """Get expected tools for a given workflow type."""
        workflow_tools = {
            'wheel_generation': {'get_user_profile', 'save_wheel', 'get_activities'},
            'discussion': {'get_user_profile', 'save_message', 'get_conversation_history'},
            'activity_feedback': {'get_user_profile', 'get_activity', 'save_feedback'},
            'goal_setting': {'get_user_profile', 'save_goal', 'get_user_goals'},
            'progress_tracking': {'get_user_profile', 'get_user_progress', 'update_progress'},
            'reflection': {'get_user_profile', 'get_user_history', 'save_reflection'}
        }
        return workflow_tools.get(workflow_type, set())

    @classmethod
    def validate_scenario(cls, data: dict) -> 'BenchmarkScenario':
        """Validate a scenario against the schema.

        Args:
            data: The scenario data to validate.

        Returns:
            BenchmarkScenario: The validated scenario instance.

        Raises:
            Exception: If validation fails.
        """
        try:
            instance = cls(**data)
            logger.info("Scenario validation successful",
                extra={"scenario_name": instance.name,
                      "agent_role": instance.agent_role})
            return instance
        except Exception as e:
            logger.error("Scenario validation failed",
                extra={"data": data, "error": str(e)},
                exc_info=True)
            raise

    @classmethod
    def validate_scenario_file(cls, file_path: str) -> 'BenchmarkScenario':
        """
        Validate a scenario from a JSON file.

        Args:
            file_path: Path to the JSON file containing scenario data

        Returns:
            BenchmarkScenario: Validated scenario instance

        Raises:
            ValidationError: If validation fails
            FileNotFoundError: If file doesn't exist
            JSONDecodeError: If file contains invalid JSON
        """
        import json
        from pathlib import Path

        path = Path(file_path)
        if not path.exists():
            raise FileNotFoundError(f"Scenario file not found: {file_path}")

        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return cls.validate_scenario(data)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in scenario file {file_path}: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to validate scenario file {file_path}: {e}")
            raise

    def to_dict(self) -> dict:
        """
        Convert the scenario to a dictionary suitable for JSON serialization.

        Returns:
            dict: Scenario data as dictionary
        """
        return self.model_dump(exclude_none=True)

    def to_json(self) -> str:
        """
        Convert the scenario to a JSON string.

        Returns:
            str: Scenario data as JSON string
        """
        return self.model_dump_json(exclude_none=True, indent=2)

    def get_validation_summary(self) -> dict:
        """
        Get a summary of the scenario's validation status.

        Returns:
            dict: Validation summary with status and details
        """
        summary = {
            'is_valid': True,
            'warnings': [],
            'recommendations': [],
            'completeness_score': 0.0
        }

        # Check completeness
        completeness_factors = {
            'has_description': bool(self.description and self.description.strip()),
            'has_workflow_type': bool(self.workflow_type),
            'has_quality_criteria': bool(self.expected_quality_criteria),
            'has_mock_responses': bool(self.mock_tool_responses),
            'has_user_context': bool(self.user_profile_context),
            'has_evaluation_template': bool(self.evaluation_template_id or self.evaluation_template_name),
            'reasonable_run_counts': self.warmup_runs >= 1 and self.benchmark_runs >= 3,
            'has_timeout': bool(self.timeout_seconds)
        }

        summary['completeness_score'] = sum(completeness_factors.values()) / len(completeness_factors)

        # Generate warnings and recommendations
        if not completeness_factors['has_description']:
            summary['warnings'].append("Missing or empty description")

        if not completeness_factors['has_workflow_type']:
            summary['warnings'].append("Workflow type not specified")

        if not completeness_factors['has_quality_criteria']:
            summary['recommendations'].append("Consider adding expected quality criteria for better evaluation")

        if not completeness_factors['has_mock_responses']:
            summary['recommendations'].append("Consider adding mock tool responses for consistent testing")

        if self.benchmark_runs < 3:
            summary['recommendations'].append("Consider using at least 3 benchmark runs for statistical significance")

        if not completeness_factors['has_timeout']:
            summary['recommendations'].append("Consider setting a timeout to prevent hanging tests")

        return summary

    def is_compatible_with_agent(self, agent_role: str) -> bool:
        """
        Check if this scenario is compatible with a given agent role.

        Args:
            agent_role: The agent role to check compatibility with

        Returns:
            bool: True if compatible, False otherwise
        """
        if not self.workflow_type:
            return True  # No workflow type specified, assume compatible

        workflow_agent_mapping = {
            'wheel_generation': ['mentor', 'orchestrator'],
            'discussion': ['mentor', 'strategy'],
            'activity_feedback': ['mentor', 'feedback'],
            'goal_setting': ['mentor', 'strategy'],
            'progress_tracking': ['analysis', 'mentor'],
            'reflection': ['mentor', 'analysis']
        }

        expected_agents = workflow_agent_mapping.get(self.workflow_type, [])
        return not expected_agents or agent_role in expected_agents

    def get_missing_mock_tools(self) -> List[str]:
        """
        Get list of tools that should have mock responses but don't.

        Returns:
            List[str]: List of missing tool names
        """
        if not self.workflow_type:
            return []

        expected_tools = self._get_expected_tools_for_workflow(self.workflow_type)
        provided_tools = set(self.mock_tool_responses.keys()) if self.mock_tool_responses else set()

        return list(expected_tools - provided_tools)
