"""
Tool Call Interceptor

This module provides functionality to intercept and log tool calls during
workflow execution for Phase 1 & Phase 2 Data Model Enhancement. It captures tool
inputs, outputs, execution modes, performance metrics, and advanced monitoring data.

Phase 2 Enhancements:
- Tool call sequence tracking and analysis
- Performance correlation monitoring
- Real-time state capture integration
"""

import time
import logging
from typing import Dict, Any, Optional, Callable, Awaitable, List
from functools import wraps
from apps.main.services.agent_communication_tracker import AgentCommunicationTracker

logger = logging.getLogger(__name__)


class ToolCallInterceptor:
    """
    Intercepts tool calls to capture detailed debugging information.
    
    This class provides decorators and context managers to automatically
    capture tool call information during agent execution.
    """
    
    def __init__(self, tracker: Optional[AgentCommunicationTracker] = None):
        """
        Initialize the interceptor.

        Args:
            tracker: AgentCommunicationTracker instance to record tool calls
        """
        self.tracker = tracker
        self.enabled = tracker is not None and tracker.enabled
        # Phase 2: Track tool call sequences
        self._current_sequence_calls: List[str] = []
        self._sequence_start_time: Optional[float] = None
        
    def intercept_tool_call(self, tool_name: str, execution_mode: str = "unknown"):
        """
        Decorator to intercept tool calls and capture execution data.
        
        Args:
            tool_name: Name of the tool being called
            execution_mode: "real" or "mock" execution mode
            
        Returns:
            Decorator function
        """
        def decorator(func: Callable[..., Awaitable[Any]]):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                if not self.enabled:
                    return await func(*args, **kwargs)
                
                # Extract tool input from arguments
                tool_input = self._extract_tool_input_from_args(args, kwargs)
                
                # Record start time
                start_time = time.perf_counter()
                
                try:
                    # Call the original tool function
                    result = await func(*args, **kwargs)
                    
                    # Calculate duration
                    duration_ms = (time.perf_counter() - start_time) * 1000
                    
                    # Extract tool output
                    tool_output = self._extract_tool_output(result)
                    
                    # Record the tool call
                    if self.tracker:
                        tool_call_id = self.tracker.record_tool_call(
                            tool_name=tool_name,
                            tool_input=tool_input,
                            tool_output=tool_output,
                            execution_mode=execution_mode,
                            duration_ms=duration_ms,
                            success=True
                        )

                        # Phase 2: Track tool call sequences
                        self._track_tool_sequence(tool_call_id)
                    
                    return result
                    
                except Exception as e:
                    # Calculate duration even for failed calls
                    duration_ms = (time.perf_counter() - start_time) * 1000
                    
                    # Record the failed tool call
                    if self.tracker:
                        tool_call_id = self.tracker.record_tool_call(
                            tool_name=tool_name,
                            tool_input=tool_input,
                            tool_output={},
                            execution_mode=execution_mode,
                            duration_ms=duration_ms,
                            success=False,
                            error=str(e)
                        )

                        # Phase 2: Track tool call sequences (even for failed calls)
                        self._track_tool_sequence(tool_call_id)
                    
                    # Re-raise the exception
                    raise
                    
            return wrapper
        return decorator
    
    def _extract_tool_input_from_args(self, args: tuple, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract tool input from function arguments.
        
        Args:
            args: Positional arguments
            kwargs: Keyword arguments
            
        Returns:
            Extracted tool input data
        """
        # Start with kwargs as base input
        tool_input = dict(kwargs)
        
        # Add positional arguments with generic names
        for i, arg in enumerate(args):
            if isinstance(arg, (dict, list, str, int, float, bool)):
                tool_input[f'arg_{i}'] = arg
        
        # Remove internal parameters that shouldn't be logged
        internal_params = ['self', 'cls', 'tracker', 'logger']
        for param in internal_params:
            tool_input.pop(param, None)
        
        return tool_input
    
    def _extract_tool_output(self, result: Any) -> Dict[str, Any]:
        """
        Extract tool output from the result.
        
        Args:
            result: Tool execution result
            
        Returns:
            Extracted tool output data
        """
        if isinstance(result, dict):
            return result
        elif isinstance(result, (list, str, int, float, bool)):
            return {'result': result}
        elif hasattr(result, '__dict__'):
            # For objects, try to extract their attributes
            try:
                return {k: v for k, v in result.__dict__.items() 
                       if not k.startswith('_')}
            except:
                return {'result': str(result)}
        else:
            return {'result': str(result)}

    # Phase 2 Methods

    def _track_tool_sequence(self, tool_call_id: str) -> None:
        """
        Track tool call sequences for Phase 2 analysis.

        Args:
            tool_call_id: ID of the tool call to track
        """
        if not self.enabled or not self.tracker:
            return

        # Initialize sequence tracking if needed
        if self._sequence_start_time is None:
            self._sequence_start_time = time.perf_counter()
            self._current_sequence_calls = []

        # Add to current sequence
        self._current_sequence_calls.append(tool_call_id)

        # Check if sequence should be finalized (e.g., after a delay)
        current_time = time.perf_counter()
        if current_time - self._sequence_start_time > 5.0:  # 5 second window
            self._finalize_tool_sequence()

    def _finalize_tool_sequence(self) -> None:
        """
        Finalize the current tool call sequence and analyze it.
        """
        if not self.enabled or not self.tracker or not self._current_sequence_calls:
            return

        # Analyze the sequence if we have multiple calls
        if len(self._current_sequence_calls) > 1:
            # Get current agent from tracker
            current_agent = getattr(self.tracker, '_current_agent', 'unknown')

            # Analyze the tool call sequence
            self.tracker.analyze_tool_call_sequence(current_agent, self._current_sequence_calls)

        # Reset sequence tracking
        self._current_sequence_calls = []
        self._sequence_start_time = None

    def start_sequence_tracking(self) -> None:
        """
        Explicitly start tracking a new tool call sequence.
        """
        if self.enabled:
            self._sequence_start_time = time.perf_counter()
            self._current_sequence_calls = []

    def end_sequence_tracking(self) -> None:
        """
        Explicitly end the current tool call sequence tracking.
        """
        if self.enabled:
            self._finalize_tool_sequence()


def create_tool_interceptor_for_tracker(tracker: AgentCommunicationTracker) -> ToolCallInterceptor:
    """
    Create a tool call interceptor for the given tracker.
    
    Args:
        tracker: AgentCommunicationTracker instance
        
    Returns:
        ToolCallInterceptor instance
    """
    return ToolCallInterceptor(tracker)


def intercept_tool_execution(tool_func, tool_name: str, execution_mode: str, 
                           tracker: AgentCommunicationTracker):
    """
    Intercept a tool execution function.
    
    Args:
        tool_func: Tool function to intercept
        tool_name: Name of the tool
        execution_mode: "real" or "mock"
        tracker: Tracker to record tool calls
        
    Returns:
        Intercepted tool function
    """
    if not tracker or not tracker.enabled:
        return tool_func
    
    interceptor = ToolCallInterceptor(tracker)
    return interceptor.intercept_tool_call(tool_name, execution_mode)(tool_func)


def intercept_agent_tool_calls(agent, tracker: AgentCommunicationTracker):
    """
    Intercept tool calls for a specific agent.
    
    Args:
        agent: Agent instance to intercept
        tracker: Tracker to record tool calls
    """
    if not tracker or not tracker.enabled:
        return
    
    # Check if agent has a _call_tool method
    if hasattr(agent, '_call_tool'):
        original_call_tool = agent._call_tool
        
        async def intercepted_call_tool(*args, **kwargs):
            """Intercepted _call_tool method that handles multiple calling patterns."""
            # Handle different calling patterns
            if args:
                # Pattern 1: tool_name as positional argument
                tool_name = args[0]
                tool_input = args[1] if len(args) > 1 else kwargs.get('tool_input', {})
            elif 'tool_code' in kwargs:
                # Pattern 2: tool_code as keyword argument (engagement agent pattern)
                tool_name = kwargs.pop('tool_code')
                tool_input = kwargs.pop('tool_input', {})
            elif 'tool_name' in kwargs:
                # Pattern 3: tool_name as keyword argument
                tool_name = kwargs.pop('tool_name')
                tool_input = kwargs.pop('tool_input', {})
            else:
                # Fallback: try to extract from first argument
                tool_name = args[0] if args else "unknown_tool"
                tool_input = args[1] if len(args) > 1 else {}

            # Determine execution mode based on agent configuration
            execution_mode = "real"
            if hasattr(agent, 'use_real_tools') and not agent.use_real_tools:
                execution_mode = "mock"

            # Create interceptor and apply decorator
            interceptor = ToolCallInterceptor(tracker)
            decorated_method = interceptor.intercept_tool_call(tool_name, execution_mode)(original_call_tool)

            # Call the decorated method with the original calling pattern
            if args:
                return await decorated_method(*args, **kwargs)
            else:
                # Reconstruct the original call with tool_code pattern
                kwargs['tool_code'] = tool_name
                kwargs['tool_input'] = tool_input
                return await decorated_method(**kwargs)
        
        # Replace the method
        agent._call_tool = intercepted_call_tool
        
        logger.debug(f"Intercepted tool calls for agent {agent.agent_role}")


def intercept_tools_util_execute_tool(tracker: AgentCommunicationTracker):
    """
    Intercept the global execute_tool function from tools_util.
    
    Args:
        tracker: Tracker to record tool calls
    """
    if not tracker or not tracker.enabled:
        return
    
    try:
        from apps.main.agents.tools.tools_util import execute_tool
        
        # Store original function
        original_execute_tool = execute_tool
        
        async def intercepted_execute_tool(tool_code: str, tool_input: Dict[str, Any], 
                                         run_id: Optional[str] = None, 
                                         user_profile_id: Optional[str] = None):
            """Intercepted execute_tool function."""
            # Determine execution mode (this would need to be enhanced based on actual logic)
            execution_mode = "real"  # Default assumption
            
            # Create interceptor and apply decorator
            interceptor = ToolCallInterceptor(tracker)
            decorated_method = interceptor.intercept_tool_call(tool_code, execution_mode)(original_execute_tool)
            
            # Call the decorated method
            return await decorated_method(tool_code, tool_input, run_id, user_profile_id)
        
        # This would require monkey-patching the module, which is complex
        # For now, we'll focus on agent-level interception
        logger.debug("Tool util interception setup (agent-level interception preferred)")
        
    except ImportError:
        logger.debug("Could not import execute_tool for interception")


def setup_comprehensive_tool_interception(agent, tracker: AgentCommunicationTracker):
    """
    Set up comprehensive tool call interception for an agent.
    
    Args:
        agent: Agent instance
        tracker: Tracker to record tool calls
    """
    if not tracker or not tracker.enabled:
        return
    
    # Intercept agent's tool calls
    intercept_agent_tool_calls(agent, tracker)
    
    # Add processing step to track tool interception setup
    if hasattr(tracker, 'add_processing_step'):
        tracker.add_processing_step(
            "tool_interception_setup",
            {
                "agent": agent.agent_role,
                "has_call_tool": hasattr(agent, '_call_tool'),
                "interception_enabled": True
            }
        )
    
    logger.debug(f"Set up comprehensive tool interception for agent {agent.agent_role}")
