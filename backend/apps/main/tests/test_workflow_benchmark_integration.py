"""
Integration test for workflow benchmark fixes.
Tests the complete workflow benchmark execution pipeline.
"""
from django.test import TestCase
from django.contrib.auth.models import User

from apps.main.models import BenchmarkScenario, GenericAgent, BenchmarkRun, LLMConfig
from apps.main.graphs.wheel_generation_graph import _configure_agent_for_execution_mode, WheelGenerationState
from tests.factories import GenericAgentFactory


class TestWorkflowBenchmarkIntegration(TestCase):
    """Integration tests for workflow benchmark fixes."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(username='testuser', password='testpass')
        
        # Create an LLM config
        self.llm_config = LLMConfig.objects.create(
            name="test-gpt-4",
            model_name="gpt-4",
            temperature=0.7
        )
        
        # Create a workflow scenario
        self.workflow_scenario = BenchmarkScenario.objects.create(
            name="Integration Test Workflow Scenario",
            description="Integration test scenario for workflow benchmarks",
            agent_role="orchestrator",
            input_data={"user_message": "Test integration message"},
            metadata={"expected_response": "Test response", "workflow_type": "wheel_generation"}
        )
        
        # Create an agent definition
        self.agent_def = GenericAgentFactory(
            description="Test orchestrator agent",
            system_instructions="Test instructions",
            input_schema={"type": "object"},
            output_schema={"type": "object"},
            langgraph_node_class="test.TestNode",
            llm_config=self.llm_config
        )

    def test_workflow_state_configuration(self):
        """Test that workflow state can be properly configured for benchmarking."""
        # Create a workflow state for testing
        state = WheelGenerationState(
            user_profile_id="test_user_123",
            use_real_llm=False,
            use_real_tools=False,
            use_real_db=False,
            mock_tools={"test_tool": "mock_implementation"}
        )
        
        # Verify state configuration
        self.assertEqual(state.user_profile_id, "test_user_123")
        self.assertFalse(state.use_real_llm)
        self.assertFalse(state.use_real_tools)
        self.assertFalse(state.use_real_db)
        self.assertIsNotNone(state.mock_tools)
        
        # Test that the state can be used for agent configuration
        import asyncio
        
        async def test_agent_config():
            agent_kwargs = await _configure_agent_for_execution_mode(
                state, "OrchestratorAgent", "test_user_123"
            )
            return agent_kwargs
        
        agent_kwargs = asyncio.run(test_agent_config())
        
        # Verify that tool_registry is not in the kwargs (this was the main bug)
        self.assertNotIn('tool_registry', agent_kwargs)
        
        # Verify that other expected configurations are present
        self.assertIsInstance(agent_kwargs, dict)

    def test_benchmark_run_creation_and_classification(self):
        """Test that benchmark runs are created correctly and classified properly."""
        # Create a workflow benchmark run
        workflow_run = BenchmarkRun.objects.create(
            scenario=self.workflow_scenario,
            agent_definition=self.agent_def,
            agent_version="test_integration",
            parameters={"workflow_type": "wheel_generation", "runs": 1},
            raw_results={
                "performance": {"runs": 1, "success_rate": 1.0},
                "operations": {"tool_calls": {}},
                "errors": [],
                "last_output": {"user_response": "Test response"}
            },
            runs_count=1,
            mean_duration=150.0,
            median_duration=150.0,
            min_duration=150.0,
            max_duration=150.0,
            std_dev=0.0,
            success_rate=1.0
        )
        
        # Test execution type determination
        from apps.admin_tools.benchmark.views import _determine_execution_type
        execution_type = _determine_execution_type(workflow_run)
        
        # Verify it's correctly identified as a workflow benchmark
        self.assertEqual(execution_type, "Workflow (wheel_generation)")
        
        # Test error handling methods
        self.assertFalse(workflow_run.has_errors())
        self.assertFalse(workflow_run.has_critical_errors())
        
        error_summary = workflow_run.get_error_summary()
        self.assertEqual(error_summary['total_errors'], 0)
        self.assertEqual(error_summary['critical_errors'], 0)
        self.assertFalse(error_summary['has_critical_errors'])

    def test_error_handling_integration(self):
        """Test that errors are properly stored and retrieved in the expected format."""
        # Create a benchmark run with structured errors
        error_data = {
            "performance": {"runs": 1, "success_rate": 0.0},
            "operations": {"tool_calls": {}},
            "errors": [
                {
                    "type": "critical",
                    "level": "error",
                    "message": "Integration test error",
                    "source": "test_integration",
                    "timestamp": "2025-01-31T12:00:00.123Z",
                    "details": {
                        "error_type": "TestError",
                        "traceback": "Test traceback",
                        "context": {"test": True}
                    }
                }
            ],
            "last_output": {"user_response": "Error occurred during processing"}
        }
        
        error_run = BenchmarkRun.objects.create(
            scenario=self.workflow_scenario,
            agent_definition=self.agent_def,
            agent_version="test_integration_error",
            parameters={"workflow_type": "wheel_generation", "runs": 1},
            raw_results=error_data,
            runs_count=1,
            mean_duration=50.0,
            median_duration=50.0,
            min_duration=50.0,
            max_duration=50.0,
            std_dev=0.0,
            success_rate=0.0  # Failed run
        )
        
        # Test error detection
        self.assertTrue(error_run.has_errors())
        self.assertTrue(error_run.has_critical_errors())
        
        # Test error summary
        error_summary = error_run.get_error_summary()
        self.assertEqual(error_summary['total_errors'], 1)
        self.assertEqual(error_summary['critical_errors'], 1)
        self.assertTrue(error_summary['has_critical_errors'])
        
        # Test error retrieval by type
        critical_errors = error_run.get_errors_by_type('critical')
        self.assertEqual(len(critical_errors), 1)
        self.assertEqual(critical_errors[0]['message'], "Integration test error")
        
        # Test execution type is still correct even with errors
        from apps.admin_tools.benchmark.views import _determine_execution_type
        execution_type = _determine_execution_type(error_run)
        self.assertEqual(execution_type, "Workflow (wheel_generation)")

    def test_api_response_structure(self):
        """Test that the API response includes all expected fields."""
        # Create a benchmark run
        benchmark_run = BenchmarkRun.objects.create(
            scenario=self.workflow_scenario,
            agent_definition=self.agent_def,
            agent_version="test_api",
            parameters={"workflow_type": "wheel_generation"},
            raw_results={"test": "data"},
            runs_count=1,
            mean_duration=100.0,
            median_duration=100.0,
            min_duration=100.0,
            max_duration=100.0,
            std_dev=0.0,
            success_rate=1.0
        )
        
        # Simulate the API response structure
        from apps.admin_tools.benchmark.views import _determine_execution_type
        
        response_data = {
            'id': benchmark_run.id,
            'scenario_id': benchmark_run.scenario_id,
            'scenario_name': benchmark_run.scenario.name,
            'agent_role': benchmark_run.agent_definition.role,
            'execution_date': benchmark_run.execution_date.isoformat(),
            'execution_type': _determine_execution_type(benchmark_run),
            'success_rate': benchmark_run.success_rate,
            'semantic_score': benchmark_run.semantic_score,
            'parameters': benchmark_run.parameters,
        }
        
        # Verify all expected fields are present
        expected_fields = [
            'id', 'scenario_id', 'scenario_name', 'agent_role', 'execution_date',
            'execution_type', 'success_rate', 'semantic_score', 'parameters'
        ]
        
        for field in expected_fields:
            self.assertIn(field, response_data, f"Field '{field}' missing from response")
        
        # Verify execution_type is correct
        self.assertEqual(response_data['execution_type'], "Workflow (wheel_generation)")
        
        # Verify parameters are included (this was added as part of the fix)
        self.assertEqual(response_data['parameters'], {"workflow_type": "wheel_generation"})
