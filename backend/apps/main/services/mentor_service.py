import asyncio
import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
import threading
from dataclasses import dataclass, field

from apps.main.agents.mentor_agent import MentorAgent
from apps.main.llm.service import RealLL<PERSON>lient
from apps.main.services.database_service import RealDatabaseService
from apps.main.services.event_service import EventService

logger = logging.getLogger(__name__)


@dataclass
class MentorState:
    """Represents the current state of the Mentor across workflow boundaries."""
    user_profile_id: str
    session_id: Optional[str] = None
    conversation_context: Dict[str, Any] = field(default_factory=dict)
    trust_level: float = 0.5
    communication_preferences: Dict[str, Any] = field(default_factory=dict)
    active_workflow_id: Optional[str] = None
    workflow_history: List[Dict[str, Any]] = field(default_factory=list)
    last_interaction: Optional[datetime] = None
    memory_cache: Dict[str, Any] = field(default_factory=dict)


class MentorService:
    """
    Singleton service that maintains the Mentor's state and handles all user-facing communication.
    
    This service acts as the central hub for Men<PERSON> interactions, ensuring consistency
    across workflow boundaries while maintaining the Mentor's system-level responsibilities.
    """
    
    _instances: Dict[str, 'MentorService'] = {}
    _lock = threading.Lock()
    
    def __new__(cls, user_profile_id: str, session_id: Optional[str] = None):
        """
        Singleton pattern implementation per user profile.
        Each user gets their own MentorService instance.
        """
        with cls._lock:
            key = f"{user_profile_id}_{session_id or 'default'}"
            if key not in cls._instances:
                instance = super().__new__(cls)
                cls._instances[key] = instance
                instance._initialized = False
            return cls._instances[key]
    
    def __init__(self, user_profile_id: str, session_id: Optional[str] = None):
        """Initialize the MentorService if not already initialized."""
        if self._initialized:
            return
            
        self.user_profile_id = user_profile_id
        self.session_id = session_id
        self.state = MentorState(
            user_profile_id=user_profile_id,
            session_id=session_id,
            last_interaction=datetime.now()
        )
        
        # Initialize services
        self.db_service = RealDatabaseService()
        self.llm_client = None
        self._mentor_agent = None
        
        # Initialize LLM client
        try:
            self.llm_client = RealLLMClient()
        except Exception as e:
            logger.warning(f"Failed to initialize LLM client for MentorService: {str(e)}")
        
        self._initialized = True
        logger.info(f"MentorService initialized for user {user_profile_id}")
    
    @classmethod
    def get_instance(cls, user_profile_id: str, session_id: Optional[str] = None) -> 'MentorService':
        """Get or create a MentorService instance for the given user."""
        return cls(user_profile_id, session_id)
    
    @classmethod
    def clear_instance(cls, user_profile_id: str, session_id: Optional[str] = None):
        """Clear a specific MentorService instance (useful for testing)."""
        with cls._lock:
            key = f"{user_profile_id}_{session_id or 'default'}"
            if key in cls._instances:
                del cls._instances[key]
    
    async def process_incoming_message(self, user_message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process an incoming user message before workflow execution.
        
        This method handles the initial user interaction, updates Mentor state,
        and prepares the message for workflow routing.
        
        Args:
            user_message: The user's message content and metadata
            
        Returns:
            dict: Processed message with Mentor context and routing information
        """
        try:
            # Update last interaction time
            self.state.last_interaction = datetime.now()
            
            # Extract message content
            message_text = user_message.get('text', '')
            metadata = user_message.get('metadata', {})
            
            # Update conversation context
            self.state.conversation_context.update({
                'last_message': message_text,
                'last_message_time': self.state.last_interaction.isoformat(),
                'message_metadata': metadata
            })
            
            # Get Mentor's initial assessment
            mentor_assessment = await self._get_mentor_assessment(user_message)
            
            # Prepare enhanced message for workflow routing
            enhanced_message = {
                **user_message,
                'mentor_context': {
                    'trust_level': self.state.trust_level,
                    'communication_preferences': self.state.communication_preferences,
                    'conversation_context': self.state.conversation_context,
                    'mentor_assessment': mentor_assessment
                },
                'mentor_service_id': f"{self.user_profile_id}_{self.session_id or 'default'}"
            }
            
            await self._send_debug_info('info', 'Message processed by MentorService', {
                'message_length': len(message_text),
                'trust_level': self.state.trust_level,
                'has_mentor_assessment': bool(mentor_assessment)
            })
            
            return enhanced_message
            
        except Exception as e:
            logger.error(f"Error processing incoming message in MentorService: {str(e)}", exc_info=True)
            # Return original message if processing fails
            return user_message
    
    async def process_workflow_result(self, workflow_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process workflow results and format them for user delivery.
        
        This method ensures all workflow outputs pass through the Mentor
        for consistent user-facing communication.
        
        Args:
            workflow_result: The result from workflow execution
            
        Returns:
            dict: Formatted result with Mentor's user-facing messages
        """
        try:
            workflow_type = workflow_result.get('workflow_type', 'unknown')
            workflow_id = workflow_result.get('workflow_id')
            
            # Update workflow history
            if workflow_id:
                self.state.workflow_history.append({
                    'workflow_id': workflow_id,
                    'workflow_type': workflow_type,
                    'completed_at': datetime.now().isoformat(),
                    'success': workflow_result.get('status') != 'error'
                })
                
                # Update active workflow
                if self.state.active_workflow_id == workflow_id:
                    self.state.active_workflow_id = None
            
            # Get Mentor's response to the workflow result
            mentor_response = await self._format_workflow_result(workflow_result)
            
            # Enhance the result with Mentor's formatting
            enhanced_result = {
                **workflow_result,
                'mentor_response': mentor_response,
                'mentor_context': {
                    'trust_level': self.state.trust_level,
                    'communication_preferences': self.state.communication_preferences
                }
            }
            
            await self._send_debug_info('info', 'Workflow result processed by MentorService', {
                'workflow_type': workflow_type,
                'workflow_id': workflow_id,
                'has_mentor_response': bool(mentor_response)
            })
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Error processing workflow result in MentorService: {str(e)}", exc_info=True)
            # Return original result if processing fails
            return workflow_result
    
    async def update_workflow_state(self, workflow_id: str, workflow_type: str):
        """Update the active workflow state."""
        self.state.active_workflow_id = workflow_id
        await self._send_debug_info('debug', 'Workflow state updated', {
            'workflow_id': workflow_id,
            'workflow_type': workflow_type
        })
    
    async def _get_mentor_assessment(self, user_message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get Mentor's initial assessment of the user message."""
        # PERFORMANCE FIX: Skip LLM assessment for wheel generation requests to eliminate 4-second delay
        # This assessment is not critical for wheel generation workflow and can be done asynchronously
        message_text = user_message.get('text', '').lower()
        metadata = user_message.get('metadata', {})

        # Check if this is a wheel generation request
        is_wheel_request = (
            'wheel' in message_text or
            'activity' in message_text or
            metadata.get('requested_workflow') == 'wheel_generation' or
            metadata.get('forced_wheel_generation', False)
        )

        if is_wheel_request:
            # Return fast heuristic assessment for wheel requests
            return {
                "emotional_tone": "positive" if any(word in message_text for word in ['excited', 'energetic', 'ready']) else "neutral",
                "urgency_level": "medium",
                "support_needed": "practical",
                "assessment_type": "heuristic_fast",
                "processing_time_ms": 0
            }

        # For non-wheel requests, use LLM assessment if available
        if not self.llm_client:
            return {
                "emotional_tone": "neutral",
                "urgency_level": "medium",
                "support_needed": "informational",
                "assessment_type": "fallback"
            }

        try:
            # Create a simple assessment prompt
            system_prompt = """You are a supportive mentor. Briefly assess this user message for:
            1. Emotional tone (positive, neutral, negative, mixed)
            2. Urgency level (low, medium, high)
            3. Support needed (informational, emotional, practical)

            Respond with a JSON object containing these assessments."""

            response = await self.llm_client.chat_completion(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"User message: {message_text}"}
                ],
                temperature=0.3
            )

            if response.is_text:
                # Try to parse JSON from response
                import json
                try:
                    assessment = json.loads(response.content)
                    assessment["assessment_type"] = "llm_full"
                    return assessment
                except json.JSONDecodeError:
                    # Return basic assessment if JSON parsing fails
                    return {
                        "emotional_tone": "neutral",
                        "urgency_level": "medium",
                        "support_needed": "informational",
                        "assessment_type": "llm_fallback"
                    }

        except Exception as e:
            logger.warning(f"Error getting mentor assessment: {str(e)}")

        return {
            "emotional_tone": "neutral",
            "urgency_level": "medium",
            "support_needed": "informational",
            "assessment_type": "error_fallback"
        }
    
    async def _format_workflow_result(self, workflow_result: Dict[str, Any]) -> Optional[str]:
        """Format workflow results into user-friendly messages."""
        if not self.llm_client:
            return None
            
        try:
            workflow_type = workflow_result.get('workflow_type', 'unknown')
            output_data = workflow_result.get('output_data', {})
            
            # Create a formatting prompt based on workflow type
            system_prompt = f"""You are a supportive mentor. Format the results from a {workflow_type} workflow 
            into a warm, encouraging message for the user. Keep it conversational and supportive."""
            
            user_prompt = f"Workflow results to format: {output_data}"
            
            response = await self.llm_client.chat_completion(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7
            )
            
            if response.is_text:
                return response.content
                
        except Exception as e:
            logger.warning(f"Error formatting workflow result: {str(e)}")
            
        return None
    
    async def _send_debug_info(self, level: str, message: str, details: Optional[Dict[str, Any]] = None):
        """Send debug information via EventService."""
        try:
            await EventService.emit_debug_info(
                level=level,
                message=message,
                source='MentorService',
                details=details,
                user_profile_id=self.user_profile_id,
                session_id=self.session_id
            )
        except Exception as e:
            logger.warning(f"Failed to send debug info: {str(e)}")

    async def get_mentor_agent(self) -> MentorAgent:
        """Get or create a MentorAgent instance for workflow participation."""
        if self._mentor_agent is None:
            try:
                self._mentor_agent = MentorAgent(
                    user_profile_id=self.user_profile_id,
                    llm_client=self.llm_client,
                    db_service=self.db_service
                )
            except Exception as e:
                logger.error(f"Error creating MentorAgent: {str(e)}")
                raise

        return self._mentor_agent

    async def inject_contextual_instructions(self, situation_context: Dict[str, Any], profile_gaps: Dict[str, Any] = None) -> bool:
        """
        Phase 4 Enhancement: Inject contextual instructions based on current situation and user context.

        Provides specific instructions to the Mentor agent based on:
        - Current conversation phase
        - Profile completion status
        - User's immediate needs
        - Workflow context

        Args:
            situation_context: Current situation and conversation context
            profile_gaps: Profile gap analysis results

        Returns:
            bool: True if instructions were successfully injected
        """
        try:
            mentor_agent = await self.get_mentor_agent()
            if not mentor_agent or not hasattr(mentor_agent, 'inject_instructions'):
                logger.warning("MentorAgent does not support instruction injection")
                return False

            # Generate contextual instructions based on situation
            instructions = await self._generate_contextual_instructions(situation_context, profile_gaps)

            # Inject instructions into mentor agent
            mentor_agent.inject_instructions(instructions)

            await self._send_debug_info('info', 'Contextual instructions injected', {
                'situation_type': situation_context.get('type', 'unknown'),
                'instruction_length': len(instructions),
                'has_profile_gaps': bool(profile_gaps)
            })

            return True

        except Exception as e:
            logger.error(f"Error injecting contextual instructions: {str(e)}")
            await self._send_debug_info('error', 'Failed to inject contextual instructions', {
                'error': str(e)
            })
            return False

    async def inject_dynamic_tools(self, workflow_type: str, current_needs: Dict[str, Any]) -> bool:
        """
        Phase 4 Enhancement: Inject dynamic tools based on current workflow and user needs.

        Provides relevant tools to the Mentor agent based on:
        - Current workflow type
        - User's immediate needs
        - Profile completion status
        - Conversation context

        Args:
            workflow_type: Type of workflow currently active
            current_needs: Analysis of user's current needs

        Returns:
            bool: True if tools were successfully injected
        """
        try:
            mentor_agent = await self.get_mentor_agent()
            if not mentor_agent or not hasattr(mentor_agent, 'inject_tools'):
                logger.warning("MentorAgent does not support runtime tool injection")
                return False

            # Generate relevant tools based on workflow and needs
            tools = await self._generate_dynamic_tools(workflow_type, current_needs)

            # Inject tools into mentor agent (MentorAgent uses inject_tools, not inject_runtime_tools)
            mentor_agent.inject_tools(tools)

            await self._send_debug_info('info', 'Dynamic tools injected', {
                'workflow_type': workflow_type,
                'tools_count': len(tools),
                'needs_categories': list(current_needs.keys())
            })

            return True

        except Exception as e:
            logger.error(f"Error injecting dynamic tools: {str(e)}")
            await self._send_debug_info('error', 'Failed to inject dynamic tools', {
                'error': str(e),
                'workflow_type': workflow_type
            })
            return False

    async def update_trust_level(self, new_trust_level: float):
        """Update the user's trust level in the Mentor state."""
        old_trust_level = self.state.trust_level
        self.state.trust_level = max(0.0, min(1.0, new_trust_level))  # Clamp between 0 and 1

        await self._send_debug_info('info', 'Trust level updated', {
            'old_trust_level': old_trust_level,
            'new_trust_level': self.state.trust_level
        })

    async def update_communication_preferences(self, preferences: Dict[str, Any]):
        """Update communication preferences."""
        self.state.communication_preferences.update(preferences)

        await self._send_debug_info('debug', 'Communication preferences updated', {
            'preferences': preferences
        })

    async def get_conversation_context(self) -> Dict[str, Any]:
        """Get the current conversation context."""
        return {
            'conversation_context': self.state.conversation_context,
            'trust_level': self.state.trust_level,
            'communication_preferences': self.state.communication_preferences,
            'active_workflow_id': self.state.active_workflow_id,
            'last_interaction': self.state.last_interaction.isoformat() if self.state.last_interaction else None
        }

    async def store_memory(self, memory_key: str, content: Any, confidence: float = 0.8):
        """Store information in the Mentor's memory cache."""
        self.state.memory_cache[memory_key] = {
            'content': content,
            'confidence': confidence,
            'stored_at': datetime.now().isoformat()
        }

        await self._send_debug_info('debug', 'Memory stored', {
            'memory_key': memory_key,
            'confidence': confidence
        })

    async def retrieve_memory(self, memory_key: str) -> Optional[Any]:
        """Retrieve information from the Mentor's memory cache."""
        memory_item = self.state.memory_cache.get(memory_key)
        if memory_item:
            return memory_item.get('content')
        return None

    def get_state_summary(self) -> Dict[str, Any]:
        """Get a summary of the current Mentor state."""
        return {
            'user_profile_id': self.state.user_profile_id,
            'session_id': self.state.session_id,
            'trust_level': self.state.trust_level,
            'active_workflow_id': self.state.active_workflow_id,
            'workflow_count': len(self.state.workflow_history),
            'last_interaction': self.state.last_interaction.isoformat() if self.state.last_interaction else None,
            'memory_items': len(self.state.memory_cache),
            'has_llm_client': self.llm_client is not None,
            'has_mentor_agent': self._mentor_agent is not None
        }

    async def _generate_contextual_instructions(self, situation_context: Dict[str, Any], profile_gaps: Dict[str, Any] = None) -> str:
        """
        Generate contextual instructions based on current situation and profile gaps.

        Phase 4 Enhancement: Creates specific instructions for different scenarios.
        """
        situation_type = situation_context.get('type', 'general')
        conversation_phase = situation_context.get('phase', 'initial')
        user_needs = situation_context.get('needs', [])

        # Base instruction template
        instructions = f"""
CONTEXTUAL ENHANCEMENT - {situation_type.title()} Mode:
Current Phase: {conversation_phase}
Trust Level: {self.state.trust_level:.2f}

"""

        # Add situation-specific instructions
        if situation_type == 'profile_completion':
            instructions += await self._generate_profile_completion_instructions(profile_gaps)
        elif situation_type == 'wheel_generation':
            instructions += await self._generate_wheel_generation_instructions(situation_context)
        elif situation_type == 'discussion':
            instructions += await self._generate_discussion_instructions(situation_context)
        elif situation_type == 'post_activity':
            instructions += await self._generate_post_activity_instructions(situation_context)
        else:
            instructions += await self._generate_general_instructions(situation_context)

        # Add trust-based communication adjustments
        if self.state.trust_level < 0.3:
            instructions += """
TRUST BUILDING FOCUS:
- Be extra patient and understanding
- Explain your reasoning clearly
- Ask for permission before making suggestions
- Acknowledge any concerns the user might have
"""
        elif self.state.trust_level > 0.8:
            instructions += """
HIGH TRUST INTERACTION:
- You can be more direct and confident in suggestions
- User trusts your judgment, so provide clear recommendations
- Feel free to challenge them constructively when appropriate
"""

        return instructions.strip()

    async def _generate_profile_completion_instructions(self, profile_gaps: Dict[str, Any] = None) -> str:
        """Generate instructions for profile completion scenarios."""
        if not profile_gaps:
            return """
APPROACH:
- Guide the user through profile completion naturally
- Ask one question at a time to avoid overwhelming
- Explain how each piece of information helps provide better recommendations
- Be encouraging and enthusiastic about helping with activities
"""

        critical_gaps = profile_gaps.get('critical', [])
        next_priority = profile_gaps.get('next_priority_field')

        # Handle both string and dict formats for critical gaps
        if critical_gaps:
            if isinstance(critical_gaps[0], dict):
                priority_fields = [gap['field'] for gap in critical_gaps[:3]]
            else:
                priority_fields = critical_gaps[:3]  # Assume they are field names directly
        else:
            priority_fields = ['basic information']

        instructions = f"""
PRIORITY FOCUS: {', '.join(priority_fields)}

APPROACH:
- Be encouraging and enthusiastic about helping with activities
- Explain briefly how each piece of information helps provide better recommendations
- Ask ONE question at a time to avoid overwhelming the user
- Keep questions conversational and friendly

"""

        if next_priority:
            instructions += f"""
NEXT QUESTION FOCUS: {next_priority.get('description', 'user information')}
Context Hint: {next_priority.get('context_hint', '')}
"""

        return instructions

    async def _generate_wheel_generation_instructions(self, situation_context: Dict[str, Any]) -> str:
        """Generate instructions for wheel generation scenarios."""
        return """
APPROACH:
- Be excited and supportive about creating their activity wheel
- Explain what's happening during the generation process
- Set appropriate expectations for timing
- Be ready to explain activity selections and reasoning
- Focus on the value and personalization of their wheel

COMMUNICATION STYLE:
- Enthusiastic and encouraging
- Clear about next steps
- Supportive of their goals and preferences
"""

    async def _generate_discussion_instructions(self, situation_context: Dict[str, Any]) -> str:
        """Generate instructions for discussion scenarios."""
        return """
APPROACH:
- Listen actively and empathetically
- Ask clarifying questions to understand their situation better
- Provide emotional support and validation
- Help them process their thoughts and feelings
- Guide toward constructive solutions when appropriate

COMMUNICATION STYLE:
- Warm and understanding
- Non-judgmental and supportive
- Reflective and thoughtful
- Patient with their process
"""

    async def _generate_post_activity_instructions(self, situation_context: Dict[str, Any]) -> str:
        """Generate instructions for post-activity scenarios."""
        activity_name = situation_context.get('activity_name', 'the activity')

        return f"""
APPROACH:
- Celebrate their completion of {activity_name}
- Ask about their experience and how it felt
- Gather feedback for future recommendations
- Encourage reflection on what they learned
- Support them in planning next steps

COMMUNICATION STYLE:
- Celebratory and proud
- Curious about their experience
- Supportive of their growth
- Forward-looking and encouraging
"""

    async def _generate_general_instructions(self, situation_context: Dict[str, Any]) -> str:
        """Generate general instructions for unspecified scenarios."""
        return """
APPROACH:
- Be warm, supportive, and genuinely helpful
- Listen carefully to understand their needs
- Provide appropriate guidance based on their situation
- Maintain your role as a caring mentor
- Adapt your communication style to their current state

COMMUNICATION STYLE:
- Natural and conversational
- Empathetic and understanding
- Clear and helpful
- Encouraging and positive
"""

    async def _generate_dynamic_tools(self, workflow_type: str, current_needs: Dict[str, Any]) -> List[str]:
        """
        Generate dynamic tools based on workflow type and current needs.

        Phase 4 Enhancement: Provides relevant tools for different scenarios.
        """
        tools = []

        # Base tools always available
        base_tools = [
            'get_user_profile',
            'store_conversation_message',
            'update_current_mood'
        ]
        tools.extend(base_tools)

        # Workflow-specific tools
        if workflow_type == 'onboarding' or workflow_type == 'profile_completion':
            profile_tools = [
                'create_user_demographics',
                'create_user_goal',
                'create_user_preference',
                'extract_demographics_from_text',
                'extract_goals_from_text',
                'extract_preferences_from_text'
            ]
            tools.extend(profile_tools)

        elif workflow_type == 'wheel_generation':
            wheel_tools = [
                'generate_wheel',
                'validate_activities',
                'tailor_activity',
                'query_activity_catalog',
                'assign_wheel_probabilities'
            ]
            tools.extend(wheel_tools)

        elif workflow_type == 'discussion':
            discussion_tools = [
                'analyze_psychological_state',
                'get_trust_metrics',
                'store_conversation_memory',
                'get_conversation_history'
            ]
            tools.extend(discussion_tools)

        elif workflow_type == 'post_spin' or workflow_type == 'post_activity':
            feedback_tools = [
                'record_user_feedback',
                'get_recent_interactions',
                'analyze_trait_gap',
                'identify_growth_opportunities'
            ]
            tools.extend(feedback_tools)

        # Need-based tool additions
        needs_categories = current_needs.keys()

        if 'emotional_support' in needs_categories:
            tools.extend([
                'analyze_psychological_state',
                'get_trust_metrics',
                'identify_growth_opportunities'
            ])

        if 'resource_assessment' in needs_categories:
            tools.extend([
                'get_environment_context',
                'get_available_resources',
                'parse_time_availability'
            ])

        if 'activity_guidance' in needs_categories:
            tools.extend([
                'present_activity_options',
                'explain_activity_selection',
                'format_activity_instructions'
            ])

        # Remove duplicates while preserving order
        unique_tools = []
        seen = set()
        for tool in tools:
            if tool not in seen:
                unique_tools.append(tool)
                seen.add(tool)

        return unique_tools

    async def coordinate_profile_analysis(self, conversation_dispatcher) -> Dict[str, Any]:
        """
        Phase 4 Enhancement: Coordinate with ConversationDispatcher for intelligent profile gap analysis.

        Provides enhanced coordination between MentorService and ConversationDispatcher
        for intelligent profile gap analysis and contextual questioning strategies.

        Args:
            conversation_dispatcher: The ConversationDispatcher instance

        Returns:
            dict: Coordinated analysis results with questioning strategy
        """
        try:
            # Get current profile status from ConversationDispatcher
            profile_status = await conversation_dispatcher._check_profile_completion()
            profile_gaps = await conversation_dispatcher._analyze_profile_gaps()

            # Enhance analysis with Mentor's contextual understanding
            mentor_analysis = await self._analyze_profile_from_mentor_perspective(profile_gaps)

            # Generate coordinated questioning strategy
            questioning_strategy = await self._generate_questioning_strategy(
                profile_gaps,
                mentor_analysis,
                self.state.trust_level
            )

            # Update Mentor state with analysis results
            await self.store_memory('profile_analysis', {
                'completion_status': profile_status,
                'gaps_analysis': profile_gaps,
                'mentor_perspective': mentor_analysis,
                'questioning_strategy': questioning_strategy
            })

            # Coordinate contextual instructions based on analysis
            situation_context = {
                'type': 'profile_completion',
                'phase': 'analysis_coordination',
                'completion_status': profile_status,
                'critical_gaps_count': len(profile_gaps.get('critical', []))
            }

            await self.inject_contextual_instructions(situation_context, profile_gaps)

            return {
                'profile_status': profile_status,
                'profile_gaps': profile_gaps,
                'mentor_analysis': mentor_analysis,
                'questioning_strategy': questioning_strategy,
                'coordination_success': True
            }

        except Exception as e:
            logger.error(f"Error coordinating profile analysis: {str(e)}")
            await self._send_debug_info('error', 'Profile analysis coordination failed', {
                'error': str(e)
            })
            return {
                'coordination_success': False,
                'error': str(e)
            }

    async def _analyze_profile_from_mentor_perspective(self, profile_gaps: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze profile gaps from Mentor's perspective with trust and communication context."""
        try:
            critical_gaps = profile_gaps.get('critical', [])
            completion_percentage = profile_gaps.get('completion_percentage', 0.0)

            # Mentor's contextual analysis
            analysis = {
                'trust_adjusted_priority': [],
                'communication_approach': 'standard',
                'estimated_questions_needed': len(critical_gaps),
                'user_readiness_assessment': 'ready'
            }

            # Adjust priorities based on trust level
            if self.state.trust_level < 0.4:
                # Low trust - prioritize less invasive questions
                analysis['communication_approach'] = 'gentle_gradual'
                analysis['estimated_questions_needed'] = min(2, len(critical_gaps))
                analysis['user_readiness_assessment'] = 'needs_trust_building'

                # Prioritize basic, non-personal information first
                for gap in critical_gaps:
                    if gap['field'] in ['resources', 'time_availability']:
                        analysis['trust_adjusted_priority'].append(gap)

            elif self.state.trust_level > 0.7:
                # High trust - can ask more comprehensive questions
                analysis['communication_approach'] = 'direct_comprehensive'
                analysis['estimated_questions_needed'] = len(critical_gaps)
                analysis['user_readiness_assessment'] = 'ready_for_comprehensive'
                analysis['trust_adjusted_priority'] = critical_gaps

            else:
                # Medium trust - standard approach
                analysis['trust_adjusted_priority'] = critical_gaps[:3]  # Limit to top 3

            return analysis

        except Exception as e:
            logger.warning(f"Error in mentor perspective analysis: {str(e)}")
            return {
                'trust_adjusted_priority': [],
                'communication_approach': 'standard',
                'estimated_questions_needed': 1,
                'user_readiness_assessment': 'unknown',
                'error': str(e)
            }

    async def _generate_questioning_strategy(self, profile_gaps: Dict[str, Any], mentor_analysis: Dict[str, Any], trust_level: float) -> Dict[str, Any]:
        """Generate intelligent questioning strategy based on gaps and trust level."""
        try:
            strategy = {
                'approach': mentor_analysis.get('communication_approach', 'standard'),
                'question_sequence': [],
                'pacing': 'one_at_a_time',
                'encouragement_level': 'medium'
            }

            # Adjust strategy based on trust level
            if trust_level < 0.4:
                strategy.update({
                    'pacing': 'very_gradual',
                    'encouragement_level': 'high',
                    'explanation_detail': 'comprehensive',
                    'permission_seeking': True
                })
            elif trust_level > 0.7:
                strategy.update({
                    'pacing': 'efficient',
                    'encouragement_level': 'medium',
                    'explanation_detail': 'concise',
                    'permission_seeking': False
                })

            # Generate question sequence from trust-adjusted priorities
            priority_gaps = mentor_analysis.get('trust_adjusted_priority', [])
            for gap in priority_gaps[:3]:  # Limit to top 3 questions
                question_info = {
                    'field': gap['field'],
                    'question': gap.get('question', f"Tell me about your {gap['field']}"),
                    'context_hint': gap.get('context_hint', ''),
                    'priority_weight': gap.get('priority_weight', 5)
                }
                strategy['question_sequence'].append(question_info)

            return strategy

        except Exception as e:
            logger.warning(f"Error generating questioning strategy: {str(e)}")
            return {
                'approach': 'standard',
                'question_sequence': [],
                'pacing': 'one_at_a_time',
                'encouragement_level': 'medium',
                'error': str(e)
            }

    async def apply_runtime_enhancements(self, conversation_state: Dict[str, Any], user_profile_gaps: Dict[str, Any]) -> Dict[str, Any]:
        """
        Phase 4 Enhancement: Apply runtime instruction and tool enhancement based on conversation state and user profile gaps.

        This method coordinates all runtime enhancements:
        1. Contextual instruction injection
        2. Dynamic tool injection
        3. Communication style adaptation
        4. Trust-based interaction adjustments

        Args:
            conversation_state: Current conversation state and context
            user_profile_gaps: Profile gap analysis results

        Returns:
            dict: Enhancement results and status
        """
        try:
            enhancement_results = {
                'instructions_injected': False,
                'tools_injected': False,
                'communication_adapted': False,
                'trust_adjustments_applied': False,
                'enhancements_applied': []
            }

            # Extract context information
            current_phase = conversation_state.get('phase', 'initial')
            awaiting_response = conversation_state.get('awaiting_response_type')
            workflow_context = conversation_state.get('workflow_context', {})

            # 1. Apply contextual instructions
            situation_context = {
                'type': self._determine_situation_type(current_phase, awaiting_response),
                'phase': current_phase,
                'awaiting_response': awaiting_response,
                'workflow_context': workflow_context
            }

            if await self.inject_contextual_instructions(situation_context, user_profile_gaps):
                enhancement_results['instructions_injected'] = True
                enhancement_results['enhancements_applied'].append('contextual_instructions')

            # 2. Apply dynamic tools
            workflow_type = workflow_context.get('type', 'general')
            current_needs = self._analyze_current_needs(conversation_state, user_profile_gaps)

            if await self.inject_dynamic_tools(workflow_type, current_needs):
                enhancement_results['tools_injected'] = True
                enhancement_results['enhancements_applied'].append('dynamic_tools')

            # 3. Adapt communication style
            if await self._adapt_communication_style(conversation_state):
                enhancement_results['communication_adapted'] = True
                enhancement_results['enhancements_applied'].append('communication_adaptation')

            # 4. Apply trust-based adjustments
            if await self._apply_trust_adjustments(user_profile_gaps):
                enhancement_results['trust_adjustments_applied'] = True
                enhancement_results['enhancements_applied'].append('trust_adjustments')

            # Update state with enhancement results
            self.state.conversation_context.update({
                'last_enhancement': datetime.now().isoformat(),
                'enhancement_results': enhancement_results
            })

            await self._send_debug_info('info', 'Runtime enhancements applied', {
                'enhancements_count': len(enhancement_results['enhancements_applied']),
                'enhancements': enhancement_results['enhancements_applied'],
                'situation_type': situation_context['type']
            })

            return enhancement_results

        except Exception as e:
            logger.error(f"Error applying runtime enhancements: {str(e)}")
            await self._send_debug_info('error', 'Runtime enhancement failed', {
                'error': str(e)
            })
            return {
                'instructions_injected': False,
                'tools_injected': False,
                'communication_adapted': False,
                'trust_adjustments_applied': False,
                'enhancements_applied': [],
                'error': str(e)
            }

    def _determine_situation_type(self, current_phase: str, awaiting_response: str = None) -> str:
        """Determine the current situation type for contextual enhancement."""
        if awaiting_response == 'profile_info' or current_phase == 'awaiting_profile_info':
            return 'profile_completion'
        elif current_phase == 'wheel_generation' or awaiting_response == 'wheel_generation':
            return 'wheel_generation'
        elif awaiting_response == 'situation_info':
            return 'discussion'
        elif awaiting_response == 'activity_selection' or current_phase == 'post_activity':
            return 'post_activity'
        else:
            return 'general'

    def _analyze_current_needs(self, conversation_state: Dict[str, Any], profile_gaps: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze current user needs based on conversation state and profile gaps."""
        needs = {}

        # Analyze based on conversation phase
        current_phase = conversation_state.get('phase', 'initial')
        awaiting_response = conversation_state.get('awaiting_response_type')

        if awaiting_response == 'profile_info':
            needs['information_gathering'] = {
                'priority': 'high',
                'focus': 'profile_completion'
            }

        if current_phase == 'wheel_generation':
            needs['activity_guidance'] = {
                'priority': 'high',
                'focus': 'wheel_creation'
            }

        # Analyze based on profile gaps
        critical_gaps = profile_gaps.get('critical', [])
        if critical_gaps:
            needs['profile_enhancement'] = {
                'priority': 'medium',
                'gaps_count': len(critical_gaps)
            }

        # Analyze based on trust level
        if self.state.trust_level < 0.4:
            needs['trust_building'] = {
                'priority': 'high',
                'current_level': self.state.trust_level
            }

        # Check for emotional support needs
        conversation_context = conversation_state.get('context', {})
        if conversation_context.get('emotional_indicators'):
            needs['emotional_support'] = {
                'priority': 'high',
                'indicators': conversation_context['emotional_indicators']
            }

        return needs

    async def _adapt_communication_style(self, conversation_state: Dict[str, Any]) -> bool:
        """Adapt communication style based on conversation state."""
        try:
            mentor_agent = await self.get_mentor_agent()
            if not mentor_agent:
                return False

            # Determine communication adaptations needed
            adaptations = []

            current_phase = conversation_state.get('phase', 'initial')
            context = conversation_state.get('context', {})

            # Trust-based adaptations
            if self.state.trust_level < 0.3:
                adaptations.append('extra_gentle_approach')
            elif self.state.trust_level > 0.8:
                adaptations.append('confident_direct_approach')

            # Phase-based adaptations
            if current_phase == 'awaiting_profile_info':
                adaptations.append('encouraging_information_gathering')
            elif current_phase == 'wheel_generation':
                adaptations.append('excited_activity_focus')

            # Apply adaptations if any are needed
            if adaptations and hasattr(mentor_agent, 'adapt_communication_style'):
                mentor_agent.adapt_communication_style(adaptations)
                return True

            return False

        except Exception as e:
            logger.warning(f"Error adapting communication style: {str(e)}")
            return False

    async def _apply_trust_adjustments(self, profile_gaps: Dict[str, Any]) -> bool:
        """Apply trust-based interaction adjustments."""
        try:
            # Update communication preferences based on trust level
            if self.state.trust_level < 0.4:
                self.state.communication_preferences.update({
                    'explanation_detail': 'comprehensive',
                    'permission_seeking': True,
                    'encouragement_frequency': 'high',
                    'question_pacing': 'very_gradual'
                })
            elif self.state.trust_level > 0.7:
                self.state.communication_preferences.update({
                    'explanation_detail': 'concise',
                    'permission_seeking': False,
                    'encouragement_frequency': 'medium',
                    'question_pacing': 'efficient'
                })
            else:
                self.state.communication_preferences.update({
                    'explanation_detail': 'balanced',
                    'permission_seeking': False,
                    'encouragement_frequency': 'medium',
                    'question_pacing': 'standard'
                })

            return True

        except Exception as e:
            logger.warning(f"Error applying trust adjustments: {str(e)}")
            return False
