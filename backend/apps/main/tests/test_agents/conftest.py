import pytest
import pytest_asyncio
import os
from unittest.mock import MagicMock, AsyncMock # Added for potential use within this file if needed
from decimal import <PERSON>ima<PERSON>
from typing import TYPE_CHECKING

# Use TYPE_CHECKING for type hints if needed elsewhere, but avoid direct model imports here
if TYPE_CHECKING:
    from apps.main.models import LLMConfig
    from apps.main.testing.agent_test_runner import Agent<PERSON><PERSON><PERSON><PERSON>ner
    from apps.main.testing.mock_database_service import MockDatabaseService
    from apps.main.testing.definition_extractors import AgentDefinitionsExtractor, ToolDefinitionsExtractor

# Helper functions for API key validation
def is_real_api_key(api_key: str) -> bool:
    """
    Check if an API key is a real API key or a dummy test key.

    Args:
        api_key: The API key to check

    Returns:
        bool: True if it's a real API key, False if it's a dummy test key
    """
    if not api_key:
        return False

    # List of patterns that indicate dummy/test keys
    dummy_patterns = [
        'test_',
        'dummy_',
        'fake_',
        'mock_',
        '_for_testing',
        'testing_only'
    ]

    api_key_lower = api_key.lower()
    return not any(pattern in api_key_lower for pattern in dummy_patterns)


def get_real_api_key() -> str:
    """
    Get a real API key from environment variables, excluding dummy test keys.

    Returns:
        str: Real API key if available, None otherwise
    """
    mistral_key = os.environ.get("MISTRAL_API_KEY")
    openai_key = os.environ.get("OPENAI_API_KEY")

    if mistral_key and is_real_api_key(mistral_key):
        return mistral_key
    if openai_key and is_real_api_key(openai_key):
        return openai_key

    return None


# Agent Test Environment Setup - Moved here from parent conftest.py
# Note: Imports from .test_env are moved into the fixture to avoid import-time conflicts

# Disable autouse again to prevent interference with non-agent tests
# @pytest.fixture(autouse=True)
@pytest.fixture(scope="function") # Needs explicit request by agent tests, function scope for isolation
def agent_test_environment_fixture(monkeypatch, request): # Added monkeypatch and request fixtures
    """Sets up the mock environment using monkeypatch for safe isolation.

    This fixture is ONLY available to tests in the test_agents directory
    and ONLY applies to tests explicitly marked with @pytest.mark.agent_test.
    """
    import sys # Import sys here for monkeypatch usage

    # ULTRA-STRICT: Only apply agent test environment to tests that explicitly request it
    # This fixture should NEVER be applied automatically
    test_file_path = str(request.fspath)
    test_name = request.node.name

    # ULTRA-STRICT: Check if this is explicitly an agent test
    # ALL conditions must be true for this to be considered an agent test
    is_in_agent_test_dir = 'test_agents' in test_file_path
    has_agent_test_marker = (
        hasattr(request.node, 'get_closest_marker') and
        request.node.get_closest_marker('agent_test') is not None
    )

    # BOTH conditions must be true
    is_agent_test = is_in_agent_test_dir and has_agent_test_marker

    # Log for debugging
    import logging
    logger = logging.getLogger(__name__)
    logger.debug(f"agent_test_environment_fixture called for {test_file_path}::{test_name}, is_agent_test={is_agent_test}")

    # FAIL FAST: If this fixture is called from outside test_agents directory, raise an error
    if not is_in_agent_test_dir:
        raise RuntimeError(f"agent_test_environment_fixture should only be used in test_agents directory, called from: {test_file_path}")

    if not is_agent_test:
        # For non-agent tests, just yield without applying any mocks
        logger.debug(f"Skipping agent test environment for non-agent test: {test_file_path}::{test_name}")
        yield
        return

    try:
        # Import setup and reset functions inside the fixture
        from .test_env import setup_agent_test_env, create_app_models, reset_mock_models

        # Prepare the core Django mocks
        core_mocks_to_apply = setup_agent_test_env() # This now returns core mocks

        # Prepare the app model mocks
        app_model_mocks_to_apply = create_app_models() # This now returns app model mocks

        # Combine the dictionaries
        all_mocks_to_apply = {**core_mocks_to_apply, **app_model_mocks_to_apply}

        # Apply all mocks using monkeypatch
        for module_name, mock_object in all_mocks_to_apply.items():
            # Ensure the mock object is actually created before patching
            if mock_object is None:
                 print(f"Warning: Mock object for {module_name} is None, skipping patch.")
                 continue
            # Check if module exists before patching, create dummy if not? Or let setitem handle?
            # monkeypatch.setitem should handle creating the entry if it doesn't exist.
            monkeypatch.setitem(sys.modules, module_name, mock_object)

    except ImportError as e:
        print(f"Error setting up agent test environment (ImportError): {e}")
        # Define a dummy reset function if setup failed
        def reset_mock_models(): pass

    yield # Run the test

    try:
        # Reset only the mock models state. Monkeypatch handles sys.modules restoration.
        # Import reset function inside the teardown part
        from .test_env import reset_mock_models
        reset_mock_models()
    except ImportError as e:
        print(f"Warning: Could not import reset_mock_models from .test_env: {e}")
        pass
    # Removed call to cleanup_agent_test_env as monkeypatch handles it

# --- Existing Fixtures ---
# --- REMOVED TOP-LEVEL IMPORTS ---
# from apps.main.testing.agent_test_runner import AgentTestRunner
# from apps.main.testing.mock_database_service import MockDatabaseService
# from apps.main.models import LLMConfig
# --- END REMOVED TOP-LEVEL IMPORTS ---


class TestLLMConfig:
    """A simple class that mimics the LLMConfig interface for testing."""
    def __init__(self, name, model_name, temperature=0.7,
                 input_token_price=Decimal("0.01"), output_token_price=Decimal("0.02"),
                 is_default=False, is_evaluation=False):
        self.name = name
        self.model_name = model_name
        self.temperature = temperature
        self.input_token_price = input_token_price
        self.output_token_price = output_token_price
        self.is_default = is_default
        self.is_evaluation = is_evaluation

@pytest.fixture
def llm_config(request):
    """
    Fixture providing an LLMConfig object for tests.

    If the test is marked with @pytest.mark.llm, returns a TestLLMConfig with a real model name.
    Otherwise, returns a mock LLMConfig.
    """
    # Check if the test is marked with llm
    is_real_llm_test = request.node.get_closest_marker("llm") is not None

    if is_real_llm_test:
        # Create a TestLLMConfig object with a real model name
        real_llm_config = TestLLMConfig(
            name="test_real_llm",
            model_name="mistral-small-latest",  # Use a real model name
            temperature=0.5,
            input_token_price=Decimal("0.01"),
            output_token_price=Decimal("0.02"),
            is_default=True,
            is_evaluation=False
        )

        return real_llm_config
    else:
        # Create a TestLLMConfig with a mock name but real string for model_name
        # Using TestLLMConfig instead of MagicMock to ensure proper attribute types
        mock_llm_config = TestLLMConfig(
            name="test_mock_llm",
            model_name="mistral-small-latest",  # Use a real string for model_name
            temperature=0.5,
            input_token_price=Decimal("0.01"),
            output_token_price=Decimal("0.02"),
            is_default=False,
            is_evaluation=False
        )

        return mock_llm_config

@pytest_asyncio.fixture(scope="function")
async def llm_config_async():
    """Async fixture providing a mock LLMConfig object for tests."""
    # Create a mock LLMConfig with the necessary attributes
    mock_llm_config = MagicMock()
    mock_llm_config.name = "test_mock_llm"
    mock_llm_config.model_name = "mock-model-v1"
    mock_llm_config.temperature = 0.5
    mock_llm_config.input_token_price = Decimal("0.01")
    mock_llm_config.output_token_price = Decimal("0.02")
    mock_llm_config.is_evaluation = False

    return mock_llm_config


@pytest.fixture
def agent_runner():
    """Factory fixture for creating agent test runners.

    This fixture supports both class-based and string-based agent references:
    - Class-based: agent_runner(MentorAgent)
    - String-based: agent_runner("MentorAgent") or agent_runner("mentor")

    Using string-based references is preferred to avoid AppRegistryNotReady errors
    caused by importing Django models at module level.

    For agents that import Django models at module level, this fixture will use
    mock implementations from apps.main.testing.mock_agents to avoid AppRegistryNotReady errors.
    """
    # --- DEFER IMPORT HERE ---
    import importlib
    import logging
    from apps.main.testing.mock_agents import MockErrorHandlerAgent
    from apps.main.testing.mock_agent_test_runner import MockAgentTestRunner
    # --- END DEFER IMPORT ---

    logger = logging.getLogger(__name__)

    def _create_runner(agent_class_or_name, use_real_llm=False):
        # Special case for ErrorHandlerAgent - use the mock implementation
        if agent_class_or_name == "ErrorHandlerAgent" or agent_class_or_name == "error_handler":
            logger.debug("Using MockErrorHandlerAgent to avoid AppRegistryNotReady errors")
            return MockAgentTestRunner(MockErrorHandlerAgent, use_real_llm=use_real_llm)

        # If agent_class_or_name is a string, try to import the class dynamically
        if isinstance(agent_class_or_name, str):
            # Map of common agent role names to their class paths
            agent_class_paths = {
                # Full class names
                'MentorAgent': 'apps.main.agents.mentor_agent.MentorAgent',
                'OrchestratorAgent': 'apps.main.agents.orchestrator_agent.OrchestratorAgent',
                'ResourceAgent': 'apps.main.agents.resource_agent.ResourceAgent',
                'EngagementAndPatternAgent': 'apps.main.agents.engagement_agent.EngagementAndPatternAgent',
                'PsychologicalMonitoringAgent': 'apps.main.agents.psy_agent.PsychologicalMonitoringAgent',
                'StrategyAgent': 'apps.main.agents.strategy_agent.StrategyAgent',
                'WheelAndActivityAgent': 'apps.main.agents.wheel_activity_agent.WheelAndActivityAgent',
                'EthicalAgent': 'apps.main.agents.ethical_agent.EthicalAgent',
                'DispatcherAgent': 'apps.main.agents.dispatcher_agent.DispatcherAgent',
                # ErrorHandlerAgent is handled separately above

                # Role names (lowercase)
                'mentor': 'apps.main.agents.mentor_agent.MentorAgent',
                'orchestrator': 'apps.main.agents.orchestrator_agent.OrchestratorAgent',
                'resource': 'apps.main.agents.resource_agent.ResourceAgent',
                'engagement': 'apps.main.agents.engagement_agent.EngagementAndPatternAgent',
                'psychological': 'apps.main.agents.psy_agent.PsychologicalMonitoringAgent',
                'strategy': 'apps.main.agents.strategy_agent.StrategyAgent',
                'wheel_activity': 'apps.main.agents.wheel_activity_agent.WheelAndActivityAgent',
                'ethical': 'apps.main.agents.ethical_agent.EthicalAgent',
                'dispatcher': 'apps.main.agents.dispatcher_agent.DispatcherAgent',
                # error_handler is handled separately above
            }

            # Get the class path from the mapping or construct it
            if agent_class_or_name in agent_class_paths:
                class_path = agent_class_paths[agent_class_or_name]
            else:
                # Try to construct a class path from the name
                # Assume format like 'agent_name' -> 'apps.main.agents.agent_name_agent.AgentNameAgent'
                agent_name = agent_class_or_name.lower().replace('agent', '')

                # Special case for PsychologicalMonitoringAgent
                if agent_class_or_name == "PsychologicalMonitoringAgent":
                    module_name = "apps.main.agents.psy_agent"
                    class_name = "PsychologicalMonitoringAgent"
                else:
                    module_name = f"apps.main.agents.{agent_name}_agent"
                    class_name = f"{agent_name.capitalize()}Agent"

                class_path = f"{module_name}.{class_name}"
                logger.debug(f"Constructed class path {class_path} from agent name {agent_class_or_name}")

            try:
                # Split the class path into module path and class name
                module_path, class_name = class_path.rsplit('.', 1)

                # Import the module
                module = importlib.import_module(module_path)

                # Get the class from the module
                agent_class = getattr(module, class_name)
                logger.debug(f"Successfully imported {class_name} from {module_path}")
            except (ImportError, AttributeError) as e:
                logger.error(f"Failed to import agent class from {class_path}: {str(e)}")
                raise ValueError(f"Could not import agent class from {class_path}: {str(e)}")
        else:
            # If agent_class_or_name is already a class, use it directly
            agent_class = agent_class_or_name

        # Create and return the MockAgentTestRunner with the resolved class
        # Always use MockAgentTestRunner to avoid AppRegistryNotReady errors
        return MockAgentTestRunner(agent_class, use_real_llm=use_real_llm)

    return _create_runner

@pytest.fixture
def agent_assert():
    """Provide agent-specific assertions"""
    class AgentAssertions:
        @staticmethod
        def assert_contains_context_packet(output_data):
            """Assert that output data contains a valid context packet"""
            assert "context_packet" in output_data, "Output missing context_packet"
            assert isinstance(output_data["context_packet"], dict), "context_packet is not a dictionary"

        @staticmethod
        def assert_has_next_agent(output_data, expected_agent=None):
            """Assert that output data has a next agent specification"""
            routing_key = "forwardTo" if "forwardTo" in output_data else "next_agent"
            assert routing_key in output_data, f"Output missing routing key ({routing_key})"

            if expected_agent:
                assert output_data[routing_key] == expected_agent, \
                    f"Expected next agent to be '{expected_agent}', got '{output_data[routing_key]}'"

        @staticmethod
        def assert_contains_mood(context, expected_terms=None):
            """Assert that context includes mood with expected terms"""
            assert "mood" in context, "Context missing : 'mood'"

            if expected_terms:
                mood_text = str(context["mood"]).lower()
                for term in expected_terms:
                    assert term.lower() in mood_text, f"Expected mood to contain '{term}'"

        @staticmethod
        def assert_structure_contains(data, expected_structure):
            """
            Assert that a data structure contains all the expected keys and values.

            Args:
                data: The data structure to check
                expected_structure: The expected structure to check against
            """
            assert isinstance(data, dict), f"Data is not a dictionary: {data}"
            assert isinstance(expected_structure, dict), f"Expected structure is not a dictionary: {expected_structure}"

            for key, expected_value in expected_structure.items():
                assert key in data, f"Missing key '{key}' in data"

                if isinstance(expected_value, dict) and isinstance(data[key], dict):
                    # Recursively check nested dictionaries
                    AgentAssertions.assert_structure_contains(data[key], expected_value)
                elif isinstance(expected_value, list) and isinstance(data[key], list):
                    # Check that all expected items are in the list
                    for item in expected_value:
                        assert item in data[key], f"Expected item '{item}' not found in list {data[key]}"
                else:
                    # Direct comparison for other types
                    assert data[key] == expected_value, f"Value mismatch for key '{key}': expected '{expected_value}', got '{data[key]}'"

    return AgentAssertions()

@pytest.fixture
def agent_runner_with_extracted_definitions(llm_config): # Add llm_config fixture dependency
    """
    Factory fixture for creating agent test runners with extracted definitions.
    Accepts an optional llm_config object to pass to the agent.
    """
    # --- DEFER IMPORTS HERE ---
    from apps.main.testing.agent_test_runner import AgentTestRunner
    from apps.main.testing.mock_database_service import MockDatabaseService
    from apps.main.testing.definition_extractors import AgentDefinitionsExtractor, ToolDefinitionsExtractor
    # --- END DEFER IMPORTS ---

    def _create_runner(agent_class, use_real_llm=False, agent_llm_config=None): # Add agent_llm_config param
        # Use the provided llm_config if available, otherwise default (e.g., from fixture)
        config_to_use = agent_llm_config if agent_llm_config is not None else llm_config

        # Create runner, passing the llm_config
        runner = AgentTestRunner(
            agent_class,
            use_real_llm=use_real_llm,
            llm_config=config_to_use # Pass llm_config to runner
        )

        # Force load extracted definitions
        agent_extractor = AgentDefinitionsExtractor()
        agent_definitions = agent_extractor.extract_definitions()
        agent_tool_mappings, common_tools = agent_extractor.extract_tool_mappings()

        tool_extractor = ToolDefinitionsExtractor()
        tool_definitions = tool_extractor.extract_definitions()

        # Ensure db_service is created and has definitions loaded
        if not runner.db_service:
            runner.db_service = MockDatabaseService(use_extracted_definitions=True)

        # Force load definitions
        runner.db_service.agent_definitions = agent_definitions
        runner.db_service.agent_tool_mappings = agent_tool_mappings
        runner.db_service.common_tools = common_tools
        runner.db_service.tool_definitions = tool_definitions
        runner.db_service.tool_responses = tool_extractor.tool_mock_responses

        return runner
    return _create_runner
