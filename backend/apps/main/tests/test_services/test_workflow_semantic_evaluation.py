"""
Tests for the semantic evaluation functionality in AsyncWorkflowManager.
"""

import json
import pytest
import uuid
from typing import Optional
from unittest.mock import AsyncMock, MagicMock, patch

from apps.main.models import BenchmarkScenario
from apps.main.services.async_workflow_manager import WorkflowBenchmarker, BenchmarkResult
from apps.main.services.semantic_evaluator import SemanticEvaluator
from apps.main.llm.response import LLMResponse, ResponseType


class TestWorkflowBenchmarker(WorkflowBenchmarker):
    """Test implementation of AsyncWorkflowManager."""

    async def _run_workflow_benchmark(self, scenario, workflow_type, mock_tools, runs=3, warmup_runs=1, progress_callback=None, use_real_llm: bool = False, use_real_tools: bool = False, use_real_db: bool = False, user_profile_id: Optional[str] = None):
        """Test implementation that returns a simple result."""
        return BenchmarkResult(
            workflow_type=workflow_type,
            scenario_name=scenario.name,
            mean_duration=1.0,
            median_duration=1.0,
            min_duration=0.9,
            max_duration=1.1,
            std_dev=0.1,
            success_rate=1.0,
            tool_call_counts={"test_tool": 5},
            total_input_tokens=100,
            total_output_tokens=50,
            last_output_data={"response": "Test response"}
        )


@pytest.fixture
def workflow_manager():
    """Fixture to provide a TestWorkflowManager instance."""
    return TestWorkflowBenchmarker()


@pytest.fixture
def mock_scenario():
    """Fixture to provide a mock BenchmarkScenario."""
    scenario = MagicMock(spec=BenchmarkScenario)
    scenario.id = uuid.uuid4()
    scenario.name = "Test Scenario"
    scenario.description = "Test Description"
    scenario.metadata = {
        "workflow_type": "test_workflow",
        "expected_quality_criteria": {
            "Clarity": ["Is the response clear?", "Is it well-structured?"],
            "Helpfulness": ["Does the response provide useful information?"]
        },
        "evaluator_models": ["test-model"]
    }
    scenario.input_data = {"query": "Test query"}
    return scenario


@pytest.mark.asyncio
async def test_evaluate_semantic_quality(workflow_manager, mock_scenario):
    """Test the _evaluate_semantic_quality method."""
    # Create a mock result
    result = BenchmarkResult(
        workflow_type="test_workflow",
        scenario_name=mock_scenario.name,
        last_output_data={"response": "Test response"}
    )

    # Create a mock semantic evaluator
    mock_evaluator = AsyncMock(spec=SemanticEvaluator)
    mock_evaluator.evaluate_response.return_value = {
        "test-model": {
            "overall_score": 0.8,
            "overall_reasoning": "Good response",
            "dimensions": {
                "Clarity": {"score": 0.85, "reasoning": "Clear"},
                "Helpfulness": {"score": 0.75, "reasoning": "Helpful"}
            }
        },
        "_meta": {
            "primary_model": "test-model",
            "models_used": ["test-model"],
            "errors": []
        }
    }

    # Replace the semantic evaluator in the workflow manager
    workflow_manager.semantic_evaluator = mock_evaluator

    # Call the method
    await workflow_manager._evaluate_semantic_quality(
        scenario=mock_scenario,
        result=result,
        user_profile_id="test_user"
    )

    # Verify the semantic evaluator was called correctly
    mock_evaluator.evaluate_response.assert_called_once()
    args, kwargs = mock_evaluator.evaluate_response.call_args
    assert kwargs["scenario_context"] == workflow_manager._get_scenario_context(mock_scenario)
    assert kwargs["agent_response"] == "Test response"
    assert kwargs["criteria"] == mock_scenario.metadata["expected_quality_criteria"]
    assert kwargs["evaluator_models"] == mock_scenario.metadata["evaluator_models"]
    assert kwargs["user_profile_id"] == "test_user"

    # Verify the result was updated
    assert result.semantic_score == 0.8
    assert result.semantic_evaluation_details == "Good response"
    assert result.semantic_evaluations == mock_evaluator.evaluate_response.return_value


@pytest.mark.asyncio
async def test_evaluate_semantic_quality_no_response(workflow_manager, mock_scenario):
    """Test _evaluate_semantic_quality with no response."""
    # Create a result with no last_output_data
    result = BenchmarkResult(
        workflow_type="test_workflow",
        scenario_name=mock_scenario.name
    )

    # Create a mock semantic evaluator
    mock_evaluator = AsyncMock(spec=SemanticEvaluator)

    # Replace the semantic evaluator in the workflow manager
    workflow_manager.semantic_evaluator = mock_evaluator

    # Call the method
    await workflow_manager._evaluate_semantic_quality(
        scenario=mock_scenario,
        result=result
    )

    # Verify the semantic evaluator was not called
    mock_evaluator.evaluate_response.assert_not_called()


@pytest.mark.asyncio
async def test_evaluate_semantic_quality_no_criteria(workflow_manager, mock_scenario):
    """Test _evaluate_semantic_quality with no evaluation criteria."""
    # Create a result with last_output_data
    result = BenchmarkResult(
        workflow_type="test_workflow",
        scenario_name=mock_scenario.name,
        last_output_data={"response": "Test response"}
    )

    # Remove evaluation criteria from the scenario
    mock_scenario.metadata = {"workflow_type": "test_workflow"}

    # Create a mock semantic evaluator
    mock_evaluator = AsyncMock(spec=SemanticEvaluator)

    # Replace the semantic evaluator in the workflow manager
    workflow_manager.semantic_evaluator = mock_evaluator

    # Call the method
    await workflow_manager._evaluate_semantic_quality(
        scenario=mock_scenario,
        result=result
    )

    # Verify the semantic evaluator was not called
    mock_evaluator.evaluate_response.assert_not_called()


@pytest.mark.asyncio
async def test_evaluate_semantic_quality_error_handling(workflow_manager, mock_scenario):
    """Test error handling in _evaluate_semantic_quality."""
    # Create a result with last_output_data
    result = BenchmarkResult(
        workflow_type="test_workflow",
        scenario_name=mock_scenario.name,
        last_output_data={"response": "Test response"}
    )

    # Create a mock semantic evaluator that raises an exception
    mock_evaluator = AsyncMock(spec=SemanticEvaluator)
    mock_evaluator.evaluate_response.side_effect = Exception("Test error")

    # Replace the semantic evaluator in the workflow manager
    workflow_manager.semantic_evaluator = mock_evaluator

    # Mock the EventService.emit_debug_info method
    with patch('apps.main.services.async_workflow_manager.EventService.emit_debug_info', new_callable=AsyncMock) as mock_emit:
        # Call the method
        await workflow_manager._evaluate_semantic_quality(
            scenario=mock_scenario,
            result=result,
            user_profile_id="test_user"
        )

        # Verify the error was handled
        assert len(result.errors) == 1
        # Verify the error message contains the expected substring
        assert "Test error" in result.errors[0]
        # Optionally, verify the type of the stored error if needed, but checking the message content is often sufficient

        # Verify the error was reported
        mock_emit.assert_called_once()
        args, kwargs = mock_emit.call_args
        # Verify the emitted error message contains the expected substring
        # The error is in the 'details' parameter, not 'data'
        assert "Test error" in kwargs["details"]["error"]
        assert kwargs["source"] == "WorkflowBenchmarker"
        assert kwargs["user_profile_id"] == "test_user"


@pytest.mark.asyncio
async def test_execute_benchmark_with_semantic_evaluation(workflow_manager, mock_scenario):
    """Test execute_benchmark with semantic evaluation."""
    # Mock the _get_scenario_async method
    workflow_manager._get_scenario_async = AsyncMock(return_value=mock_scenario)

    # Mock the _prepare_mock_tools method
    workflow_manager._prepare_mock_tools = AsyncMock(return_value=MagicMock())

    # Mock the _evaluate_semantic_quality method
    workflow_manager._evaluate_semantic_quality = AsyncMock()

    # Mock the _store_results method
    mock_benchmark_run = MagicMock()
    workflow_manager._store_results = AsyncMock(return_value=mock_benchmark_run)

    # Mock the progress_callback
    progress_callback = MagicMock()

    # Call execute_benchmark with semantic_evaluation=True
    result = await workflow_manager.execute_benchmark(
        scenario_id=mock_scenario.id,
        params={"semantic_evaluation": True},
        progress_callback=progress_callback,
        user_profile_id="test_user"
    )

    # Verify _evaluate_semantic_quality was called
    workflow_manager._evaluate_semantic_quality.assert_called_once()

    # Verify progress_callback was called with semantic evaluation status
    progress_callback.assert_any_call(
        state='PROGRESS',
        meta={
            'current': 70,
            'total': 100,
            'status': f"Performing semantic evaluation for: {mock_scenario.name}"
        }
    )

    # Verify the result
    assert result == mock_benchmark_run


@pytest.mark.asyncio
async def test_execute_benchmark_without_semantic_evaluation(workflow_manager, mock_scenario):
    """Test execute_benchmark without semantic evaluation."""
    # Mock the _get_scenario_async method
    workflow_manager._get_scenario_async = AsyncMock(return_value=mock_scenario)

    # Mock the _prepare_mock_tools method
    workflow_manager._prepare_mock_tools = AsyncMock(return_value=MagicMock())

    # Mock the _evaluate_semantic_quality method
    workflow_manager._evaluate_semantic_quality = AsyncMock()

    # Mock the _store_results method
    mock_benchmark_run = MagicMock()
    workflow_manager._store_results = AsyncMock(return_value=mock_benchmark_run)

    # Mock the progress_callback
    progress_callback = MagicMock()

    # Call execute_benchmark with semantic_evaluation=False
    result = await workflow_manager.execute_benchmark(
        scenario_id=mock_scenario.id,
        params={"semantic_evaluation": False},
        progress_callback=progress_callback,
        user_profile_id="test_user"
    )

    # Verify _evaluate_semantic_quality was not called
    workflow_manager._evaluate_semantic_quality.assert_not_called()

    # Verify progress_callback was called with skipped semantic evaluation status
    progress_callback.assert_any_call(
        state='PROGRESS',
        meta={
            'current': 80,
            'total': 100,
            'status': f"Skipped semantic evaluation for: {mock_scenario.name}"
        }
    )

    # Verify the result
    assert result == mock_benchmark_run
