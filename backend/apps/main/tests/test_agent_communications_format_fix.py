"""
Test for the agent communications standardized extraction in workflow benchmarks.

This test verifies that the new standardized agent communications extraction
methods can handle multiple data formats and locations without errors.
"""

from django.test import TestCase

from apps.main.services.async_workflow_manager import WorkflowBenchmarker
from apps.main.models import BenchmarkScenario, GenericAgent


class TestAgentCommunicationsStandardizedExtraction(TestCase):
    """Test the agent communications format handling fix."""

    def setUp(self):
        """Set up test data."""
        self.manager = WorkflowBenchmarker()
        
        # Create a test scenario
        self.scenario = BenchmarkScenario.objects.create(
            name="test_scenario",
            description="Test scenario for agent communications format fix",
            agent_role="workflow",
            input_data={"test_input": "test_value"},
            metadata={"workflow_type": "wheel_generation"}
        )
        
        # Create a test agent definition
        self.agent_def = GenericAgent.objects.create(
            role="workflow",
            system_instructions="Test agent for workflow benchmarks",
            input_schema={"type": "object", "properties": {}},
            output_schema={"type": "object", "properties": {}}
        )

    def test_agent_communications_dict_format(self):
        """Test handling of dictionary format agent communications."""
        # Dictionary format (from AgentCommunicationTracker.export_data)
        raw_results = {
            'agent_communications': {
                'enabled': True,
                'workflow_id': 'test_workflow_123',
                'agents': [
                    {
                        'agent': 'orchestrator',
                        'stage': 'planning',
                        'input': {'user_input': 'test'},
                        'output': {'plan': 'test plan'},
                        'timestamp': '2024-01-01T00:00:00',
                        'duration_ms': 100.0,
                        'success': True
                    }
                ],
                'state_transitions': [],
                'summary': {'total_communications': 1}
            }
        }
        
        # This should not raise an AttributeError
        benchmark_run = self.manager._create_benchmark_run_sync(
            scenario=self.scenario,
            agent_definition=self.agent_def,
            raw_results=raw_results,
            parameters={'runs': 1}
        )
        
        # Verify the benchmark run was created successfully
        self.assertIsNotNone(benchmark_run)
        self.assertEqual(benchmark_run.scenario, self.scenario)
        
        # Verify agent communications were stored correctly
        stored_comms = benchmark_run.agent_communications
        self.assertIsInstance(stored_comms, dict)
        self.assertIn('agents', stored_comms)
        self.assertEqual(len(stored_comms['agents']), 1)

    def test_agent_communications_list_format(self):
        """Test handling of list format agent communications."""
        # List format (direct list of agent communications)
        raw_results = {
            'agent_communications': [
                {
                    'agent': 'orchestrator',
                    'stage': 'planning',
                    'input': {'user_input': 'test'},
                    'output': {'plan': 'test plan'},
                    'timestamp': '2024-01-01T00:00:00',
                    'duration_ms': 100.0,
                    'success': True
                },
                {
                    'agent': 'mentor',
                    'stage': 'review',
                    'input': {'plan': 'test plan'},
                    'output': {'feedback': 'good plan'},
                    'timestamp': '2024-01-01T00:01:00',
                    'duration_ms': 50.0,
                    'success': True
                }
            ]
        }
        
        # This should not raise an AttributeError
        benchmark_run = self.manager._create_benchmark_run_sync(
            scenario=self.scenario,
            agent_definition=self.agent_def,
            raw_results=raw_results,
            parameters={'runs': 1}
        )
        
        # Verify the benchmark run was created successfully
        self.assertIsNotNone(benchmark_run)
        self.assertEqual(benchmark_run.scenario, self.scenario)
        
        # Verify agent communications were normalized to dict format
        stored_comms = benchmark_run.agent_communications
        self.assertIsInstance(stored_comms, dict)
        self.assertIn('agents', stored_comms)
        self.assertEqual(len(stored_comms['agents']), 2)

    def test_agent_communications_nested_in_last_output(self):
        """Test handling of agent communications nested in last_output."""
        raw_results = {
            'last_output': {
                'agent_communications': [
                    {
                        'agent': 'orchestrator',
                        'stage': 'execution',
                        'input': {'task': 'test'},
                        'output': {'result': 'completed'},
                        'timestamp': '2024-01-01T00:00:00',
                        'duration_ms': 200.0,
                        'success': True
                    }
                ]
            }
        }
        
        # This should not raise an AttributeError
        benchmark_run = self.manager._create_benchmark_run_sync(
            scenario=self.scenario,
            agent_definition=self.agent_def,
            raw_results=raw_results,
            parameters={'runs': 1}
        )
        
        # Verify the benchmark run was created successfully
        self.assertIsNotNone(benchmark_run)
        
        # Verify agent communications were normalized to dict format
        stored_comms = benchmark_run.agent_communications
        self.assertIsInstance(stored_comms, dict)
        self.assertIn('agents', stored_comms)
        self.assertEqual(len(stored_comms['agents']), 1)

    def test_agent_communications_invalid_format(self):
        """Test handling of invalid format agent communications."""
        raw_results = {
            'agent_communications': 'invalid_string_format'
        }
        
        # This should not raise an AttributeError, should default to empty dict
        benchmark_run = self.manager._create_benchmark_run_sync(
            scenario=self.scenario,
            agent_definition=self.agent_def,
            raw_results=raw_results,
            parameters={'runs': 1}
        )
        
        # Verify the benchmark run was created successfully
        self.assertIsNotNone(benchmark_run)
        
        # Verify agent communications defaulted to empty dict
        stored_comms = benchmark_run.agent_communications
        self.assertEqual(stored_comms, {})

    def test_no_agent_communications(self):
        """Test handling when no agent communications are present."""
        raw_results = {
            'other_data': 'some_value'
        }
        
        # This should not raise an AttributeError
        benchmark_run = self.manager._create_benchmark_run_sync(
            scenario=self.scenario,
            agent_definition=self.agent_def,
            raw_results=raw_results,
            parameters={'runs': 1}
        )
        
        # Verify the benchmark run was created successfully
        self.assertIsNotNone(benchmark_run)
        
        # Verify agent communications defaulted to empty dict
        stored_comms = benchmark_run.agent_communications
        self.assertEqual(stored_comms, {})

    def test_extract_agent_communications_method(self):
        """Test the new _extract_agent_communications method directly."""
        # Test with AgentCommunicationTracker.export_data() format
        raw_results_dict_format = {
            'agent_communications': {
                'enabled': True,
                'workflow_id': 'test_workflow_123',
                'agents': [
                    {
                        'agent': 'orchestrator',
                        'stage': 'planning',
                        'input': {'user_input': 'test'},
                        'output': {'plan': 'test plan'},
                        'timestamp': '2024-01-01T00:00:00',
                        'duration_ms': 100.0,
                        'success': True
                    }
                ],
                'state_transitions': [],
                'summary': {'total_communications': 1}
            }
        }

        result = self.manager._extract_agent_communications(raw_results_dict_format)
        self.assertIsInstance(result, dict)
        self.assertIn('agents', result)
        self.assertEqual(len(result['agents']), 1)
        self.assertEqual(result['workflow_id'], 'test_workflow_123')

    def test_normalize_agent_communications_format_method(self):
        """Test the new _normalize_agent_communications_format method directly."""
        # Test dict format (already normalized)
        dict_data = {
            'enabled': True,
            'workflow_id': 'test_workflow',
            'agents': [{'agent': 'test', 'stage': 'test'}],
            'state_transitions': []
        }
        result = self.manager._normalize_agent_communications_format(dict_data)
        self.assertEqual(result, dict_data)

        # Test list format (needs normalization)
        list_data = [
            {'agent': 'orchestrator', 'stage': 'planning'},
            {'agent': 'mentor', 'stage': 'review'}
        ]
        result = self.manager._normalize_agent_communications_format(list_data)
        self.assertIsInstance(result, dict)
        self.assertIn('agents', result)
        self.assertEqual(result['agents'], list_data)
        self.assertEqual(len(result['agents']), 2)

        # Test invalid format
        result = self.manager._normalize_agent_communications_format("invalid")
        self.assertEqual(result, {})

    def test_benchmark_result_from_dict_with_list_last_output(self):
        """Test BenchmarkResult.from_dict when last_output is a list (reproduces the original error)."""
        from apps.main.services.async_workflow_manager import BenchmarkResult

        # This reproduces the exact scenario that was causing the AttributeError
        data_with_list_last_output = {
            'workflow_type': 'wheel_generation',
            'scenario': 'test_scenario',
            'performance': {
                'mean_duration_s': 1.0,
                'median_duration_s': 1.0,
                'min_duration_s': 1.0,
                'max_duration_s': 1.0,
                'std_dev': 0.0,
                'success_rate': 1.0,
            },
            'operations': {
                'tool_calls': {},
                'total_input_tokens': 100,
                'total_output_tokens': 50,
            },
            'last_output': [  # This is a list, not a dict!
                {
                    'agent': 'orchestrator',
                    'stage': 'planning',
                    'input': {'user_input': 'test'},
                    'output': {'plan': 'test plan'},
                    'timestamp': '2024-01-01T00:00:00',
                    'duration_ms': 100.0,
                    'success': True
                }
            ]
        }

        # This should not raise an AttributeError anymore
        result = BenchmarkResult.from_dict(data_with_list_last_output)

        # Verify the result was created successfully
        self.assertIsInstance(result, BenchmarkResult)
        self.assertEqual(result.workflow_type, 'wheel_generation')
        self.assertEqual(result.scenario_name, 'test_scenario')

        # Verify last_output_data is an empty dict (since the list was filtered out)
        self.assertEqual(result.last_output_data, {})

    def test_create_benchmark_run_with_list_last_output_data(self):
        """Test _create_benchmark_run_sync with list in last_output_data (reproduces the original error)."""
        # This reproduces the exact raw_results structure that was causing the error
        raw_results_with_list = {
            'last_output_data': [  # This is a list, not a dict!
                {
                    'agent': 'orchestrator',
                    'stage': 'planning',
                    'input': {'user_input': 'test'},
                    'output': {'plan': 'test plan'},
                    'timestamp': '2024-01-01T00:00:00',
                    'duration_ms': 100.0,
                    'success': True
                }
            ]
        }

        # This should not raise an AttributeError anymore
        benchmark_run = self.manager._create_benchmark_run_sync(
            scenario=self.scenario,
            agent_definition=self.agent_def,
            raw_results=raw_results_with_list,
            parameters={'runs': 1}
        )

        # Verify the benchmark run was created successfully
        self.assertIsNotNone(benchmark_run)
        self.assertEqual(benchmark_run.scenario, self.scenario)

        # Verify agent communications defaulted to empty dict
        stored_comms = benchmark_run.agent_communications
        self.assertEqual(stored_comms, {})
